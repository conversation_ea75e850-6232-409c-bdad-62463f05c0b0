/* HealthCampaignHistory - Clean & Modern Design */
.health-campaign-history {
  padding: 25px;
  background: linear-gradient(135deg, #f8fcff 0%, #e3f2fd 100%);
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* Page Header */
.health-campaign-page-header {
  background: linear-gradient(135deg, #00b4d8 0%, #0077b6 50%, #023e8a 100%);
  color: white;
  padding: 40px 35px;
  border-radius: 20px;
  margin-bottom: 30px;
  box-shadow: 0 8px 32px rgba(0, 180, 216, 0.25);
  position: relative;
  overflow: hidden;
}

.health-campaign-page-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200px;
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: rotate(45deg);
}

.health-campaign-header-content h1 {
  color: #ffffff;
  margin: 0 0 15px 0;
  font-size: 2.2rem;
  font-weight: 700;
  letter-spacing: -0.02em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.health-campaign-header-content p {
  margin: 0;
  font-size: 1.2rem;
  opacity: 0.95;
  font-weight: 400;
}

/* Statistics Section */
.statistics-section {
  margin-bottom: 30px;
}

.health-campaign-history .stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)) !important;
  gap: 16px !important;
}

.stat-card {
  background: white;
  padding: 20px 16px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 180, 216, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-width: 0;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--stat-color, #00b4d8);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.stat-card.total { --stat-color: #3b82f6; }
.stat-card.preparing { --stat-color: #f59e0b; }
.stat-card.ongoing { --stat-color: #10b981; }
.stat-card.completed { --stat-color: #059669; }
.stat-card.cancelled { --stat-color: #ef4444; }

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
  background: var(--stat-color, #00b4d8);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 1.4rem;
  font-weight: 700;
  color: #1f2937;
  display: block;
  margin-bottom: 3px;
  line-height: 1.2;
}

.stat-label {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
  line-height: 1.3;
}

/* Controls Section */
.controls-section {
  background: white;
  padding: 25px 30px;
  border-radius: 16px;
  margin-bottom: 30px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 180, 216, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.search-group {
  flex: 1;
  min-width: 300px;
}

.search-box {
  position: relative;
}

.search-box input {
  width: 100%;
  padding: 14px 20px 14px 50px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #ffffff;
}

.search-box input:focus {
  outline: none;
  border-color: #00b4d8;
  box-shadow: 0 0 0 3px rgba(0, 180, 216, 0.1);
}

.search-icon {
  position: absolute;
  left: 18px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  font-size: 1.1rem;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 15px;
}

.status-filter {
  padding: 14px 18px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 160px;
}

.status-filter:focus {
  outline: none;
  border-color: #00b4d8;
  box-shadow: 0 0 0 3px rgba(0, 180, 216, 0.1);
}

.refresh-btn {
  padding: 14px 20px;
  background: linear-gradient(135deg, #00b4d8, #0077b6);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background: linear-gradient(135deg, #0077b6, #023e8a);
  transform: translateY(-1px);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Table Container */
.table-container {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 180, 216, 0.1);
}

.campaigns-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.95rem;
}

.campaigns-table th {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 20px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.campaigns-table td {
  padding: 20px;
  border-bottom: 1px solid #f3f4f6;
  vertical-align: middle;
}

.table-row {
  cursor: pointer;
  transition: all 0.2s ease;
}

.table-row:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.title-cell {
  font-weight: 600;
  color: #1f2937;
  max-width: 300px;
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.2s ease;
}

.status-badge.clickable {
  cursor: pointer;
  user-select: none;
}

.status-badge.clickable:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.status-badge.preparing {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fbbf24;
}

.status-badge.preparing.clickable:hover {
  background: #fde68a;
  border-color: #f59e0b;
}

.status-badge.ongoing {
  background: #dcfdf7;
  color: #047857;
  border: 1px solid #10b981;
}

.status-badge.ongoing.clickable:hover {
  background: #a7f3d0;
  border-color: #059669;
}

.status-badge.completed {
  background: #d1fae5;
  color: #065f46;
  border: 1px solid #059669;
}

.status-badge.completed.clickable:hover {
  background: #a7f3d0;
  border-color: #047857;
}

.status-badge.cancelled {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #ef4444;
}

.status-badge.cancelled.clickable:hover {
  background: #fecaca;
  border-color: #dc2626;
}

/* Status Dropdown */
.status-dropdown-container {
  position: relative;
  display: inline-block;
}

.status-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 160px;
  overflow: hidden;
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.status-dropdown-item {
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f3f4f6;
}

.status-dropdown-item:last-child {
  border-bottom: none;
}

.status-dropdown-item:hover {
  background: #f8fafc;
  transform: translateX(2px);
}

.status-dropdown-item .status-badge {
  margin: 0;
  font-size: 0.75rem;
  padding: 4px 10px;
}

/* Admin History Toolbar */
.admin-history-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  gap: 24px;
  flex-wrap: wrap;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 24px 28px;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.admin-search-filter-group {
  display: flex;
  gap: 20px;
  flex: 1;
  min-width: 320px;
}

.admin-search-box {
  position: relative;
  flex: 2;
  max-width: 420px;
}

.admin-search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #3b82f6;
  font-size: 1.1rem;
  z-index: 1;
}

.admin-search-box input {
  width: 100%;
  padding: 14px 20px 14px 44px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  background: #ffffff;
  color: #1e293b;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-search-box input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.admin-search-box input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.admin-filter-dropdown {
  position: relative;
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 200px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-filter-dropdown:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.admin-filter-icon {
  position: absolute;
  left: 16px;
  color: #3b82f6;
  font-size: 1.1rem;
  z-index: 1;
}

.admin-filter-dropdown select {
  padding: 14px 20px 14px 44px;
  border: none;
  border-radius: 12px;
  background: transparent;
  font-size: 0.875rem;
  min-width: 200px;
  cursor: pointer;
  color: #1e293b;
  font-weight: 600;
}

.admin-filter-dropdown select:focus {
  outline: none;
}

.admin-toolbar-buttons {
  display: flex;
  gap: 16px;
  align-items: center;
}

.admin-sort-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 14px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  background: #ffffff;
  color: #64748b;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  font-size: 0.875rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-sort-button:hover {
  background: #f1f5f9;
  border-color: #3b82f6;
  color: #3b82f6;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.admin-refresh-button {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 14px 24px;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  font-size: 0.875rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.admin-refresh-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
}

.admin-refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 12px;
}

.action-btn {
  padding: 12px 14px;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 44px;
  min-height: 44px;
}

.action-btn svg {
  width: 18px;
  height: 18px;
  transition: transform 0.2s ease;
}

.action-btn:hover svg {
  transform: scale(1.1);
}

.action-btn.view {
  background: #e0f2fe;
  color: #0369a1;
  border: 1px solid #0ea5e9;
}

.action-btn.view:hover {
  background: #bae6fd;
  transform: translateY(-1px);
}

.action-btn.edit {
  background: #dbeafe;
  color: #1d4ed8;
  border: 1px solid #3b82f6;
}

.action-btn.edit:hover {
  background: #bfdbfe;
  transform: translateY(-1px);
}

.action-btn.delete {
  background: #fee2e2;
  color: #dc2626;
  border: 1px solid #ef4444;
}

.action-btn.delete:hover {
  background: #fecaca;
  transform: translateY(-1px);
}

.action-btn.notify {
  background: #f0f9ff;
  color: #0369a1;
  border: 1px solid #0ea5e9;
  padding: 8px 10px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.notify:hover {
  background: #e0f2fe;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-btn.notify:active {
  transform: translateY(0);
}

/* Loading, Error, Empty States */
.loading-state,
.error-state,
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.loading-spinner {
  display: inline-block;
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #00b4d8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

.error-state {
  color: #dc2626;
}

.error-state svg {
  font-size: 48px;
  margin-bottom: 15px;
}

.empty-state svg {
  font-size: 64px;
  color: #9ca3af;
  margin-bottom: 15px;
}

.empty-state h3,
.error-state h3 {
  margin: 0 0 10px 0;
  font-size: 1.3rem;
  color: #374151;
}

.retry-btn {
  margin-top: 15px;
  padding: 10px 20px;
  background: #00b4d8;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.retry-btn:hover {
  background: #0077b6;
}

/* Health Campaign History Modal Styles - Namespaced to prevent conflicts */
.health-campaign-history .hch-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.65);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 20px;
  backdrop-filter: blur(2px);
}

.health-campaign-history .hch-modal-content {
  background: white;
  border-radius: 20px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: hchModalSlideIn 0.3s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 25px 80px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  backdrop-filter: blur(10px);
}

@keyframes hchModalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.health-campaign-history .hch-modal-header {
  padding: 25px 30px;
  border-bottom: 2px solid #f1f5f9;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 20px 20px 0 0;
  position: relative;
}

.health-campaign-history .hch-modal-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0,0,0,0.1), transparent);
}

.health-campaign-history .hch-modal-header h2 {
  margin: 0;
  font-size: 1.6rem;
  font-weight: 700;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 12px;
}

.health-campaign-history .hch-close-btn {
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid #e2e8f0;
  font-size: 1.1rem;
  cursor: pointer;
  color: #64748b;
  padding: 10px;
  border-radius: 12px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.health-campaign-history .hch-close-btn:hover {
  background: #fee2e2;
  border-color: #ef4444;
  color: #dc2626;
  transform: scale(1.05);
}

.health-campaign-history .hch-modal-body {
  padding: 30px;
  background: #ffffff;
}

.health-campaign-history .hch-modal-footer {
  padding: 20px 30px;
  border-top: 2px solid #f1f5f9;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 0 0 20px 20px;
}

/* Health Campaign History Button Styles */
.health-campaign-history .hch-btn-primary {
  background: linear-gradient(135deg, #0ea5e9, #0284c7);
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.25);
}

.health-campaign-history .hch-btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #0284c7, #0369a1);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(14, 165, 233, 0.4);
}

.health-campaign-history .hch-btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.health-campaign-history .hch-btn-secondary {
  background: #f8fafc;
  color: #475569;
  border: 2px solid #cbd5e1;
  padding: 12px 26px;
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
}

.health-campaign-history .hch-btn-secondary:hover {
  background: #e2e8f0;
  border-color: #94a3b8;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.health-campaign-history .hch-btn-danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.25);
}

.health-campaign-history .hch-btn-danger:hover:not(:disabled) {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

.health-campaign-history .hch-btn-danger:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Health Campaign History Detail Modal Styles */
.health-campaign-history .hch-detail-modal {
  max-width: 700px;
}

.health-campaign-history .hch-detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 25px;
}

.health-campaign-history .hch-detail-item {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 18px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.health-campaign-history .hch-detail-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
}

.health-campaign-history .hch-detail-item.full-width {
  grid-column: 1 / -1;
}

.health-campaign-history .hch-detail-item label {
  display: block;
  font-weight: 700;
  color: #334155;
  margin-bottom: 8px;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.health-campaign-history .hch-detail-item span,
.health-campaign-history .hch-detail-item p {
  color: #475569;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0;
}

.health-campaign-history .hch-checkup-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.health-campaign-history .hch-checkup-item {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1e40af;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid #93c5fd;
}

/* Health Campaign History Delete Modal Styles */
.health-campaign-history .hch-delete-modal {
  max-width: 500px;
}

.health-campaign-history .hch-delete-modal .hch-modal-body {
  text-align: center;
  padding: 40px 30px;
}

.health-campaign-history .hch-delete-info {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  padding: 20px;
  border-radius: 12px;
  margin: 20px 0;
  border: 2px solid #f59e0b;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);
}

.health-campaign-history .hch-delete-info strong {
  color: #92400e;
  font-size: 1.1rem;
  display: block;
  margin-bottom: 8px;
}

.health-campaign-history .hch-warning {
  color: #dc2626;
  font-weight: 700;
  margin: 20px 0 0 0;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* Health Campaign History Status Modal Styles */
.health-campaign-history .hch-status-modal {
  max-width: 550px;
}

.health-campaign-history .hch-status-modal .hch-modal-header {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  text-align: center;
}

.health-campaign-history .hch-status-modal .hch-modal-header h2 {
  color: white;
}

.health-campaign-history .hch-status-modal .hch-close-btn {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
}

.health-campaign-history .hch-status-modal .hch-close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.health-campaign-history .hch-status-info {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 25px;
  border-radius: 15px;
  margin: 25px 0 30px 0;
  border: 2px solid #cbd5e1;
  text-align: center;
}

.health-campaign-history .hch-status-info strong {
  display: block;
  font-size: 1.2rem;
  color: #1e293b;
  margin-bottom: 12px;
}

.health-campaign-history .hch-status-options {
  display: grid;
  gap: 12px;
  margin-top: 25px;
}

.health-campaign-history .hch-status-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18px 24px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
}

.health-campaign-history .hch-status-option:hover:not(:disabled):not(.current) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.health-campaign-history .hch-status-option.preparing {
  border-color: #f59e0b;
}

.health-campaign-history .hch-status-option.preparing:hover:not(:disabled):not(.current) {
  background: #fef3c7;
  border-color: #d97706;
}

.health-campaign-history .hch-status-option.ongoing {
  border-color: #10b981;
}

.health-campaign-history .hch-status-option.ongoing:hover:not(:disabled):not(.current) {
  background: #d1fae5;
  border-color: #059669;
}

.health-campaign-history .hch-status-option.completed {
  border-color: #059669;
}

.health-campaign-history .hch-status-option.completed:hover:not(:disabled):not(.current) {
  background: #d1fae5;
  border-color: #047857;
}

.health-campaign-history .hch-status-option.cancelled {
  border-color: #ef4444;
}

.health-campaign-history .hch-status-option.cancelled:hover:not(:disabled):not(.current) {
  background: #fee2e2;
  border-color: #dc2626;
}

.health-campaign-history .hch-status-option.current {
  background: linear-gradient(135deg, #64748b 0%, #475569 100%);
  color: white;
  cursor: not-allowed;
  border-color: #64748b;
}

.health-campaign-history .hch-current-indicator {
  font-size: 0.9rem;
  font-weight: 500;
  opacity: 0.9;
  font-style: italic;
  color: rgba(255, 255, 255, 0.8);
}

/* Health Campaign History Edit Modal Styles */
.health-campaign-history .hch-edit-modal {
  max-width: 800px;
}

.health-campaign-history .hch-form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 25px;
}

.health-campaign-history .hch-form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.health-campaign-history .hch-form-group.full-width {
  grid-column: 1 / -1;
}

.health-campaign-history .hch-form-group label {
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.health-campaign-history .hch-form-group input,
.health-campaign-history .hch-form-group textarea,
.health-campaign-history .hch-form-group select {
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.health-campaign-history .hch-form-group input:focus,
.health-campaign-history .hch-form-group textarea:focus,
.health-campaign-history .hch-form-group select:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

/* Health Campaign History - Additional Modal Enhancements */
.health-campaign-history .hch-modal-content {
  box-shadow: 
    0 25px 80px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  backdrop-filter: blur(10px);
}

.health-campaign-history .hch-modal-header {
  position: relative;
}

.health-campaign-history .hch-modal-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0,0,0,0.1), transparent);
}

/* Improved form styling */
.health-campaign-history .hch-form-group input:focus,
.health-campaign-history .hch-form-group textarea:focus,
.health-campaign-history .hch-form-group select:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

/* Better button hover effects */
.health-campaign-history .hch-btn-primary:hover:not(:disabled) {
  box-shadow: 0 8px 25px rgba(14, 165, 233, 0.4);
}

.health-campaign-history .hch-btn-danger:hover:not(:disabled) {
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

/* Enhanced status badges in modals */
.health-campaign-history .hch-detail-item .status-badge,
.health-campaign-history .hch-status-info .status-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 2px solid transparent;
}

/* Modal overlay better backdrop */
.health-campaign-history .hch-modal-overlay {
  backdrop-filter: blur(4px);
  background: rgba(0, 0, 0, 0.65);
}

/* Focus management for accessibility */
.health-campaign-history .hch-modal-content:focus {
  outline: none;
}

.health-campaign-history .hch-close-btn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Prevent body scroll when modal is open */
.health-campaign-history .modal-open {
  overflow: hidden;
}

/* Special Checkup Items Section in Edit Modal */
.health-campaign-history .hch-current-edit-items {
  margin-bottom: 15px;
}

.health-campaign-history .hch-edit-items-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin: 0;
}

.health-campaign-history .hch-edit-item-tag {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1e40af;
  padding: 8px 14px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid #93c5fd;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
  transition: all 0.3s ease;
  animation: fadeInScale 0.3s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.health-campaign-history .hch-edit-item-tag:hover {
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
  background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 100%);
}

.health-campaign-history .hch-edit-item-tag span {
  user-select: none;
}

.health-campaign-history .hch-remove-edit-item {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid #fca5a5;
  color: #dc2626;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.65rem;
  flex-shrink: 0;
}

.health-campaign-history .hch-remove-edit-item:hover {
  background: #ef4444;
  color: white;
  border-color: #dc2626;
  transform: scale(1.1);
}

.health-campaign-history .hch-add-edit-item-section {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border: 1px solid #bbf7d0;
  border-radius: 12px;
  padding: 15px;
  margin-top: 15px;
}

.health-campaign-history .hch-edit-input-with-button {
  display: flex;
  gap: 12px;
  align-items: stretch;
}

.health-campaign-history .hch-edit-input-with-button input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #d1d5db;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.health-campaign-history .hch-edit-input-with-button input:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
  transform: translateY(-1px);
}

.health-campaign-history .hch-edit-input-with-button input::placeholder {
  color: #9ca3af;
  font-style: italic;
}

.health-campaign-history .hch-add-edit-button {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  padding: 12px 18px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 48px;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
  font-size: 0.95rem;
}

.health-campaign-history .hch-add-edit-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.health-campaign-history .hch-add-edit-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
}

.health-campaign-history .hch-add-edit-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Helper text for empty state */
.health-campaign-history .hch-form-group.full-width .hch-empty-checkup-hint {
  color: #6b7280;
  font-size: 0.9rem;
  font-style: italic;
  margin-bottom: 15px;
  padding: 10px 15px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px dashed #d1d5db;
  text-align: center;
}

/* Modal Chọn Lớp Health Campaign - New Namespace */
.modalchonjlop_hc-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.65);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 20px;
  backdrop-filter: blur(4px);
  animation: modalchonjlop_hc-fadeIn 0.3s ease-out;
}

@keyframes modalchonjlop_hc-fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modalchonjlop_hc-content {
  background: white;
  border-radius: 20px;
  box-shadow:
    0 25px 80px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalchonjlop_hc-slideIn 0.3s ease-out;
  backdrop-filter: blur(10px);
}

@keyframes modalchonjlop_hc-slideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modalchonjlop_hc-header {
  padding: 25px 30px;
  border-bottom: 2px solid #f1f5f9;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 20px 20px 0 0;
  position: relative;
}

.modalchonjlop_hc-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0,0,0,0.1), transparent);
}

.modalchonjlop_hc-header h2 {
  margin: 0;
  font-size: 1.6rem;
  font-weight: 700;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 12px;
}

.modalchonjlop_hc-close-btn {
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid #e2e8f0;
  font-size: 1.1rem;
  cursor: pointer;
  color: #64748b;
  border-radius: 10px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.modalchonjlop_hc-close-btn:hover {
  background: #fee2e2;
  border-color: #ef4444;
  color: #dc2626;
  transform: scale(1.05);
}

.modalchonjlop_hc-body {
  padding: 30px;
  background: #ffffff;
}

.modalchonjlop_hc-notification-info {
  margin-bottom: 25px;
}

.modalchonjlop_hc-notification-info h3 {
  color: #1e293b;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0 0 10px 0;
}

.modalchonjlop_hc-notification-info p {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
}

.modalchonjlop_hc-loading-students {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #64748b;
  gap: 15px;
}

.modalchonjlop_hc-loading-students .spinning {
  font-size: 2rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.modalchonjlop_hc-grade-selection {
  margin-top: 20px;
}

.modalchonjlop_hc-grade-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 25px;
}

.modalchonjlop_hc-grade-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
}

.modalchonjlop_hc-grade-option:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.modalchonjlop_hc-grade-option.all-grades {
  border-color: #10b981;
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

.modalchonjlop_hc-grade-option.all-grades:hover {
  border-color: #059669;
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
}

.modalchonjlop_hc-grade-option.selected {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
}

.modalchonjlop_hc-grade-option input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin-right: 12px;
  cursor: pointer;
  accent-color: #3b82f6;
}

.modalchonjlop_hc-grade-label {
  font-weight: 600;
  color: #1e293b;
  flex: 1;
  margin-left: 8px;
}

.modalchonjlop_hc-student-count {
  color: #64748b;
  font-size: 0.9rem;
  font-weight: 500;
  background: rgba(100, 116, 139, 0.1);
  padding: 4px 10px;
  border-radius: 15px;
}

.modalchonjlop_hc-selected-summary {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 2px solid #0ea5e9;
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
}

.modalchonjlop_hc-selected-summary h4 {
  color: #0c4a6e;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 10px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modalchonjlop_hc-selected-summary h4::before {
  content: '📊';
  font-size: 1.2rem;
}

.modalchonjlop_hc-selected-summary p {
  color: #0c4a6e;
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
}

.modalchonjlop_hc-selected-summary strong {
  color: #0369a1;
  font-weight: 700;
}

.modalchonjlop_hc-modal-footer {
  padding: 20px 30px;
  border-top: 2px solid #f1f5f9;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 0 0 20px 20px;
}

.modalchonjlop_hc-btn-primary {
  background: linear-gradient(135deg, #0ea5e9, #0284c7);
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.25);
}

.modalchonjlop_hc-btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #0284c7, #0369a1);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(14, 165, 233, 0.4);
}

.modalchonjlop_hc-btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.modalchonjlop_hc-btn-secondary {
  background: #f8fafc;
  color: #475569;
  border: 2px solid #cbd5e1;
  padding: 12px 26px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
}

.modalchonjlop_hc-btn-secondary:hover {
  background: #e2e8f0;
  border-color: #94a3b8;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Predefined Items Section in Edit Modal */
.health-campaign-history .hch-predefined-edit-items {
  margin: 20px 0;
  padding: 15px;
  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
  border: 1px solid #fbbf24;
  border-radius: 12px;
}

.health-campaign-history .hch-predefined-edit-items h4 {
  margin: 0 0 12px 0;
  color: #92400e;
  font-size: 0.95rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.health-campaign-history .hch-predefined-edit-items h4::before {
  content: "💡";
  font-size: 1.1rem;
}

.health-campaign-history .hch-predefined-edit-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.health-campaign-history .hch-predefined-edit-item {
  background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
  user-select: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.health-campaign-history .hch-predefined-edit-item:hover:not(:disabled) {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1e40af;
  border-color: #3b82f6;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.health-campaign-history .hch-predefined-edit-item.selected {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #166534;
  border-color: #16a34a;
  cursor: default;
}

.health-campaign-history .hch-predefined-edit-item:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.health-campaign-history .hch-predefined-edit-item.selected:hover {
  transform: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
