.admin-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, #2A7FB8, #1A5E8C);
  height: 60px;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(26, 94, 140, 0.2);
  width: 100%;
  z-index: 100;
  position: relative;
}

.admin-header-left {
  display: flex;
  align-items: center;
}

.admin-header-logo {
  display: flex;
  align-items: center;
  margin-right: 30px;
}

.logo-icon {
  margin-right: 10px;
  color: #FFFFFF;
  font-size: 20px;
}

.logo-icon i {
  color: #FFFFFF;
}

.logo-text {
  display: flex;
  flex-direction: column;
}

.logo-text h1 {
  font-size: 1.2rem;
  color: #FFFFFF;
  margin: 0;
  font-weight: 600;
  letter-spacing: 0.5px;
  line-height: 1.2;
}

.logo-text span {
  font-size: 0.7rem;
  color: #E6F3FA;
  font-weight: 400;
}

.breadcrumb {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: #E6F3FA;
}

.breadcrumb i {
  margin-right: 8px;
  font-size: 0.8rem;
}

.admin-header-right {
  display: flex;
  align-items: center;
}

.header-search {
  position: relative;
  margin-right: 20px;
}

.header-search input {
  background-color: #f8f9fa;
  border: 1px solid #eef0f2;
  border-radius: 20px;
  padding: 8px 15px 8px 35px;
  font-size: 0.9rem;
  width: 220px;
  transition: all 0.3s ease;
}

.header-search input:focus {
  outline: none;
  border-color: #67B7DC;
  box-shadow: 0 0 0 2px rgba(42, 127, 184, 0.2);
  width: 250px;
}

.header-search i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 14px;
}

.header-notifications {
  position: relative;
  margin-right: 20px;
}

.notification-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: #FFFFFF;
  font-size: 1.2rem;
  position: relative;
  cursor: pointer;
  padding: 8px 10px;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.notification-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.25);
}

.notification-btn.active {
  background: #1A5E8C;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.3);
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: linear-gradient(135deg, #4CB8C4, #3CD3AD);
  color: white;
  font-size: 10px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-weight: bold;
  border: 2px solid white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.notifications-dropdown {
  position: absolute;
  top: 45px;
  right: -10px;
  width: 320px;
  background-color: #fff;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  overflow: hidden;
  z-index: 100;
  border: 1px solid #eef0f2;
}

.dropdown-header {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dropdown-header h4 {
  margin: 0;
  font-size: 1rem;
  color: #333;
}

.notification-count {
  background-color: rgba(64, 184, 196, 0.1);
  color: #2a7fb8;
  padding: 3px 10px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
}

.notifications-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.2s ease;
  cursor: pointer;
}

.notification-item:hover {
  background: #f8f9fa;
}

.notification-item.urgent .notification-icon {
  color: #E74C3C;
  background-color: rgba(231, 76, 60, 0.1);
}

.notification-item.info .notification-icon {
  color: #3498DB;
  background-color: rgba(52, 152, 219, 0.1);
}

.notification-item.normal .notification-icon {
  color: #4CB8C4;
  background-color: rgba(76, 184, 196, 0.1);
}



.notification-item.info .notification-icon {
  color: #3498db;
}

.notification-item.normal .notification-icon {
  color: #2ecc71;
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  margin-right: 15px;
  flex-shrink: 0;
}

.notification-content {
  flex: 1;
}

.notification-content h5 {
  margin: 0 0 5px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.admin-header-user {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-right: 10px;
}

.user-greeting {
  font-size: 0.7rem;
  color: #E6F3FA;
}

.user-name {
  font-weight: 600;
  color: #FFFFFF;
  font-size: 0.9rem;
}

.admin-header-profile {
  display: flex;
  align-items: center;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
}

.profile-avatar {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.4);
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-avatar i {
  font-size: 1.5rem;
  color: #aaa;
}

.dropdown-arrow {
  margin-left: 5px;
  font-size: 0.7rem;
  color: #E6F3FA;
}

.notification-content p {
  margin: 0 0 5px 0;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.notification-time {
  font-size: 11px;
  color: #999;
}

.dropdown-footer {
  padding: 15px 20px;
  text-align: center;
  border-top: 1px solid #f0f0f0;
  background: #f8f9fa;
}

.dropdown-footer button {
  background: none;
  border: none;
  color: #667eea;
  font-weight: 600;
  cursor: pointer;
  font-size: 14px;
}

/* Admin Header User Section */
.admin-header-user {
  display: flex;
  align-items: center;
  gap: 15px;
  position: relative;
}

.user-info {
  text-align: right;
  color: #333;
}

.user-greeting {
  display: block;
  font-size: 12px;
  color: #E6F3FA;
  margin-bottom: 2px;
}

.user-name {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #FFFFFF;
}

.admin-header-dropdown {
  position: relative;
}

.admin-header-profile {
  display: flex;
  align-items: center;
  gap: 8px;
  background: transparent;
  border: none;
  font-size: 1.2rem;
  color: #FFFFFF;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 5px;
  border-radius: 50px;
}

.admin-header-profile:hover,
.admin-header-profile.active {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(255, 255, 255, 0.1);
}

.profile-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #2a7fb8, #21D4FD);
  box-shadow: 0 2px 10px rgba(33, 212, 253, 0.3);
  border: 2px solid white;
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-avatar i {
  font-size: 22px;
  color: white;
}

.dropdown-arrow {
  color: #E6F3FA;
  font-size: 12px;
  transition: transform 0.3s ease;
}

.admin-header-profile.active .dropdown-arrow {
  transform: rotate(180deg);
}



.admin-header-dropdown-content {
  position: absolute;
  top: 55px;
  right: 0;
  width: 280px;
  background-color: #fff;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
  border-radius: 10px;
  overflow: hidden;
  z-index: 9999;
  border: 1px solid #eef0f2;
  pointer-events: auto;
}



.dropdown-user-info {
  display: flex;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.dropdown-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  margin-right: 15px;
  flex-shrink: 0;
}

.dropdown-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.dropdown-avatar i {
  font-size: 24px;
  color: white;
}

.dropdown-user-details {
  flex: 1;
}

.dropdown-user-details h4 {
  margin: 0 0 5px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.dropdown-user-details p {
  margin: 0 0 5px 0;
  font-size: 13px;
  color: #666;
}

.user-role {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.dropdown-divider {
  height: 1px;
  background: #e9ecef;
  margin: 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #555;
  width: 100%;
  text-align: left;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  background-color: rgba(42, 127, 184, 0.05);
}

.dropdown-item i {
  color: #2A7FB8;
  width: 20px;
  text-align: center;
  margin-right: 10px;
  font-size: 0.9rem;
}

.dropdown-item.logout {
  color: #e74c3c !important;
  pointer-events: auto !important;
  cursor: pointer !important;
  position: relative !important;
  z-index: 10000 !important;
}

.dropdown-item.logout i {
  color: #e74c3c !important;
  pointer-events: none !important;
}

.dropdown-item.logout span {
  pointer-events: none !important;
}

.dropdown-item.logout:hover {
  background-color: rgba(231, 76, 60, 0.05) !important;
  color: #c0392b !important;
}

.dropdown-item.logout:active {
  background-color: rgba(231, 76, 60, 0.1) !important;
}