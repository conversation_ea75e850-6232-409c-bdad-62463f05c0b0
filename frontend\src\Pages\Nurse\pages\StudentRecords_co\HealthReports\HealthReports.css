/* Health Reports Styles - Modern Design */
.health-reports-container {
  padding: 24px;
  background-color: #f8fafc;
  min-height: 100vh;
}

.report-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px;
  border-radius: 16px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.report-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.report-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 0;
}

.export-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.export-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* KPI Cards */
.kpi-card {
  border: none;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.kpi-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.kpi-card.blue {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.kpi-card.green {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
}

.kpi-card.red {
  background: linear-gradient(135deg, #f44336 0%, #da190b 100%);
  color: white;
}

.kpi-card.orange {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
  color: white;
}

.kpi-card.purple {
  background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
  color: white;
}

.kpi-card.teal {
  background: linear-gradient(135deg, #009688 0%, #00695c 100%);
  color: white;
}

.kpi-icon {
  opacity: 0.9;
  margin-right: 16px;
}

.kpi-content {
  flex: 1;
}

.kpi-value {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.kpi-label {
  font-size: 0.9rem;
  opacity: 0.9;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Chart Cards */
.chart-card {
  border: none;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}

.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.chart-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
  padding: 20px 24px;
  border-radius: 16px 16px 0 0;
}

.chart-header h5 {
  color: #334155;
  font-weight: 600;
  margin: 0;
}

.chart-container {
  height: 300px;
  position: relative;
  padding: 20px;
}

/* Data Table */
.data-table-card {
  border: none;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
  padding: 20px 24px;
}

.student-count-badge {
  font-size: 0.9rem;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
}

.filter-form {
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.filter-label {
  font-weight: 600;
  color: #334155;
  margin-bottom: 8px;
}

.filter-select {
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  padding: 12px;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.filter-select:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modern-table {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.modern-table thead {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: white;
}

.modern-table thead th {
  border: none;
  padding: 16px 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.85rem;
}

.modern-table tbody td {
  padding: 16px 12px;
  border-bottom: 1px solid #f1f5f9;
  vertical-align: middle;
}

.modern-table tbody tr:hover {
  background-color: #f8fafc;
}

.modern-table tbody tr:last-child td {
  border-bottom: none;
}

.student-name strong {
  color: #1e293b;
  font-weight: 600;
}

.student-name small {
  font-size: 0.8rem;
  color: #64748b;
}

.class-badge {
  font-weight: 600;
  padding: 6px 12px;
  border-radius: 20px;
  border: 1px solid #cbd5e1;
}

.bmi-value {
  font-weight: 600;
  color: #1e293b;
  font-size: 1.1rem;
}

.blood-type-badge {
  font-weight: 600;
  padding: 6px 12px;
  border-radius: 20px;
}

/* Pagination Styles */
.pagination-container {
  padding: 20px 0;
  border-top: 1px solid #e9ecef;
}

.pagination-info {
  font-size: 14px;
  color: #6c757d;
}

.pagination {
  margin-bottom: 0;
}

.page-link {
  color: #007bff;
  border: 1px solid #dee2e6;
  padding: 8px 12px;
  margin: 0 2px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.page-link:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
}

.page-item.active .page-link {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.page-item.disabled .page-link {
  color: #6c757d;
  background-color: #fff;
  border-color: #dee2e6;
}

@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }
  
  .pagination-info {
    text-align: center;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .health-reports-container {
    padding: 16px;
  }
  
  .report-header {
    padding: 24px;
  }
  
  .report-title {
    font-size: 2rem;
  }
  
  .kpi-value {
    font-size: 2rem;
  }
  
  .chart-container {
    height: 250px;
    padding: 10px;
  }
  
  .filter-form {
    padding: 16px;
  }
  
  .modern-table {
    font-size: 0.9rem;
  }
  
  .modern-table thead th,
  .modern-table tbody td {
    padding: 12px 8px;
  }
}

.report-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.chart-container {
  position: relative;
  height: 300px; /* Adjust height as needed */
  width: 100%;
}

.report-summary {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.report-summary h3 {
  margin-top: 0;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.summary-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  flex: 1;
}

.stat-icon {
  font-size: 24px;
  width: 50px;
  height: 50px;
  background-color: #e3f2fd;
  color: #1976d2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-item:nth-child(2) .stat-icon {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.stat-item:nth-child(3) .stat-icon {
  background-color: #fff8e1;
  color: #ffa000;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.report-details {
  margin-top: 30px;
}

.report-details h4 {
  margin-top: 0;
  margin-bottom: 15px;
}

.summary-table {
  width: 100%;
  border-collapse: collapse;
}

.summary-table th,
.summary-table td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.summary-table th {
  font-weight: 600;
  background-color: #f8f9fa;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #4a90e2;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  text-align: center;
  color: #d32f2f;
  padding: 30px;
}

.error-container i {
  font-size: 24px;
  margin-bottom: 10px;
}

.error-container button {
  margin-top: 15px;
  padding: 8px 15px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .report-content {
    grid-template-columns: 1fr;
  }
  
  .summary-stats {
    flex-direction: column;
    gap: 10px;
  }
}

.kpi-card {
  color: #fff;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: none;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  transition: transform 0.2s;
}
.kpi-card:hover {
    transform: translateY(-5px);
}

.kpi-card svg {
  opacity: 0.8;
}

.kpi-card div {
  text-align: right;
}

.kpi-card span {
  display: block;
  font-size: 0.9rem;
  opacity: 0.9;
}

.kpi-card strong {
  display: block;
  font-size: 1.8rem;
  font-weight: 700;
}

.kpi-card.blue { background-color: #007bff; }
.kpi-card.green { background-color: #28a745; }
.kpi-card.red { background-color: #dc3545; }
.kpi-card.orange { background-color: #fd7e14; }
.kpi-card.purple { background-color: #6f42c1; }

.card-header {
    background-color: #e9ecef;
    font-weight: 600;
}
