.admin-dashboard {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100%;
  background-color: #f5f7fa;
}

.admin-header {
  background: linear-gradient(135deg, #1a7bb5, #0a5d8f); /* Medical blue gradient */
  color: white;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(26, 123, 181, 0.2); /* Medical blue shadow */
}

.admin-header h1 {
  font-size: 1.5rem;
  margin: 0;
}

.admin-user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logout-button {
  background-color: rgba(231, 76, 60, 0.8);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.logout-button:hover {
  background-color: #c0392b;
}

.admin-container {
  display: flex;
  flex: 1;
  width: 100%;
}

.admin-sidebar {
  width: 250px;
  background-color: white;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
}

.admin-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.admin-menu-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 1rem 1.5rem;
  color: #5f6368;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.admin-menu-item:hover {
  background-color: #f8f9fa;
  color: #1a73e8;
}

.admin-menu-item.active {
  background-color: #e8f5fd; /* Light medical blue background */
  color: #1a7bb5; /* Medical blue */
  border-left-color: #1a7bb5; /* Medical blue */
}

.admin-menu-item i {
  font-size: 1.1rem;
  width: 24px;
  text-align: center;
}

.admin-content {
  flex: 1;
  overflow: auto;
}

.admin-welcome {
  margin-bottom: 2rem;
}

.admin-welcome h2 {
  font-size: 1.75rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.admin-welcome p {
  color: #6b7280;
}

.admin-stats {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.admin-stat-card {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.admin-stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 50px;
  height: 50px;
  background-color: #e8f0fe;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  color: #1a73e8;
}

.stat-icon i {
  font-size: 1.5rem;
}

.stat-details h3 {
  font-size: 2rem;
  margin: 0;
  color: #333;
}

.stat-details p {
  margin: 0.3rem 0 0;
  color: #6b7280;
}

.admin-recent {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.admin-card {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.admin-card h3 {
  margin-top: 0;
  margin-bottom: 1.25rem;
  color: #333;
  font-size: 1.2rem;
  border-bottom: 1px solid #edf2f7;
  padding-bottom: 0.75rem;
}

.activity-list, .notification-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.activity-list li {
  display: flex;
  align-items: flex-start;
  padding: 0.75rem 0;
  border-bottom: 1px solid #edf2f7;
}

.activity-list li:last-child {
  border-bottom: none;
}

.activity-time {
  width: 80px;
  color: #6b7280;
  font-size: 0.85rem;
}

.activity-details {
  flex: 1;
}

.activity-user {
  font-weight: 600;
  margin-right: 0.5rem;
}

.activity-action {
  color: #6b7280;
}

.notification-item {
  display: flex;
  gap: 1rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid #edf2f7;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item i {
  color: #3182ce;
  font-size: 1.25rem;
}

.notification-item.urgent i {
  color: #e53e3e;
}

.notification-content p {
  margin: 0 0 0.25rem;
  color: #4a5568;
}

.notification-content span {
  font-size: 0.85rem;
  color: #6b7280;
}

.dashboard-content {
  padding: 1.5rem;
}

.dashboard-header {
  margin-bottom: 2rem;
}

.dashboard-header h1 {
  font-size: 1.75rem;
  color: #1e293b;
  margin: 0;
}

.dashboard-header p {
  color: #64748b;
  margin-top: 0.5rem;
  font-size: 0.875rem;
}

/* Stats Cards */
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 0.5rem;
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
}

.stat-icon.students {
  background-color: #3b82f6;
}

.stat-icon.events {
  background-color: #10b981;
}

.stat-icon.upcoming {
  background-color: #f59e0b;
}

.stat-icon.reports {
  background-color: #6366f1;
}

.stat-details h3 {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
  margin: 0;
}

.stat-value {
  color: #1e293b;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0.25rem 0 0 0;
}

/* Dashboard Row with Cards */
.dashboard-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.dashboard-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.card-header h2 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.view-all-btn {
  background: none;
  border: none;
  color: #3b82f6;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
}

.card-content {
  padding: 1.5rem;
}

/* Events Table */
.events-table {
  width: 100%;
  border-collapse: collapse;
}

.events-table th,
.events-table td {
  padding: 0.75rem 0.5rem;
  text-align: left;
  font-size: 0.875rem;
}

.events-table th {
  color: #64748b;
  font-weight: 500;
  border-bottom: 1px solid #e5e7eb;
}

.events-table tr:not(:last-child) td {
  border-bottom: 1px solid #f1f5f9;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.completed {
  background-color: #dcfce7;
  color: #16a34a;
}

.status-badge.upcoming {
  background-color: #dbeafe;
  color: #2563eb;
}

.status-badge.in-progress {
  background-color: #fef3c7;
  color: #d97706;
}

.status-badge.postponed {
  background-color: #fee2e2;
  color: #dc2626;
}

/* Alerts */
.alerts {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.alert-item {
  border-radius: 0.375rem;
  padding: 1rem;
  position: relative;
}

.alert-item.high {
  background-color: #fee2e2;
  border-left: 4px solid #dc2626;
}

.alert-item.medium {
  background-color: #fef3c7;
  border-left: 4px solid #d97706;
}

.alert-item.low {
  background-color: #e0e7ff;
  border-left: 4px solid #4f46e5;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.alert-header h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: #1e293b;
}

.alert-date {
  font-size: 0.75rem;
  color: #64748b;
}

.alert-description {
  margin: 0;
  font-size: 0.875rem;
  color: #4b5563;
}

/* Responsive */
@media (max-width: 992px) {
  .dashboard-row {
    grid-template-columns: 1fr;
  }

  .stats-container {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 1024px) {
  .admin-recent {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .admin-container {
    flex-direction: column;
  }
  
  .admin-sidebar {
    width: 100%;
    order: 2;
  }
  
  .admin-content {
    order: 1;
  }
  
  .admin-stats {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
  
  .admin-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}