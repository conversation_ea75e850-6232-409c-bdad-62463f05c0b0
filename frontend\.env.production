# Environment variables for production deployment
# Backend API URL (replace with your deployed backend URL)
VITE_API_BASE_URL=https://medically-backend.southeastasia.cloudapp.azure.com/api/v1

# Google OAuth Configuration
VITE_GOOGLE_CLIENT_ID=your-production-google-client-id
VITE_GOOGLE_REDIRECT_URI=https://school-medical-management-system-red.vercel.app/auth/oauth2/callback

# Backend OAuth URL
VITE_BACKEND_URL=https://medically-backend.southeastasia.cloudapp.azure.com

# Environment
NODE_ENV=production
