/* ========================================
   CSS for CreateRecordModal.jsx
   T<PERSON><PERSON> sơ Tiêm chủng Modal Styles
   ======================================== */

/* Modal Container */
.create-vaccination-record-modal .modal-dialog {
  max-width: 600px;
  margin: 1.75rem auto;
}

.create-vaccination-record-modal .modal-content {
  border: none;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

/* Modal Header */
.create-vaccination-record-modal .modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom: none;
  padding: 1.5rem 2rem;
}

.create-vaccination-record-modal .modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.create-vaccination-record-modal .btn-close {
  background: none;
  border: none;
  color: white;
  opacity: 0.8;
  font-size: 1.2rem;
  padding: 0.5rem;
  margin: 0;
}

.create-vaccination-record-modal .btn-close:hover {
  opacity: 1;
  transform: scale(1.1);
}

/* Modal Body */
.create-vaccination-record-modal .modal-body {
  padding: 2rem;
  background: #f8f9fa;
}

/* Student Info Section */
.create-vaccination-record-modal .student-info-section {
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 1;
}

.create-vaccination-record-modal .student-info-section h6 {
  margin-bottom: 0.75rem;
  color: #495057;
  font-weight: 600;
  line-height: 1.5;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.create-vaccination-record-modal .student-info-section .fw-normal {
  color: #6c757d;
  font-weight: 400;
  display: inline-block;
  max-width: 100%;
  word-wrap: break-word;
}

.create-vaccination-record-modal .student-info-section .text-primary {
  color: #667eea !important;
  font-weight: 500;
  display: inline-block;
  max-width: 100%;
  word-wrap: break-word;
}

.create-vaccination-record-modal .student-info-section hr {
  margin: 1rem 0;
  border-color: #e9ecef;
  clear: both;
}

/* Form Section */
.create-vaccination-record-modal .form-section {
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* Form Groups */
.create-vaccination-record-modal .form-group {
  margin-bottom: 1.5rem;
}

/* Floating Labels */
.create-vaccination-record-modal .form-floating {
  margin-bottom: 1.5rem;
}

.create-vaccination-record-modal .form-floating > .form-control,
.create-vaccination-record-modal .form-floating > .form-select {
  border: 2px solid #e9ecef;
  border-radius: 10px;
  padding: 1rem 0.75rem;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.create-vaccination-record-modal .form-floating > .form-control:focus,
.create-vaccination-record-modal .form-floating > .form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.create-vaccination-record-modal .form-floating > label {
  color: #6c757d;
  font-weight: 500;
}

/* Textarea Specific */
.create-vaccination-record-modal .form-floating textarea.form-control {
  min-height: 120px;
  resize: vertical;
}

/* Validation Styles */
.create-vaccination-record-modal .form-control.is-invalid {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.create-vaccination-record-modal .form-control.is-valid {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.create-vaccination-record-modal .invalid-feedback {
  display: block;
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.5rem;
  font-weight: 500;
}

/* Modal Footer */
.create-vaccination-record-modal .modal-footer {
  background: white;
  border-top: 1px solid #e9ecef;
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.create-vaccination-record-modal .modal-footer .btn {
  padding: 0.75rem 2rem;
  border-radius: 25px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  border: none;
}

.create-vaccination-record-modal .modal-footer .btn-secondary {
  background: #6c757d;
  color: white;
}

.create-vaccination-record-modal .modal-footer .btn-secondary:hover {
  background: #5a6268;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.create-vaccination-record-modal .modal-footer .btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.create-vaccination-record-modal .modal-footer .btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .create-vaccination-record-modal .modal-dialog {
    margin: 1rem;
    max-width: calc(100% - 2rem);
  }

  .create-vaccination-record-modal .modal-header,
  .create-vaccination-record-modal .modal-body,
  .create-vaccination-record-modal .modal-footer {
    padding: 1.5rem;
  }

  .create-vaccination-record-modal .modal-footer {
    flex-direction: column;
  }

  .create-vaccination-record-modal .modal-footer .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }
}

/* Animation Effects */
.create-vaccination-record-modal .modal.fade .modal-dialog {
  transition: transform 0.3s ease-out;
  transform: translate(0, -50px);
}

.create-vaccination-record-modal .modal.show .modal-dialog {
  transform: none;
}

/* Loading State */
.create-vaccination-record-modal .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.create-vaccination-record-modal .btn:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Additional Enhancements */
.create-vaccination-record-modal .form-floating > .form-control::placeholder {
  color: transparent;
}

.create-vaccination-record-modal .form-floating > .form-control:focus::placeholder {
  color: #6c757d;
}

/* Success State */
.create-vaccination-record-modal .btn-primary.success-state {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.create-vaccination-record-modal .btn-primary.success-state:hover {
  background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
}

/* Focus States */
.create-vaccination-record-modal .form-floating > .form-control:focus + label,
.create-vaccination-record-modal .form-floating > .form-control:not(:placeholder-shown) + label {
  color: #667eea;
  font-weight: 600;
}

/* Required Field Indicator */
.create-vaccination-record-modal .form-floating > label::after {
  content: "";
}

.create-vaccination-record-modal .form-floating > label[for="formVaccinationDate"]::after,
.create-vaccination-record-modal .form-floating > label[for="formAdministeredAt"]::after {
  content: " *";
  color: #dc3545;
  font-weight: bold;
}

/* Smooth Transitions */
.create-vaccination-record-modal * {
  transition: all 0.3s ease;
}

/* Custom Scrollbar for Textarea */
.create-vaccination-record-modal textarea.form-control::-webkit-scrollbar {
  width: 8px;
}

.create-vaccination-record-modal textarea.form-control::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.create-vaccination-record-modal textarea.form-control::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.create-vaccination-record-modal textarea.form-control::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Form Help Text */
.create-vaccination-record-modal .text-muted {
  font-size: 0.875rem;
  margin-top: 0.5rem;
  display: block;
  line-height: 1.4;
}

.create-vaccination-record-modal .text-muted .fas {
  color: #667eea;
}

/* Info Text Color */
.create-vaccination-record-modal .text-info {
  color: #17a2b8 !important;
  font-weight: 500;
}