/* Medical Supplies Page */
.admin_ui_medical_supplies {
  padding: 0;
  background: #f8fafc;
  min-height: 100vh;
}

/* Header Section */
.admin_ui_medical_header {
  background: white;
  padding: 24px 32px;
  border-radius: 16px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.admin_ui_back_btn {
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  color: #64748b;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  margin-bottom: 16px;
  transition: all 0.2s ease;
}

.admin_ui_back_btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.admin_ui_header_content h1 {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.admin_ui_header_content p {
  color: #64748b;
  margin: 0;
  font-size: 14px;
}

/* Stats Grid */
.admin_ui_stats_grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.admin_ui_stat_card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  overflow: hidden;
}

.admin_ui_stat_card::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
}

.admin_ui_stat_blue::before {
  background: #3b82f6;
}

.admin_ui_stat_yellow::before {
  background: #f59e0b;
}

.admin_ui_stat_red::before {
  background: #ef4444;
}

.admin_ui_stat_gray::before {
  background: #64748b;
}

.admin_ui_stat_icon {
  font-size: 24px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: #f8fafc;
}

.admin_ui_stat_blue .admin_ui_stat_icon {
  background: #dbeafe;
  color: #3b82f6;
}

.admin_ui_stat_yellow .admin_ui_stat_icon {
  background: #fef3c7;
  color: #f59e0b;
}

.admin_ui_stat_red .admin_ui_stat_icon {
  background: #fee2e2;
  color: #ef4444;
}

.admin_ui_stat_value {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1;
}

.admin_ui_stat_label {
  font-size: 14px;
  color: #64748b;
  margin-top: 4px;
}

/* Filters Section */
.admin_ui_filters_section {
  background: white;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.admin_ui_search_box {
  flex: 1;
  min-width: 300px;
}

.admin_ui_search_input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  background: #f8fafc;
  transition: all 0.2s ease;
}

.admin_ui_search_input:focus {
  outline: none;
  border-color: #3b82f6;
  background: white;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.admin_ui_filter_select {
  padding: 12px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  min-width: 150px;
}

.admin_ui_filter_select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.admin_ui_results_info {
  text-align: center;
  color: #64748b;
  font-size: 14px;
  margin-bottom: 16px;
}

/* Table */
.admin_ui_table_container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.admin_ui_supplies_table {
  width: 100%;
  border-collapse: collapse;
}

.admin_ui_supplies_table th {
  background: #f8fafc;
  padding: 16px 12px;
  text-align: left;
  font-weight: 600;
  font-size: 12px;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid #e2e8f0;
}

.admin_ui_supplies_table td {
  padding: 16px 12px;
  border-bottom: 1px solid #f1f5f9;
  font-size: 14px;
  color: #1e293b;
}

.admin_ui_supplies_table tr:hover {
  background: #f8fafc;
}

.admin_ui_supply_info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.admin_ui_supply_name {
  font-weight: 500;
  color: #1e293b;
}

.admin_ui_supply_desc {
  font-size: 12px;
  color: #64748b;
}

/* Badges */
.admin_ui_category_badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin_ui_category_supplies {
  background: #10b981;
  color: white;
}

.admin_ui_category_medicine {
  background: #3b82f6;
  color: white;
}

.admin_ui_category_equipment {
  background: #64748b;
  color: white;
}

.admin_ui_status_badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
}

.admin_ui_status_available {
  background: #dcfce7;
  color: #166534;
}

.admin_ui_status_unavailable {
  background: #fee2e2;
  color: #991b1b;
}

.admin_ui_stock_badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
}

.admin_ui_stock_valid {
  background: #dcfce7;
  color: #166534;
}

.admin_ui_stock_warning {
  background: #fef3c7;
  color: #92400e;
}

.admin_ui_stock_expired {
  background: #fee2e2;
  color: #991b1b;
}

.admin_ui_action_btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.admin_ui_action_btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* Responsive */
@media (max-width: 768px) {
  .admin_ui_stats_grid {
    grid-template-columns: 1fr;
  }
  
  .admin_ui_filters_section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .admin_ui_search_box {
    min-width: auto;
  }
  
  .admin_ui_supplies_table {
    font-size: 12px;
  }
  
  .admin_ui_supplies_table th,
  .admin_ui_supplies_table td {
    padding: 8px 6px;
  }
}
