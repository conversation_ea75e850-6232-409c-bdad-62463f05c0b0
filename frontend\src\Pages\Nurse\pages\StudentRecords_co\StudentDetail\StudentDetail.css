/* <PERSON><PERSON><PERSON> bảo file CSS nằm đúng thư mục */
.student-detail-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.detail-header {
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
  gap: 15px;
  min-height: 40px;
}

.detail-header h2 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.detail-title {
  grid-column: 2;
  text-align: center;
  margin: 0;
  font-size: 24px;
  color: #333;
  font-weight: 600;
}

.back-button,
.edit-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  transition: all 0.2s;
}

.back-button {
  background-color: #f0f0f0;
  color: #333;
  grid-column: 1; /* Đặt ở cột đầu tiên */
  justify-self: start;
}

.back-button:hover {
  background-color: #e0e0e0;
}

.edit-button {
  background-color: #2196F3;
  color: white;
}

.edit-button:hover {
  background-color: #0b7dda;
}

.detail-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;
}

.student-info-section {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  grid-column: 1 / -1;
}

.basic-info h3 {
  margin-top: 0;
  font-size: 18px;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.basic-info p {
  margin: 8px 0;
  line-height: 1.5;
}

.health-info-section {
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.health-info-section h3 {
  margin-top: 0;
  font-size: 18px;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.info-group {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.info-group h4 {
  margin-top: 0;
  font-size: 16px;
  color: #444;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

/* Metrics styling */
.metrics-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 15px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border-radius: 8px;
  background-color: #f5f5f5;
  transition: all 0.2s;
}

.metric-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.metric-icon {
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.metric-icon i {
  font-size: 18px;
}

.metric-content {
  display: flex;
  flex-direction: column;
}

.metric-label {
  font-size: 12px;
  color: #777;
}

.metric-value {
  font-size: 18px;
  font-weight: 600;
  margin-top: 2px;
  color: #333;
}

.metric-unit {
  font-size: 12px;
  color: #777;
  margin-left: 4px;
}

.metric-status {
  font-size: 12px;
  margin-top: 2px;
  font-weight: 500;
}

.blood-type {
  background-color: #f44336;
  color: white;
  font-weight: 600;
  border-radius: 4px;
  padding: 2px 8px;
  display: inline-block;
  font-size: 14px;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(244, 67, 54, 0.3);
}

/* Health history styling */
.health-history-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
  gap: 15px;
  transition: all 0.2s;
}

.health-history-item:hover {
  background-color: #f9f9f9;
  padding: 10px;
  border-radius: 6px;
  transform: translateX(5px);
}

.health-history-icon {
  width: 36px;
  height: 36px;
  background-color: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

.health-history-content {
  flex: 1;
}

.health-history-label {
  font-weight: 600;
  margin: 0 0 5px 0;
  color: #444;
  font-size: 14px;
}

.health-history-value {
  margin: 0;
  color: #666;
  font-size: 15px;
  background-color: rgba(0, 0, 0, 0.02);
  padding: 4px 8px;
  border-radius: 4px;
}

/* Vaccination styling */
.vaccination-info {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-top: 15px;
}

.vaccination-icon {
  width: 40px;
  height: 40px;
  background-color: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

.vaccination-icon i {
  font-size: 18px;
}

.vaccination-content {
  flex: 1;
}

.vaccination-name {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.last-exam-date {
  margin-top: 10px;
  font-size: 12px;
  color: #777;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Emergency contact styling */
.emergency-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.emergency-icon {
  width: 40px;
  height: 40px;
  background-color: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

.emergency-icon i {
  font-size: 18px;
}

.emergency-content {
  flex: 1;
}

.last-updated {
  margin-top: 15px;
  font-size: 12px;
  color: #777;
  text-align: right;
  display: flex;
  align-items: center;
  gap: 5px;
  justify-content: flex-end;
}

/* Loading and error messages */
.loading-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  text-align: center;
}

.loading-spinner {
  border: 3px solid #f3f3f3;
  border-radius: 50%;
  border-top: 3px solid #2196F3;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  padding: 20px;
  background-color: #fff3cd;
  color: #856404;
  border-radius: 4px;
  margin-bottom: 15px;
  border-left: 4px solid #ffc107;
}

/* Vaccination history styles */
.vaccination-history {
  margin-top: 20px;
}

.vaccination-history .vaccination-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.vaccination-history .vaccination-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #4caf50;
}

.vaccination-item-icon {
  flex-shrink: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e8f5e8;
  border-radius: 50%;
}

.vaccination-item-content {
  flex: 1;
}

.vaccination-item-content h5 {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.vaccination-details {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.vaccination-details p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.vaccination-details p strong {
  color: #333;
  font-weight: 600;
}

@media (max-width: 768px) {
  .detail-content {
    grid-template-columns: 1fr;
  }
  
  .metrics-container {
    grid-template-columns: 1fr 1fr;
  }
  
  .vaccination-history .vaccination-item {
    flex-direction: column;
    gap: 10px;
  }
  
  .vaccination-item-icon {
    align-self: flex-start;
  }
}

@media (max-width: 576px) {
  .metrics-container {
    grid-template-columns: 1fr;
  }
  
  .detail-header {
    grid-template-columns: 1fr;
    gap: 15px;
    text-align: center;
  }

  .back-button {
    grid-column: 1;
    justify-self: start;
    margin-bottom: 10px;
  }

  .detail-title {
    grid-column: 1;
    text-align: center;
  }
  
  .health-history-item,
  .vaccination-info,
  .emergency-info {
    flex-direction: column;
    gap: 10px;
  }
  
  .health-history-icon,
  .vaccination-icon,
  .emergency-icon {
    margin-bottom: 5px;
  }
  
  .vaccination-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .vaccination-icon {
    align-self: flex-start;
  }
}
