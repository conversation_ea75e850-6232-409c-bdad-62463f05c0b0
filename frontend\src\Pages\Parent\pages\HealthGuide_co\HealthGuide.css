/* Health Guide Styling - Professional Blue Theme */
:root {
  --primary: #015C92;
  --primary-dark: #2D82B5;
  --primary-light: #BCE6FF;
  --text-dark: #1e293b;
  --text-medium: #64748b;
  --text-light: #94a3b8;
  --background-light: #BCE6FF;
  --border-light: #88CDF6;
  --shadow-sm: 0 2px 4px rgba(1, 92, 146, 0.1);
  --shadow-md: 0 4px 8px rgba(1, 92, 146, 0.15);
  --shadow-lg: 0 8px 16px rgba(1, 92, 146, 0.2);
  --shadow-xl: 0 15px 25px rgba(1, 92, 146, 0.25);
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --transition-normal: all 0.3s ease;
  --transition-fast: all 0.2s ease;
}

.health-guide-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px 15px;
  font-family: 'Be Vietnam Pro', sans-serif;
  background-color: #ffffff;
  min-height: 100vh;
}

/* Override handled by global.css */

/* Header styling - Professional Blue gradient theme */
.health-guide-header {
  background: linear-gradient(135deg, #015C92 0%, #2D82B5 30%, #428CD4 60%, #88CDF6 100%);
  color: #ffffff;
  padding: 50px 30px;
  text-align: center;
  border-radius: var(--radius-lg);
  margin-bottom: 30px;
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
  z-index: 1;
  border: none;
}

/* Thêm hiệu ứng background tương tự như community-header */
.health-guide-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><path fill="rgba(255,255,255,0.1)" d="M42.7,-65.1C53.2,-52.6,58.1,-37.6,65.3,-22.6C72.6,-7.7,82.2,7.1,79.4,19.5C76.6,31.9,61.4,41.8,47.4,49.7C33.4,57.6,20.7,63.5,5.5,67.5C-9.7,71.4,-27.4,73.5,-41.7,66.9C-56,60.2,-66.8,44.8,-71.1,28.6C-75.4,12.4,-73.1,-4.7,-69.6,-22.7C-66,-40.7,-61.3,-59.5,-48.9,-71.4C-36.6,-83.4,-18.3,-88.4,-1.1,-86.9C16.1,-85.3,32.2,-77.1,42.7,-65.1Z" transform="translate(100 100)" /></svg>');
  background-repeat: no-repeat;
  background-size: contain;
  opacity: 0.2;
  transform: rotate(45deg);
  z-index: 0;
}

.health-guide-header-content {
  position: relative;
  z-index: 1;
}

.health-guide-header h1 {
  font-size: 2.2rem;
  margin-bottom: 12px;
  font-weight: 700;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.health-guide-header p {
  font-size: 1.1rem;
  max-width: 700px;
  margin: 0 auto;
  opacity: 0.9;
  line-height: 1.5;
  font-family: 'Be Vietnam Pro', sans-serif;
}

/* Content layout */
.health-guide-content {
  display: grid;
  grid-template-columns: 320px 1fr;
  gap: 25px;
  padding: 0 0 40px;
}

/* Sidebar styling - Sử dụng style tương tự community-sidebar */
.health-guide-sidebar {
  position: sticky;
  top: 25px;
  align-self: start;
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.category-filter {
  margin-bottom: 0;
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-light);
}

.category-filter h3 {
  padding: 15px;
  margin: 0;
  font-size: 1.1rem;
  background-color: #f8fafc;
  color: var(--text-dark);
  border-bottom: 1px solid var(--border-light);
  font-family: 'Be Vietnam Pro', sans-serif;
  font-weight: 600;
  position: relative;
}

.category-filter ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.category-filter li {
  padding: 0;
  margin: 0;
  border-bottom: 1px solid var(--border-light);
}

.category-filter li:last-child {
  border-bottom: none;
}

.category-filter button {
  display: flex;
  align-items: center;
  gap: 10px;
  background: none;
  border: none;
  padding: 12px 15px;
  width: 100%;
  text-align: left;
  color: var(--text-medium);
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.category-filter button:hover {
  background-color: #f1f5f9;
  color: var(--primary-color);
}

.category-filter button.active {
  background-color: var(--primary-light);
  color: var(--primary-color);
  font-weight: 500;
  border-left: 3px solid var(--primary-color);
}

.recent-posts {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-light);
  padding: 0;
  margin-top: 0;
}

.recent-posts h3 {
  padding: 15px;
  margin: 0;
  font-size: 1.1rem;
  background-color: #f8fafc;
  color: var(--text-dark);
  border-bottom: 1px solid var(--border-light);
  font-family: 'Be Vietnam Pro', sans-serif;
  font-weight: 600;
  position: relative;
}

.recent-posts ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.recent-posts li {
  margin: 0;
  padding: 15px;
  border-bottom: 1px solid var(--border-light);
}

.recent-posts li:last-child {
  border-bottom: none;
}

.recent-posts a {
  display: flex;
  text-decoration: none;
  color: inherit;
  align-items: center;
  padding: 0;
  border-radius: 0;
  transition: all 0.2s;
  background-color: transparent;
  box-shadow: none;
}

.recent-posts a:hover {
  box-shadow: none;
  transform: none;
}

.recent-post-image {
  width: 60px;
  height: 60px;
  margin-right: 15px;
  flex-shrink: 0;
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.recent-post-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.recent-posts a:hover .recent-post-image img {
  transform: scale(1.08);
}

.recent-post-content {
  flex: 1;
}

.recent-post-content h4 {
  font-size: 0.95rem;
  margin: 0 0 5px;
  color: var(--text-dark);
  line-height: 1.4;
  transition: all 0.2s;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.recent-posts a:hover .recent-post-content h4 {
  color: var(--primary-color);
}

.post-date {
  font-size: 0.85rem;
  color: var(--text-light);
  display: flex;
  align-items: center;
  font-family: 'Be Vietnam Pro', sans-serif;
}

/* Articles styling - Card design giống với post-card */
.health-guide-articles {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.articles-header {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  padding: 20px;
  box-shadow: var(--shadow);
  margin-bottom: 20px;
  border: 1px solid var(--border-light);
}

.articles-header h2 {
  font-size: 1.4rem;
  color: var(--text-dark);
  margin: 0 0 8px;
  font-weight: 600;
  position: relative;
  display: inline-block;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.articles-header p {
  color: var(--text-medium);
  font-size: 0.95rem;
  margin: 0;
  line-height: 1.5;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.article-card {
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #edf2f7;
  position: relative;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
  transform: none; /* Thêm để tránh xung đột */
}

.article-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.article-image {
  height: 150px;
  overflow: hidden;
  position: relative;
}

.article-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.article-card:hover .article-image img {
  transform: scale(1.05);
}

.article-content {
  padding: 12px;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  background-color: white;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  align-items: center;
}

.article-category {
  background-color: #eef3fb;
  color: #4a6cf7;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.65rem;
  font-weight: 500;
  display: inline-block;
  text-transform: uppercase;
  letter-spacing: 0.2px;
}

.article-date {
  font-size: 0.65rem;
  color: #94a3b8;
}

.article-title {
  margin: 0 0 8px;
  font-size: 0.85rem;
  line-height: 1.3;
  font-weight: 600;
  letter-spacing: -0.1px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  max-height: 3rem;
  color: #193055;
}

.article-title a {
  color: inherit;
  text-decoration: none;
  transition: color 0.2s ease;
  font-size: 1.1rem;
  display: inline-block;
  width: 100%;
  word-break: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  line-height: 1.3;
}

.article-footer {
  padding-top: 8px;
  margin-top: auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #f1f5f9;
}

.article-author {
  font-size: 0.7rem;
  color: #64748b;
  display: flex;
  align-items: center;
  gap: 4px;
}

.article-author i {
  color: #94a3b8;
  font-size: 0.75rem;
}

.read-more {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.7rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 3px;
  transition: all 0.2s;
}

.read-more:hover {
  gap: 5px;
}

.read-more i {
  font-size: 0.65rem;
  transition: transform 0.2s ease;
}

.read-more:hover i {
  transform: translateX(2px);
}

/* No results styling */
.no-results {
  text-align: center;
  padding: 50px 20px;
  color: var(--text-medium);
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  border: 1px solid var(--border-light);
}

.no-results i {
  font-size: 48px;
  opacity: 0.3;
  margin-bottom: 15px;
  color: var(--text-light);
}

.no-results p {
  font-size: 1.1rem;
  margin: 0 0 20px;
  color: var(--text-medium);
  font-family: 'Be Vietnam Pro', sans-serif;
}

.no-results button {
  background-color: #edf2f7;
  color: var(--text-medium);
  border: none;
  padding: 10px 16px;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 0.95rem;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  margin: 15px auto 0;
  font-family: 'Be Vietnam Pro', sans-serif;
  transition: all 0.2s;
}

.no-results button:hover {
  background-color: #e2e8f0;
  color: var(--text-dark);
}

/* Loading styles */
.error-message {
  text-align: center;
  padding: 50px 20px;
  color: var(--text-medium);
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  border: 1px solid var(--border-light);
}

.error-message i {
  font-size: 48px;
  color: #f56565;
  margin-bottom: 15px;
}

.error-message p {
  font-size: 1.1rem;
  margin: 0 0 20px;
  color: var(--text-medium);
  font-family: 'Be Vietnam Pro', sans-serif;
}

.error-message button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 0.95rem;
  font-family: 'Be Vietnam Pro', sans-serif;
  transition: all 0.2s;
}

.error-message button:hover {
  background-color: var(--accent-color);
}

/* Pagination styles */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
  gap: 16px;
}

.pagination-btn {
  background-color: var(--bg-white);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius);
  padding: 10px 16px;
  display: flex;
  width: 150px;
  align-items: center;
  gap: 8px;
  font-size: 0.95rem;
  color: var(--text-medium);
  cursor: pointer;
  transition: all 0.2s;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #f5f5f5;
  color: var(--text-dark);
  border-color: var(--text-medium);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-pages {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-number {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 1px solid var(--border-light);
  background: none;
  font-size: 0.9rem;
  color: var(--text-medium);
  cursor: pointer;
  transition: all 0.2s;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.page-number.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  font-weight: 600;
}

.page-number:not(.active):hover {
  background-color: #f5f5f5;
}

.page-ellipsis {
  color: var(--text-medium);
  font-weight: bold;
  letter-spacing: 1px;
}

.page-info {
  font-size: 0.95rem;
  color: var(--text-medium);
  font-family: 'Be Vietnam Pro', sans-serif;
}

/* Parent Health Guide Pagination - Modern Design */
.parent-health-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 40px auto;
  gap: 20px;
  padding: 25px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(1, 92, 146, 0.15);
  max-width: 800px;
  width: 100%;
}

.parent-health-pagination-btn {
  background: linear-gradient(135deg, #015C92 0%, #2D82B5 100%);
  color: white;
  border: none;
  border-radius: 30px;
  padding: 14px 24px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Be Vietnam Pro', sans-serif;
  box-shadow: 0 6px 20px rgba(1, 92, 146, 0.3);
  min-width: 140px;
  justify-content: center;
}

.parent-health-pagination-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2D82B5 0%, #428CD4 100%);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(1, 92, 146, 0.4);
}

.parent-health-pagination-btn:disabled {
  background: linear-gradient(135deg, #9ca3af, #d1d5db);
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.parent-health-pagination-info {
  display: flex;
  align-items: center;
  justify-content: center;
}

.parent-health-pagination-pages {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(1, 92, 146, 0.08);
  padding: 12px 20px;
  border-radius: 25px;
  border: 1px solid rgba(1, 92, 146, 0.2);
}

.parent-health-page-number {
  background: transparent;
  border: none;
  color: #374151;
  font-size: 1rem;
  font-weight: 500;
  padding: 10px 14px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Be Vietnam Pro', sans-serif;
  min-width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.parent-health-page-number:hover {
  background: rgba(1, 92, 146, 0.15);
  color: #015C92;
  transform: scale(1.1);
}

.parent-health-page-number.active {
  background: linear-gradient(135deg, #015C92 0%, #2D82B5 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(1, 92, 146, 0.4);
  transform: scale(1.1);
}

.parent-health-page-ellipsis {
  color: #6b7280;
  font-size: 1.2rem;
  font-weight: bold;
  padding: 0 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Responsive Design for Parent Health Pagination */
@media (max-width: 768px) {
  .parent-health-pagination {
    margin: 30px auto;
    padding: 20px;
    gap: 15px;
    max-width: 95%;
  }

  .parent-health-pagination-btn {
    padding: 12px 20px;
    font-size: 0.9rem;
    min-width: 120px;
  }

  .parent-health-pagination-pages {
    padding: 10px 16px;
  }

  .parent-health-page-number {
    min-width: 40px;
    height: 40px;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .parent-health-pagination {
    flex-direction: column;
    gap: 15px;
    margin: 25px auto;
    padding: 18px;
    max-width: 98%;
  }

  .parent-health-pagination-btn {
    width: 100%;
    justify-content: center;
    padding: 14px 24px;
    min-width: auto;
  }

  .parent-health-pagination-info {
    order: -1;
    width: 100%;
  }

  .parent-health-pagination-pages {
    justify-content: center;
    width: 100%;
    padding: 12px 20px;
  }

  .parent-health-page-number {
    min-width: 36px;
    height: 36px;
    font-size: 0.85rem;
  }
}

/* Keyframes Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive styles */
@media (max-width: 992px) {
  .health-guide-content {
    grid-template-columns: 1fr;
    gap: 25px;
  }
  
  .health-guide-sidebar {
    position: static;
    top: 0;
  }
}

@media (max-width: 768px) {
  .health-guide-header {
    flex-direction: column;
    align-items: center;
    padding: 30px 20px;
  }
  
  .articles-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
  
  .pagination {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }
}

@media (max-width: 576px) {
  .articles-grid {
    grid-template-columns: 1fr;
  }
  
  .article-image {
    height: 200px;
  }
  
  .health-guide-header h1 {
    font-size: 1.8rem;
  }
}