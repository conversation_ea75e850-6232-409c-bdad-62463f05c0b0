<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Date Format Function</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .input {
            font-weight: bold;
            color: #333;
        }
        .output {
            color: #007bff;
            margin-left: 20px;
        }
        .error {
            color: #dc3545;
        }
        .success {
            color: #28a745;
        }
    </style>
</head>
<body>
    <h1>🧪 Test Date Format Function</h1>
    <p>This page tests the formatDate function with various inputs including the problematic number formats.</p>
    
    <div id="results"></div>

    <script>
        const formatDate = (dateString) => {
            if (!dateString) return '';

            try {
                console.log('🔍 formatDate input:', dateString, 'type:', typeof dateString);

                // Handle different date formats
                let date;

                // If it's already a Date object
                if (dateString instanceof Date) {
                    date = dateString;
                    console.log('📅 Detected: Date object');
                }
                // If it's an array (LocalDate from Java backend)
                else if (Array.isArray(dateString)) {
                    console.log('📅 Detected: Array format (LocalDate from Java)');
                    console.log('📅 Array content:', dateString);

                    if (dateString.length >= 3) {
                        // Array format: [year, month, day] - month is 1-based in Java
                        const [year, month, day] = dateString;
                        console.log(`📅 Array format: ${year}-${month}-${day}`);
                        date = new Date(year, month - 1, day); // Convert to 0-based month for JS
                    } else {
                        console.warn('❌ Invalid array format:', dateString);
                        return dateString.toString();
                    }
                }
                // If it's a timestamp (number)
                else if (typeof dateString === 'number') {
                    console.log('🔢 Detected: Number format');
                    // Handle special case: numbers like 202611, 2025315 (YYYYMM or YYYYMMD format)
                    const numStr = dateString.toString();
                    console.log(`🔢 Number string: ${numStr}, length: ${numStr.length}`);
                    
                    if (numStr.length === 6) {
                        // YYYYMM format (202611 = 2026-11)
                        const year = numStr.substring(0, 4);
                        const month = numStr.substring(4, 6);
                        console.log(`📅 YYYYMM format: ${year}-${month}`);
                        date = new Date(year, month - 1, 1); // First day of the month
                    } else if (numStr.length === 7) {
                        // YYYYMMD format (2025315 = 2025-3-15)
                        const year = numStr.substring(0, 4);
                        const month = numStr.substring(4, 5);
                        const day = numStr.substring(5, 7);
                        console.log(`📅 YYYYMMD format: ${year}-${month}-${day}`);
                        date = new Date(year, month - 1, day);
                    } else if (numStr.length === 8) {
                        // YYYYMMDD format (20251231 = 2025-12-31)
                        const year = numStr.substring(0, 4);
                        const month = numStr.substring(4, 6);
                        const day = numStr.substring(6, 8);
                        console.log(`📅 YYYYMMDD format: ${year}-${month}-${day}`);
                        date = new Date(year, month - 1, day);
                    } else {
                        // Regular timestamp
                        console.log('📅 Regular timestamp');
                        date = new Date(dateString);
                    }
                }
                // If it's a string
                else if (typeof dateString === 'string') {
                    console.log('📝 Detected: String format');
                    // Check for various string formats
                    if (dateString.includes('T')) {
                        // ISO format like "2023-07-01T01:00:00.000+00:00"
                        console.log('📅 ISO format detected');
                        date = new Date(dateString);
                    } else if (dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
                        // YYYY-MM-DD format (LocalDate from Java backend)
                        console.log('📅 YYYY-MM-DD format (LocalDate)');
                        const [year, month, day] = dateString.split('-');
                        date = new Date(year, month - 1, day);
                    } else {
                        // Try to parse as is
                        console.log('📅 Attempting generic parse');
                        date = new Date(dateString);
                    }
                } else {
                    console.warn('❌ Unknown date format:', dateString);
                    return dateString;
                }

                // Check if date is valid
                if (isNaN(date.getTime())) {
                    console.warn('❌ Invalid date created from:', dateString);
                    return dateString;
                }

                // Format as DD/MM/YYYY
                const formatted = date.toLocaleDateString('vi-VN', {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric'
                });

                console.log('✅ formatDate result:', formatted, 'from input:', dateString);
                return formatted;
            } catch (err) {
                console.error('❌ Error formatting date:', err, 'Input:', dateString);
                return dateString;
            }
        };

        // Test cases based on the actual data format from backend
        const testCases = [
            // NEW: Array format from Java LocalDate (actual backend format)
            { input: [2026, 1, 1], description: "Array format from Java LocalDate [2026, 1, 1]" },
            { input: [2025, 3, 15], description: "Array format from Java LocalDate [2025, 3, 15]" },
            { input: [2027, 2, 1], description: "Array format from Java LocalDate [2027, 2, 1]" },
            { input: [2025, 4, 10], description: "Array format from Java LocalDate [2025, 4, 10]" },
            { input: [2025, 5, 20], description: "Array format from Java LocalDate [2025, 5, 20]" },
            { input: [2026, 6, 1], description: "Array format from Java LocalDate [2026, 6, 1]" },
            { input: [2025, 8, 10], description: "Array format from Java LocalDate [2025, 8, 10]" },
            { input: [2025, 9, 1], description: "Array format from Java LocalDate [2025, 9, 1]" },
            { input: [2025, 11, 1], description: "Array format from Java LocalDate [2025, 11, 1]" },

            // Previous test cases (for backward compatibility)
            { input: 202611, description: "YYYYMM format (from image)" },
            { input: 2025315, description: "YYYYMMD format (from image)" },
            { input: "2025-03-15", description: "Expected LocalDate string format" },
            { input: "2025-12-31", description: "Expected LocalDate string format" },
            { input: null, description: "Null value" },
            { input: "", description: "Empty string" },

            // Edge cases for array format
            { input: [2025], description: "Invalid array (too short)" },
            { input: [2025, 13, 1], description: "Invalid month in array" },
            { input: [2025, 2, 30], description: "Invalid day in array" },
        ];

        // Run tests and display results
        const resultsDiv = document.getElementById('results');
        
        testCases.forEach((testCase, index) => {
            const div = document.createElement('div');
            div.className = 'test-case';
            
            try {
                const result = formatDate(testCase.input);
                div.innerHTML = `
                    <div class="input">Test ${index + 1}: ${testCase.description}</div>
                    <div>Input: <code>${JSON.stringify(testCase.input)}</code> (${typeof testCase.input})</div>
                    <div class="output success">Output: <strong>${result}</strong></div>
                `;
            } catch (error) {
                div.innerHTML = `
                    <div class="input">Test ${index + 1}: ${testCase.description}</div>
                    <div>Input: <code>${JSON.stringify(testCase.input)}</code> (${typeof testCase.input})</div>
                    <div class="output error">Error: ${error.message}</div>
                `;
            }
            
            resultsDiv.appendChild(div);
        });

        console.log('🧪 All tests completed. Check the page and console for results.');
    </script>
</body>
</html>
