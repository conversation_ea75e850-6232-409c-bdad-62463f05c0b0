/* Edit Health Article Styles */

/* Image Preview */
.image-preview {
  max-height: 200px;
  max-width: 100%;
  object-fit: cover;
}

/* Form Container */
.edit-article-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
  padding: 20px 0;
}

.edit-article-card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  border: none;
  overflow: hidden;
}

.edit-article-header {
  background: linear-gradient(135deg, #fd7e14 0%, #e55a4e 100%);
  color: white;
  padding: 24px;
  border-bottom: none;
}

.edit-article-title {
  color: white;
  font-weight: 700;
  font-size: 1.5rem;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.edit-article-body {
  padding: 32px;
}

/* Form Groups */
.form-group {
  margin-bottom: 24px;
}

.form-label {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 1rem;
}

.form-control {
  border-radius: 8px;
  border: 2px solid #e9ecef;
  padding: 12px 16px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #fd7e14;
  box-shadow: 0 0 0 0.2rem rgba(253, 126, 20, 0.25);
}

.form-select {
  border-radius: 8px;
  border: 2px solid #e9ecef;
  padding: 12px 16px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-select:focus {
  border-color: #fd7e14;
  box-shadow: 0 0 0 0.2rem rgba(253, 126, 20, 0.25);
}

/* Textarea */
.form-control[rows] {
  resize: vertical;
  min-height: 120px;
}

/* Image Upload Section */
.image-upload-section {
  background: linear-gradient(135deg, #fff3e0 0%, #ffffff 100%);
  border: 2px dashed #fd7e14;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  transition: all 0.3s ease;
}

.image-upload-section:hover {
  border-color: #e55a4e;
  background: linear-gradient(135deg, #fff0e6 0%, #ffffff 100%);
}

.image-upload-icon {
  font-size: 3rem;
  color: #fd7e14;
  margin-bottom: 16px;
}

.image-upload-text {
  color: #6c757d;
  font-size: 1.1rem;
  margin-bottom: 16px;
}

.image-preview-container {
  margin-top: 20px;
  text-align: center;
}

.image-preview-wrapper {
  position: relative;
  display: inline-block;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.current-image-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  margin-bottom: 20px;
}

.current-image-title {
  color: #495057;
  font-weight: 600;
  margin-bottom: 16px;
  font-size: 1.1rem;
}

/* Tags Input */
.tags-input-container {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 8px;
  min-height: 50px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  transition: all 0.3s ease;
}

.tags-input-container:focus-within {
  border-color: #fd7e14;
  box-shadow: 0 0 0 0.2rem rgba(253, 126, 20, 0.25);
}

.tag-item {
  background: linear-gradient(135deg, #fd7e14 0%, #e55a4e 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tag-remove {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  font-size: 1rem;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.tag-remove:hover {
  opacity: 1;
}

.tag-input {
  border: none;
  outline: none;
  flex: 1;
  min-width: 120px;
  padding: 8px;
  font-size: 1rem;
}

/* Buttons */
.btn-warning-custom {
  background: linear-gradient(135deg, #fd7e14 0%, #e55a4e 100%);
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  min-width: 120px;
}

.btn-warning-custom:hover {
  background: linear-gradient(135deg, #e55a4e 0%, #dc3545 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(253, 126, 20, 0.4);
}

.btn-secondary-custom {
  background: transparent;
  border: 2px solid #6c757d;
  color: #6c757d;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  min-width: 120px;
}

.btn-secondary-custom:hover {
  background: #6c757d;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
}

/* Loading State */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-content {
  background: white;
  padding: 40px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border-width: 3px;
  margin-bottom: 16px;
}

.loading-text {
  color: #6c757d;
  font-size: 1.1rem;
  font-weight: 500;
}

/* Validation Messages */
.invalid-feedback {
  color: #dc3545;
  font-size: 0.9rem;
  margin-top: 4px;
}

.is-invalid {
  border-color: #dc3545;
}

.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Alert Messages */
.alert-info-custom {
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
  border: 1px solid #b6d4da;
  color: #0c5460;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.alert-info-custom .alert-heading {
  color: #0c5460;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .edit-article-body {
    padding: 20px;
  }
  
  .edit-article-header {
    padding: 20px;
  }
  
  .btn-warning-custom,
  .btn-secondary-custom {
    width: 100%;
    margin-bottom: 12px;
  }
  
  .image-upload-section {
    padding: 16px;
  }
  
  .tags-input-container {
    min-height: 60px;
  }
  
  .current-image-section {
    padding: 16px;
  }
}
