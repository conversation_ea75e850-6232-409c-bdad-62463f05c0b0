/* Parent Footer Styles - phFooter prefix to avoid CSS conflicts */
.phFooter {
  margin-top: -1px !important; /* -1px để loại bỏ đường viền trắng */
  padding-top: 40px;
  background: linear-gradient(135deg, #1a374d 0%, #2c3e50 100%);
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
  color: #fff;
  padding: 0;
  margin-top: 0;
  overflow: hidden;
  font-family: var(--font-body);
}

.phFooterSvg {
  height: 80px;
}

/* Wave effect at the top of footer */
.phFooterWave {
  display: block;
  line-height: 0;
  width: 100%;
  height: 50px;
  margin-top: -1px;
  margin-bottom: -1px;
}

.phFooterWave svg {
  display: block;
  width: 100%;
  height: 100%;
}

/* Footer inner container */
.phFooterInner {
  max-width: 1400px;
  margin: 0 auto;
  padding: 40px 20px;
}

/* Footer content with columns */
.phFooterContent {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 40px;
  margin-bottom: 30px;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease;
}

.phFooterContent.phFooterVisible {
  opacity: 1;
  transform: translateY(0);
}

.phFooterColumn {
  display: flex;
  flex-direction: column;
}

/* Info column - first column */
.phFooterInfoColumn {
  padding-right: 20px;
}

.phFooterLogo {
  margin-bottom: 20px;
  display: inline-block;
  transition: transform 0.3s ease;
}

.phFooterLogo img {
  max-width: 180px;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.phFooterLogo:hover img {
  transform: scale(1.05);
}

.phFooterDescription {
  margin-bottom: 20px;
  color: #e0e0e0;
  font-size: 0.95rem;
  line-height: 1.6;
}

/* School address styling */
.phFooterSchoolAddress {
  font-style: normal;
  font-size: 0.95rem;
}

.phFooterAddressItem {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
}

.phFooterAddressItem i {
  margin-right: 10px;
  color: #6aafdb;
  min-width: 16px;
  margin-top: 4px;
}

.phFooterSchoolAddress a {
  color: #6aafdb;
  text-decoration: none;
  transition: color 0.2s;
}

.phFooterSchoolAddress a:hover {
  color: #fff;
  text-decoration: underline;
}

/* Footer titles */
.phFooterTitle {
  position: relative;
  font-size: 1.25rem;
  margin-bottom: 25px;
  color: #fff;
  font-weight: 600;
}

/* Add underline for title */
.phFooterTitle::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -10px;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #6aafdb, transparent);
  border-radius: 2px;
}

/* Social icons container */
.phFooterSocialIconsContainer {
  margin-bottom: 20px;
}

.phFooterSocialIcons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.phFooterSocialIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  text-decoration: none;
  transition: all 0.3s ease;
}

.phFooterSocialIcon:hover {
  transform: translateY(-3px);
}

.phFooterSocialIcon.phFooterFacebook:hover { background-color: #1877F2; }
.phFooterSocialIcon.phFooterTwitter:hover { background-color: #1DA1F2; }
.phFooterSocialIcon.phFooterInstagram:hover { background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888); }
.phFooterSocialIcon.phFooterLinkedin:hover { background-color: #0077B5; }
.phFooterSocialIcon.phFooterYoutube:hover { background-color: #FF0000; }

.phFooterConnectDescription {
  color: #e0e0e0;
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Contact info styling */
.phFooterContactInfo {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.phFooterContactItem {
  display: flex;
  align-items: center;
}

.phFooterContactIcon {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: #6aafdb;
  transition: all 0.3s ease;
}

.phFooterContactItem:hover .phFooterContactIcon {
  background: rgba(106, 175, 219, 0.2);
  transform: scale(1.05);
}

.phFooterContactDetail {
  flex: 1;
}

.phFooterContactLabel {
  font-size: 0.85rem;
  color: #aaa;
  margin-bottom: 3px;
}

.phFooterContactValue {
  font-weight: 600;
  color: #fff;
}

.phFooterContactValue a {
  color: inherit;
  text-decoration: none;
  transition: color 0.2s;
}

.phFooterContactValue a:hover {
  color: #6aafdb;
}

/* Divider line */
.phFooterDivider {
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  margin: 20px 0;
}

/* Footer bottom */
.phFooterBottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
  font-size: 0.9rem;
}

.phFooterCopyright {
  color: #a0a0a0;
}

.phFooterCopyright a {
  color: #6aafdb;
  text-decoration: none;
  transition: color 0.2s;
}

.phFooterCopyright a:hover {
  color: #fff;
  text-decoration: underline;
}

.phFooterBottomLinks {
  display: flex;
  gap: 20px;
}

.phFooterBottomLink {
  color: #a0a0a0;
  text-decoration: none;
  transition: color 0.2s;
}

.phFooterBottomLink:hover {
  color: #fff;
}

/* Responsive styles */
@media (max-width: 992px) {
  .phFooterContent {
    grid-template-columns: 1fr 1fr;
  }

  .phFooterContactColumn {
    grid-column: span 2;
    margin-top: 20px;
  }
}

@media (max-width: 768px) {
  .phFooterContent {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .phFooterContactColumn {
    grid-column: auto;
  }

  .phFooterBottom {
    flex-direction: column;
    text-align: center;
  }

  .phFooterBottomLinks {
    justify-content: center;
  }

  .phFooterContactInfo {
    margin-top: 10px;
  }

  .phFooterInfoColumn {
    padding-right: 0;
  }
}
