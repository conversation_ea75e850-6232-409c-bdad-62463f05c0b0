/* Edit User Modal styling */

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.edit-user-modal {
  background: white;
  border-radius: 15px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 30px;
  border-bottom: 1px solid #eee;
  background: linear-gradient(135deg, #1a7bb5, #50c9c3);
  color: white;
  border-radius: 15px 15px 0 0;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(90deg);
}

.modal-body {
  padding: 15px 25px; /* Giảm padding để form gọn hơn */
}

.user-summary {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  margin-bottom: 25px;
}

.user-avatar {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #1a7bb5, #50c9c3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.8rem;
}

.user-details h3 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 1.2rem;
}

.user-details p {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 0.9rem;
  font-family: "Courier New", monospace;
}

.role-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.role-badge.admin {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.role-badge.nurse {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.role-badge.parent {
  background: rgba(26, 123, 181, 0.1);
  color: #1a7bb5;
}

.edit-form {
  display: flex;
  flex-direction: column;
  gap: 2px; /* Giảm từ 8px xuống 6px */
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 1px; /* Giảm từ 2px xuống 1px */
}

.form-group label {
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
  gap: 6px; /* Giảm từ 10px xuống 6px */
  font-size: 0.85rem; /* Giảm từ 0.9rem */
  margin-bottom: 2px; /* Thêm margin-bottom nhỏ */
}

.form-group label i {
  color: #1a7bb5;
  width: 16px;
}

.form-group input {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  background: white;
  width: 100%;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #1a7bb5;
  box-shadow: 0 0 0 3px rgba(26, 123, 181, 0.1);
}

.form-group input.error {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.error-message {
  color: #dc3545;
  font-size: 0.7rem; /* Giảm từ 0.75rem xuống 0.7rem */
  display: inline-block; /* Thay đổi từ flex sang inline-block */
  margin: 0;
  padding: 0;
  line-height: 1; /* Giảm line-height để nhỏ gọn hơn */
  animation: errorSlideIn 0.2s ease-out; /* Giảm thời gian animation */
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.error-message::before {
  content: "⚠ ";
  font-size: 0.7rem;
}

.form-note {
  background: rgba(26, 123, 181, 0.1);
  border: 1px solid rgba(26, 123, 181, 0.2);
  border-radius: 8px;
  padding: 15px;
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-top: 10px;
}

.form-note i {
  color: #1a7bb5;
  margin-top: 2px;
}

.form-note p {
  margin: 0;
  color: #1a7bb5;
  font-size: 0.9rem;
  line-height: 1.4;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  padding: 25px 30px;
  border-top: 1px solid #eee;
  background: #f8f9fa;
  border-radius: 0 0 15px 15px;
}

.btn {
  padding: 12px 25px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  justify-content: center;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #5a6268;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
}

.btn-primary {
  background: linear-gradient(135deg, #1a7bb5, #50c9c3);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #0a5d8f, #21d4fd);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(26, 123, 181, 0.3);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.fa-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }

  .edit-user-modal {
    max-width: 100%;
    margin: 0;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 20px;
  }

  .user-summary {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .modal-footer {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .modal-header h2 {
    font-size: 1.1rem;
  }

  .user-avatar {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }

  .form-group input {
    padding: 3px 5px;
    font-size: 0.9rem;
  }
}

.error-container {
  height: 12px; /* Giảm chiều cao để gọn hơn */
  display: flex;
  align-items: flex-start;
  overflow: hidden;
  margin: 0;
}
