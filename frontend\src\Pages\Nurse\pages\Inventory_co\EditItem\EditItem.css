/* Edit Item Modal - Namespaced Styles */
.edit-item-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1050;
  opacity: 1;
  visibility: visible;
}

.edit-item-modal-dialog {
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  margin: 1rem;
}

.edit-item-modal-content {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: 90vh;
}

.edit-item-modal-header {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: none;
}

.edit-item-modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.edit-item-btn-close {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease;
}

.edit-item-btn-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.edit-item-modal-body {
  padding: 1.5rem;
  flex: 1;
  overflow-y: auto;
}

.edit-item-form-group {
  margin-bottom: 1rem;
}

.edit-item-form-label {
  display: block;
  margin-bottom: 0.375rem;
  font-weight: 500;
  color: #495057;
  font-size: 0.875rem;
}

.edit-item-form-control {
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  box-sizing: border-box;
}

.edit-item-form-control:focus {
  border-color: #28a745;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.edit-item-form-select {
  width: 100%;
  padding: 0.5rem 2.25rem 0.5rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
  appearance: none;
  box-sizing: border-box;
}

.edit-item-form-select:focus {
  border-color: #28a745;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.edit-item-alert {
  padding: 0.75rem 1rem;
  margin-top: 0.5rem;
  border: 1px solid transparent;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.edit-item-alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.edit-item-status-indicator {
  margin-top: 0.5rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
}

.edit-item-status-available {
  color: #155724;
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
}

.edit-item-status-low {
  color: #856404;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
}

.edit-item-status-out {
  color: #721c24;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
}

.edit-item-modal-footer {
  background-color: #f8f9fa;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.edit-item-btn {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.5;
  border-radius: 0.375rem;
  border: 1px solid transparent;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.2s ease;
}

.edit-item-btn-secondary {
  background-color: #6c757d;
  border-color: #6c757d;
  color: white;
}

.edit-item-btn-secondary:hover {
  background-color: #5a6268;
  border-color: #545b62;
}

.edit-item-btn-success {
  background-color: #28a745;
  border-color: #28a745;
  color: white;
}

.edit-item-btn-success:hover {
  background-color: #218838;
  border-color: #1e7e34;
}

.edit-item-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.edit-item-spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: edit-item-spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes edit-item-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.edit-item-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -0.75rem;
}

.edit-item-col-6 {
  flex: 0 0 50%;
  max-width: 50%;
  padding: 0 0.75rem;
}

.edit-item-col-12 {
  flex: 0 0 100%;
  max-width: 100%;
  padding: 0 0.75rem;
}

/* Utility Classes */
.edit-item-me-1 { margin-right: 0.25rem; }
.edit-item-me-2 { margin-right: 0.5rem; }
.edit-item-text-danger { color: #dc3545; }
.edit-item-text-muted { color: #6c757d; }

@media (max-width: 768px) {
  .edit-item-col-6 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .edit-item-modal-dialog {
    width: 95%;
    margin: 0.5rem;
  }

  .edit-item-modal-body {
    padding: 1rem;
  }
}
