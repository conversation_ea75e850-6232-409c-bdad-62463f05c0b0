/* ===== MODERN USER MANAGEMENT DESIGN SYSTEM ===== */

:root {
  /* Modern Color Palette */
  --color-white: #ffffff;
  --color-gray-50: #f8fafc;
  --color-gray-100: #f1f5f9;
  --color-gray-200: #e2e8f0;
  --color-gray-300: #cbd5e1;
  --color-gray-400: #94a3b8;
  --color-gray-500: #64748b;
  --color-gray-600: #475569;
  --color-gray-700: #334155;
  --color-gray-800: #1e293b;
  --color-gray-900: #0f172a;

  /* Blue Theme Colors */
  --color-blue-50: #eff6ff;
  --color-blue-100: #dbeafe;
  --color-blue-500: #3b82f6;
  --color-blue-600: #2563eb;
  --color-blue-700: #1d4ed8;

  /* Status Colors */
  --color-green: #10b981;
  --color-green-light: #d1fae5;
  --color-red: #ef4444;
  --color-red-light: #fee2e2;
  --color-yellow: #f59e0b;
  --color-yellow-light: #fef3c7;

  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;

  /* Borders & Radius */
  --border-width: 1px;
  --border-color: var(--color-gray-200);
  --radius-sm: 0.375rem;
  --radius: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* ===== BASE LAYOUT ===== */
.admin-user-management-container,
.user-management-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--space-6);
  font-family: var(--font-family);
  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-blue-50) 100%);
  min-height: 100vh;
}

/* ===== MODERN HEADER ===== */
.admin-user-management-header,
.user-management-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-8);
  background: var(--color-white);
  padding: var(--space-8);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: none;
  position: relative;
  overflow: hidden;
}

.admin-user-management-header::before,
.user-management-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-blue-500), var(--color-blue-600), var(--color-blue-700));
}

.admin-user-management-header h1,
.user-management-header h1 {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--color-gray-900);
  margin: 0 0 var(--space-2) 0;
  letter-spacing: -0.025em;
}

.admin-user-management-header p,
.user-management-header p {
  font-size: var(--font-size-base);
  color: var(--color-gray-600);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--space-3);
  line-height: 1.6;
}

.connection-status {
  font-size: var(--font-size-xs);
  color: var(--color-green);
  font-weight: 500;
}

.user-info {
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
}

.user-info strong {
  color: var(--color-gray-700);
  text-transform: uppercase;
}

/* ===== MODERN BUTTONS ===== */
.btn-add {
  background: linear-gradient(135deg, var(--color-blue-500), var(--color-blue-600));
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-4) var(--space-6);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--space-3);
  transition: all 0.3s ease;
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.btn-add::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-add:hover::before {
  left: 100%;
}

.btn-add svg {
  font-size: 1.125rem;
}

.btn-add:hover {
  background: linear-gradient(135deg, var(--color-blue-600), var(--color-blue-700));
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.btn-add:active {
  transform: translateY(0);
}

.btn-login,
.btn-retry {
  background: var(--color-gray-700);
  color: var(--color-white);
  border: none;
  border-radius: var(--radius);
  padding: var(--space-3) var(--space-4);
  font-size: var(--font-size-sm);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--space-4);
}

.btn-login svg,
.btn-retry svg {
  font-size: 1rem;
}

.btn-login:hover,
.btn-retry:hover {
  background: var(--color-gray-600);
}

/* ===== MODERN STATS CARDS ===== */
.admin-user-management-container .user-stats,
.user-management-container .user-stats {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
  gap: var(--space-6) !important;
  margin-bottom: var(--space-8) !important;
  width: 100% !important;
}

.admin-user-management-container .user-stats .stat-card,
.user-management-container .user-stats .stat-card {
  background: var(--color-white) !important;
  border: none !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--space-6) !important;
  transition: all 0.3s ease !important;
  box-shadow: var(--shadow-md) !important;
  position: relative !important;
  overflow: hidden !important;
  cursor: pointer !important;
}

.admin-user-management-container .user-stats .stat-card::before,
.user-management-container .user-stats .stat-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 4px !important;
  background: linear-gradient(90deg, var(--color-blue-500), var(--color-blue-600)) !important;
}

.admin-user-management-container .user-stats .stat-card:hover,
.user-management-container .user-stats .stat-card:hover {
  transform: translateY(-4px) !important;
  box-shadow: var(--shadow-xl) !important;
}

.admin-user-management-container .user-stats .stat-card-content,
.user-management-container .user-stats .stat-card-content,
.admin-user-stats .stat-card-content,
.user-stats .stat-card-content {
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-start !important;
  gap: var(--space-4) !important;
  padding: 0 !important;
}

/* ===== STANDALONE STATS CLASSES ===== */
.admin-user-stats,
.user-stats {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
  gap: var(--space-6) !important;
  margin-bottom: var(--space-8) !important;
  width: 100% !important;
}

.admin-stat-card,
.stat-card {
  background: var(--color-white) !important;
  border: none !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--space-6) !important;
  transition: all 0.3s ease !important;
  box-shadow: var(--shadow-md) !important;
  position: relative !important;
  overflow: hidden !important;
  cursor: pointer !important;
}

.admin-stat-card::before,
.stat-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 4px !important;
  background: linear-gradient(90deg, var(--color-blue-500), var(--color-blue-600)) !important;
}

.admin-stat-card:hover,
.stat-card:hover {
  transform: translateY(-4px) !important;
  box-shadow: var(--shadow-xl) !important;
}

.admin-stat-card-content,
.stat-card-content {
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-start !important;
  gap: var(--space-4) !important;
  padding: 0 !important;
}

.admin-stat-icon,
.stat-icon {
  width: 48px !important;
  height: 48px !important;
  border-radius: var(--radius-lg) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1.25rem !important;
  color: var(--color-white) !important;
  flex-shrink: 0 !important;
  box-shadow: var(--shadow-sm) !important;
}

.admin-stat-icon.total,
.stat-icon.total {
  background: linear-gradient(135deg, var(--color-gray-600), var(--color-gray-700)) !important;
}

.admin-stat-icon.admin,
.stat-icon.admin {
  background: linear-gradient(135deg, var(--color-red), #dc2626) !important;
}

.admin-stat-icon.nurse,
.stat-icon.nurse {
  background: linear-gradient(135deg, var(--color-blue-500), var(--color-blue-600)) !important;
}

.admin-stat-icon.parent,
.stat-icon.parent {
  background: linear-gradient(135deg, var(--color-green), #059669) !important;
}

.admin-stat-icon.active,
.stat-icon.active {
  background: linear-gradient(135deg, var(--color-green), #059669) !important;
}

.admin-stat-icon.inactive,
.stat-icon.inactive {
  background: linear-gradient(135deg, var(--color-gray-400), var(--color-gray-500)) !important;
}

.admin-stat-info,
.stat-info {
  flex: 1 !important;
  min-width: 0 !important;
  width: 100% !important;
}

.admin-stat-label,
.stat-label {
  font-size: var(--font-size-sm) !important;
  color: var(--color-gray-600) !important;
  margin-bottom: var(--space-1) !important;
  font-weight: 500 !important;
  line-height: 1.4 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.025em !important;
}

.admin-stat-value,
.stat-value {
  font-size: var(--font-size-2xl) !important;
  font-weight: 700 !important;
  color: var(--color-gray-900) !important;
  line-height: 1 !important;
}

/* ===== MODERN FILTERS ===== */
.admin-user-filters,
.user-filters {
  background: var(--color-white);
  padding: var(--space-6);
  border-radius: var(--radius-xl);
  border: none;
  margin-bottom: var(--space-8);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--shadow-lg);
  position: relative;
  gap: var(--space-6);
}

/* ===== MODERN SEARCH BOX ===== */
.admin-user-filters .search-box,
.user-filters .search-box {
  flex: 1;
  max-width: 500px;
  position: relative;
}

.admin-user-filters .search-box input,
.user-filters .search-box input {
  width: 100% !important;
  padding: var(--space-4) var(--space-5) var(--space-4) 3rem !important;
  border: 2px solid var(--color-gray-200) !important;
  border-radius: var(--radius-lg) !important;
  font-size: var(--font-size-base) !important;
  background: var(--color-gray-50) !important;
  transition: all 0.3s ease !important;
  box-shadow: var(--shadow-sm) !important;
  font-family: inherit !important;
  line-height: 1.5 !important;
}

.admin-user-filters .search-box input:focus,
.user-filters .search-box input:focus {
  outline: none !important;
  border-color: var(--color-blue-500) !important;
  background: var(--color-white) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), var(--shadow-md) !important;
}

.admin-user-filters .search-box input::placeholder,
.user-filters .search-box input::placeholder {
  color: var(--color-gray-500) !important;
  font-size: var(--font-size-base) !important;
  opacity: 1 !important;
}

.admin-user-filters .search-box svg,
.user-filters .search-box svg {
  position: absolute;
  left: var(--space-4);
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-gray-400);
  font-size: 1.25rem;
}

/* ===== FILTER GROUP STYLES ===== */
.admin-filter-group,
.filter-group {
  display: flex;
  gap: var(--space-4);
  align-items: center;
}

.admin-filter,
.filter {
  position: relative;
}

.admin-filter select,
.filter select {
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  background: var(--color-gray-50);
  color: var(--color-gray-700);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
}

.admin-filter select:focus,
.filter select:focus {
  outline: none;
  border-color: var(--color-blue-500);
  background: var(--color-white);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* ===== SEARCH BOX STYLES ===== */
.admin-search-box,
.search-box {
  flex: 1;
  max-width: 500px;
  position: relative;
}

.admin-search-box input,
.search-box input {
  width: 100% !important;
  padding: var(--space-4) var(--space-5) var(--space-4) 3rem !important;
  border: 2px solid var(--color-gray-200) !important;
  border-radius: var(--radius-lg) !important;
  font-size: var(--font-size-base) !important;
  background: var(--color-gray-50) !important;
  transition: all 0.3s ease !important;
  box-shadow: var(--shadow-sm) !important;
  font-family: inherit !important;
  line-height: 1.5 !important;
}

.admin-search-box input:focus,
.search-box input:focus {
  outline: none !important;
  border-color: var(--color-blue-500) !important;
  background: var(--color-white) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), var(--shadow-md) !important;
}

.admin-search-box input::placeholder,
.search-box input::placeholder {
  color: var(--color-gray-500) !important;
  font-size: var(--font-size-base) !important;
  opacity: 1 !important;
}

.admin-search-box svg,
.search-box svg {
  position: absolute;
  left: var(--space-4);
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-gray-400);
  font-size: 1.25rem;
}

.admin-search-box:focus-within svg,
.search-box:focus-within svg {
  color: var(--color-blue-500);
}

.admin-user-filters .search-box:focus-within svg,
.user-filters .search-box:focus-within svg {
  color: var(--color-blue-500);
}

/* ===== CONNECTION STATUS STYLES ===== */
.admin-connection-checking,
.connection-checking {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8);
  text-align: center;
  background: var(--color-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  margin: var(--space-8) auto;
  max-width: 500px;
}

.admin-checking-spinner,
.checking-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-gray-200);
  border-top: 4px solid var(--color-blue-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.admin-auth-info,
.auth-info {
  color: var(--color-gray-600);
  font-size: var(--font-size-sm);
  margin-top: var(--space-4);
}

/* ===== ERROR STATES ===== */
.admin-auth-required,
.auth-required,
.admin-error-message,
.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8);
  text-align: center;
  background: var(--color-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  margin: var(--space-8) auto;
  max-width: 600px;
}

.admin-auth-icon,
.auth-icon,
.admin-error-icon,
.error-icon {
  font-size: 3rem;
  color: var(--color-red);
  margin-bottom: var(--space-4);
}

.admin-current-user-info,
.current-user-info {
  background: var(--color-gray-50);
  padding: var(--space-4);
  border-radius: var(--radius);
  margin: var(--space-4) 0;
}

.admin-error-details,
.error-details {
  background: var(--color-gray-50);
  padding: var(--space-4);
  border-radius: var(--radius);
  margin: var(--space-4) 0;
  text-align: left;
  width: 100%;
}

/* ===== PAGINATION STYLES ===== */
.admin-pagination-container,
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6) var(--space-8);
  background: var(--color-white);
  border-top: 1px solid var(--color-gray-200);
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
}

.admin-pagination-info,
.pagination-info {
  color: var(--color-gray-600);
  font-size: var(--font-size-sm);
}

.admin-pagination-controls,
.pagination-controls {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.admin-pagination-btn,
.pagination-btn {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--color-gray-300);
  background: var(--color-white);
  color: var(--color-gray-700);
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
}

.admin-pagination-btn:hover:not(.disabled),
.pagination-btn:hover:not(.disabled) {
  background: var(--color-blue-500);
  color: var(--color-white);
  border-color: var(--color-blue-500);
  transform: translateY(-1px);
}

.admin-pagination-btn.disabled,
.pagination-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--color-gray-100);
}

.admin-pagination-btn.active,
.pagination-btn.active {
  background: var(--color-blue-500);
  color: var(--color-white);
  border-color: var(--color-blue-500);
}

.admin-pagination-pages,
.pagination-pages {
  display: flex;
  gap: var(--space-1);
}

.admin-pagination-ellipsis,
.pagination-ellipsis {
  padding: var(--space-2) var(--space-3);
  color: var(--color-gray-500);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
}

/* ===== USER TABLE WRAPPER ===== */
.admin-user-table-wrapper,
.user-table-wrapper {
  background: var(--color-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  margin-bottom: var(--space-8);
}

/* ===== BUTTON STYLES ===== */
.admin-btn-add,
.btn-add {
  background: linear-gradient(135deg, var(--color-blue-500), var(--color-blue-600));
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-3) var(--space-6);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  transition: all 0.3s ease;
  box-shadow: var(--shadow-md);
}

.admin-btn-add:hover,
.btn-add:hover {
  background: linear-gradient(135deg, var(--color-blue-600), var(--color-blue-700));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.admin-btn-add:disabled,
.btn-add:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.admin-btn-retry,
.btn-retry {
  background: linear-gradient(135deg, var(--color-yellow), #f59e0b);
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-3) var(--space-4);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  transition: all 0.3s ease;
  margin-top: var(--space-4);
}

.admin-btn-retry:hover,
.btn-retry:hover {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.admin-btn-login,
.btn-login {
  background: linear-gradient(135deg, var(--color-blue-500), var(--color-blue-600));
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-3) var(--space-4);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  transition: all 0.3s ease;
  margin-top: var(--space-4);
}

.admin-btn-login:hover,
.btn-login:hover {
  background: linear-gradient(135deg, var(--color-blue-600), var(--color-blue-700));
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* ===== FILTER GROUP STYLES ===== */
.user-filters .filter-group {
  display: flex !important;
  flex-direction: column !important;
  gap: 12px !important;
  flex-shrink: 0 !important;
  align-items: flex-end !important;
  margin-right: calc(-1 * var(--space-4)) !important;
  padding-right: calc(var(--space-4) + 20px) !important;
}

.user-filters .filter {
  position: relative;
}

/* Custom select styling with dropdown arrow */
.user-filters .filter select {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  padding: 12px 40px 12px 16px !important;
  border: 2px solid var(--color-gray-200) !important;
  border-radius: 8px !important;
  background: var(--color-white) !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23737373' d='M6 8.825L1.175 4 2.6 2.575 6 5.975 9.4 2.575 10.825 4z'/%3E%3C/svg%3E") !important;
  background-repeat: no-repeat !important;
  background-position: right 12px center !important;
  background-size: 12px !important;
  font-size: var(--font-size-sm) !important;
  min-width: 160px !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04) !important;
  color: var(--color-gray-700) !important;
  font-weight: 500 !important;
  font-family: inherit !important;
  line-height: 1.5 !important;
}

.user-filters .filter select:focus {
  outline: none !important;
  border-color: var(--color-blue) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 4px 12px rgba(0, 0, 0, 0.08) !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%233b82f6' d='M6 8.825L1.175 4 2.6 2.575 6 5.975 9.4 2.575 10.825 4z'/%3E%3C/svg%3E") !important;
}

.user-filters .filter select:hover {
  border-color: var(--color-gray-300) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06) !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23525252' d='M6 8.825L1.175 4 2.6 2.575 6 5.975 9.4 2.575 10.825 4z'/%3E%3C/svg%3E") !important;
}

/* Firefox specific select styling */
.user-filters .filter select::-moz-focus-inner {
  border: 0 !important;
}

/* IE specific select styling */
.user-filters .filter select::-ms-expand {
  display: none !important;
}

/* ===== TABLE WRAPPER ===== */
.user-table-wrapper {
  background: var(--color-white);
  border-radius: var(--radius);
  border: var(--border-width) solid var(--border-color);
  overflow: hidden;
}

/* ===== LOADING & ERROR STATES ===== */
.connection-checking,
.auth-required,
.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  background: var(--color-white);
  border-radius: var(--radius);
  padding: var(--space-8);
  border: var(--border-width) solid var(--border-color);
}

.checking-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-gray-200);
  border-top: 3px solid var(--color-gray-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.connection-checking h3,
.auth-required h3,
.error-message h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0 0 var(--space-3) 0;
}

.connection-checking p,
.auth-required p,
.error-message p {
  color: var(--color-gray-600);
  margin: 0 0 var(--space-3) 0;
  line-height: 1.5;
}

.auth-icon,
.error-icon {
  font-size: 3rem;
  color: var(--color-gray-400);
  margin-bottom: var(--space-4);
}

.current-user-info,
.error-details {
  background: var(--color-gray-50);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--radius);
  padding: var(--space-4);
  margin: var(--space-4) 0;
  text-align: left;
  max-width: 500px;
}

.error-details ul {
  margin: var(--space-3) 0;
  padding-left: var(--space-4);
}

.error-details li {
  margin: var(--space-1) 0;
  color: var(--color-gray-600);
}

/* ===== ADMIN HEADER CONSISTENCY ===== */
/* .admin-header {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  background: var(--color-white) !important;
  height: auto !important;
  padding: var(--space-6) !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  width: 100% !important;
  z-index: 100 !important;
  position: relative !important;
  border-radius: var(--radius) !important;
  border: var(--border-width) solid var(--border-color) !important;
  margin-bottom: var(--space-4) !important;
  font-family: var(--font-family) !important;
} */

/* .admin-header h1,
.admin-header .header-title {
  font-size: var(--font-size-2xl) !important;
  font-weight: 600 !important;
  color: var(--color-gray-900) !important;
  margin: 0 !important;
}

.admin-header .header-info,
.admin-header p {
  font-size: var(--font-size-sm) !important;
  color: var(--color-gray-600) !important;
  margin: 0 !important;
}

.admin-header .user-info {
  font-size: var(--font-size-xs) !important;
  color: var(--color-gray-500) !important;
}

.admin-header .user-info strong {
  color: var(--color-gray-700) !important;
  text-transform: uppercase !important;
} */

/* ===== RESPONSIVE ===== */
@media (max-width: 1024px) {
  .admin-user-stats .stat-card,
  .user-stats .stat-card {
    flex: 1 1 calc((100% - 32px) / 4) !important;
    max-width: calc((100% - 32px) / 4) !important;
  }
}

@media (max-width: 768px) {
  .user-management-container {
    padding: var(--space-4);
  }

  .user-management-header {
    flex-direction: column;
    gap: var(--space-4);
  }

  .user-filters {
    flex-direction: column;
    align-items: stretch;
    padding: var(--space-3);
    gap: var(--space-3);
  }

  .user-filters .search-box {
    max-width: none;
  }

  .user-filters .filter-group {
    flex-direction: row;
    justify-content: center;
    gap: var(--space-2);
  }

  .user-filters .filter select {
    min-width: 140px !important;
    flex: 1;
  }

  .admin-user-stats .stat-card,
  .user-stats .stat-card {
    flex: 1 1 calc((100% - 16px) / 3) !important;
    max-width: calc((100% - 16px) / 3) !important;
    padding: 6px 8px !important;
  }

  .admin-stat-icon,
  .stat-icon {
    width: 18px !important;
    height: 18px !important;
    font-size: 0.6rem !important;
  }

  .admin-stat-label,
  .stat-label {
    font-size: 0.55rem !important;
  }

  .admin-stat-value,
  .stat-value {
    font-size: 0.8rem !important;
  }

  /* Pagination responsive styles */
  .pagination-container {
    flex-direction: column;
    gap: var(--space-3);
    padding: var(--space-3);
  }

  .pagination-controls {
    justify-content: center;
  }

  .pagination-pages {
    margin: 0 var(--space-2);
  }

  .pagination-btn,
  .pagination-page {
    width: 32px;
    height: 32px;
    min-width: 32px;
    font-size: var(--font-size-xs);
  }

  .pagination-btn {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .admin-user-stats .stat-card,
  .user-stats .stat-card {
    flex: 1 1 calc((100% - 8px) / 2) !important;
    max-width: calc((100% - 8px) / 2) !important;
    padding: 6px 8px !important;
  }

  .admin-stat-card-content,
  .stat-card-content {
    flex-direction: column !important;
    text-align: center !important;
    gap: 4px !important;
  }

  .admin-stat-icon,
  .stat-icon {
    width: 16px !important;
    height: 16px !important;
    font-size: 0.55rem !important;
  }

  .admin-stat-label,
  .stat-label {
    font-size: 0.5rem !important;
    text-align: center !important;
  }

  .admin-stat-value,
  .stat-value {
    font-size: 0.75rem !important;
    text-align: center !important;
  }

  /* Mobile pagination styles */
  .pagination-pages {
    flex-wrap: wrap;
    justify-content: center;
    margin: 0 var(--space-1);
  }

  .pagination-ellipsis {
    width: 28px;
    height: 28px;
    min-width: 28px;
    font-size: var(--font-size-xs);
  }
}

@media (min-width: 1200px) {
  .admin-user-stats .stat-card,
  .user-stats .stat-card {
    flex: 1 1 calc((100% - 56px) / 8) !important;
    max-width: calc((100% - 56px) / 8) !important;
  }
}

/* ===== FINAL OVERRIDES FOR CONSISTENT SIZING ===== */
.admin-user-management-container .user-stats .stat-card,
.user-management-container .user-stats .stat-card,
.admin-user-management-container div.user-stats div.stat-card,
.user-management-container div.user-stats div.stat-card,
.admin-user-stats .stat-card,
.user-stats .stat-card {
  box-sizing: border-box !important;
  overflow: hidden !important;
}

.admin-user-management-container .user-stats .stat-card .stat-info,
.user-management-container .user-stats .stat-card .stat-info,
.admin-user-management-container div.user-stats div.stat-card div.stat-info,
.user-management-container div.user-stats div.stat-card div.stat-info,
.admin-user-stats .stat-card .stat-info,
.user-stats .stat-card .stat-info {
  min-width: 0 !important;
  overflow: hidden !important;
}

/* ===== PAGINATION STYLES ===== */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4) var(--space-6);
  background: var(--color-white);
  border-top: var(--border-width) solid var(--border-color);
  font-size: var(--font-size-sm);
}

.pagination-info {
  color: var(--color-gray-600);
  font-weight: 500;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: var(--border-width) solid var(--border-color);
  background: var(--color-white);
  color: var(--color-gray-700);
  font-size: 1.2rem;
  font-weight: 600;
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(.disabled) {
  background: var(--color-gray-100);
  border-color: var(--color-gray-300);
  color: var(--color-gray-900);
}

.pagination-btn.disabled {
  color: var(--color-gray-400);
  cursor: not-allowed;
  opacity: 0.5;
}

.pagination-pages {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  margin: 0 var(--space-3);
}

.pagination-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  padding: 0 var(--space-2);
  border: var(--border-width) solid var(--border-color);
  background: var(--color-white);
  color: var(--color-gray-700);
  font-size: var(--font-size-sm);
  font-weight: 500;
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-page:hover {
  background: var(--color-gray-100);
  border-color: var(--color-gray-300);
  color: var(--color-gray-900);
}

.pagination-page.active {
  background: var(--color-gray-900);
  border-color: var(--color-gray-900);
  color: var(--color-white);
  font-weight: 600;
}

.pagination-ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  color: var(--color-gray-500);
  font-weight: 600;
  font-size: var(--font-size-sm);
}

/* ===== ADDITIONAL ROBUST STYLING FOR FILTERS & SEARCH ===== */
/* Multiple selector patterns to ensure styles are applied */

/* Direct targeting of user-filters container elements */
div.user-filters input[type="text"] {
  border: 2px solid var(--color-gray-200) !important;
  border-radius: 8px !important;
  padding: 12px 16px 12px 44px !important;
  background: var(--color-white) !important;
  font-size: var(--font-size-sm) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04) !important;
  transition: all 0.3s ease !important;
}

div.user-filters input[type="text"]:focus {
  border-color: var(--color-blue) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 4px 12px rgba(0, 0, 0, 0.08) !important;
  outline: none !important;
}

/* Direct targeting of select elements in user-filters */
div.user-filters select {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  border: 2px solid var(--color-gray-200) !important;
  border-radius: 8px !important;
  padding: 12px 40px 12px 16px !important;
  background: var(--color-white) !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23737373' d='M6 8.825L1.175 4 2.6 2.575 6 5.975 9.4 2.575 10.825 4z'/%3E%3C/svg%3E") !important;
  background-repeat: no-repeat !important;
  background-position: right 12px center !important;
  background-size: 12px !important;
  font-size: var(--font-size-sm) !important;
  min-width: 160px !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04) !important;
  color: var(--color-gray-700) !important;
  font-weight: 500 !important;
}

div.user-filters select:focus {
  border-color: var(--color-blue) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 4px 12px rgba(0, 0, 0, 0.08) !important;
  outline: none !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%233b82f6' d='M6 8.825L1.175 4 2.6 2.575 6 5.975 9.4 2.575 10.825 4z'/%3E%3C/svg%3E") !important;
}

div.user-filters select:hover {
  border-color: var(--color-gray-300) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06) !important;
}

/* IE and Edge select dropdown arrow removal */
div.user-filters select::-ms-expand {
  display: none !important;
}

/* Firefox select styling */
div.user-filters select::-moz-focus-inner {
  border: 0 !important;
  padding: 0 !important;
}

/* Force modern styling over browser defaults */
.user-management-container .user-filters .search-box input,
.user-management-container .user-filters .filter select,
.user-management-container .user-filters select {
  border-style: solid !important;
  border-width: 2px !important;
  border-color: var(--color-gray-200) !important;
  border-radius: 8px !important;
  font-family: inherit !important;
}