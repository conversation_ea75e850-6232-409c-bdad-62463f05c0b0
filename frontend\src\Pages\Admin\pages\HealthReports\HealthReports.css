/* Health Reports Page */
.admin_ui_health_reports {
  padding: 0;
  background: #f8fafc;
  min-height: 100vh;
}

/* Header Section */
.admin_ui_reports_header {
  background: linear-gradient(135deg, #334155 0%, #475569 100%);
  color: white;
  padding: 32px;
  border-radius: 16px;
  margin-bottom: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.admin_ui_reports_header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.admin_ui_header_content {
  position: relative;
  z-index: 1;
}

.admin_ui_header_content h1 {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.admin_ui_header_content p {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
  max-width: 600px;
  line-height: 1.5;
}

.admin_ui_reports_badge {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  padding: 16px 24px;
  border-radius: 12px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 1;
}

.admin_ui_badge_count {
  display: block;
  font-size: 32px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.admin_ui_badge_label {
  font-size: 12px;
  opacity: 0.8;
  font-weight: 500;
  letter-spacing: 1px;
}

/* Reports Section */
.admin_ui_reports_section {
  background: white;
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.admin_ui_reports_section h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 24px 0;
}

/* Report Types Grid */
.admin_ui_report_types_grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.admin_ui_report_type_card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 16px;
  min-height: 120px;
}

.admin_ui_report_type_card:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.admin_ui_report_type_card.admin_ui_selected {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #dbeafe 0%, #eff6ff 100%);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
}

.admin_ui_report_icon {
  font-size: 32px;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border-radius: 12px;
  flex-shrink: 0;
}

.admin_ui_selected .admin_ui_report_icon {
  background: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.admin_ui_report_content {
  flex: 1;
}

.admin_ui_report_content h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.admin_ui_report_content p {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  line-height: 1.4;
}

.admin_ui_selected_indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

/* Action Section */
.admin_ui_action_section {
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
}

.admin_ui_create_report_btn {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.admin_ui_create_report_btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.admin_ui_create_report_btn:active {
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin_ui_reports_header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
  
  .admin_ui_header_content h1 {
    font-size: 24px;
  }
  
  .admin_ui_header_content p {
    font-size: 14px;
  }
  
  .admin_ui_reports_section {
    padding: 20px;
  }
  
  .admin_ui_report_types_grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .admin_ui_report_type_card {
    padding: 20px;
    min-height: auto;
  }
  
  .admin_ui_report_icon {
    font-size: 24px;
    width: 48px;
    height: 48px;
  }
  
  .admin_ui_create_report_btn {
    padding: 14px 24px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .admin_ui_report_type_card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .admin_ui_report_icon {
    align-self: center;
  }
}
