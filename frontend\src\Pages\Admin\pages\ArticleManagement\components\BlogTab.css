/* ===== BLOG TAB ===== */
.admin-blog-tab {
  padding: var(--article-space-6);
}

/* ===== HEADER ===== */
.admin-blog-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: var(--article-space-6);
}

.admin-btn-add-blog {
  display: flex;
  align-items: center;
  gap: var(--article-space-2);
  padding: var(--article-space-3) var(--article-space-5);
  background: var(--article-secondary);
  color: var(--article-white);
  border: none;
  border-radius: var(--article-radius);
  font-size: var(--article-font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: var(--article-shadow-sm);
}

.admin-btn-add-blog:hover {
  background: #059669;
  box-shadow: var(--article-shadow-md);
  transform: translateY(-1px);
}

.admin-btn-add-blog:active {
  transform: translateY(0);
  box-shadow: var(--article-shadow-sm);
}

/* ===== FILTERS ===== */
.admin-blog-filters {
  display: flex;
  gap: var(--article-space-4);
  margin-bottom: var(--article-space-6);
  align-items: center;
  flex-wrap: wrap;
}

/* ===== BLOG TABLE ===== */
.admin-blog-table-container {
  background: var(--article-white);
  border-radius: var(--article-radius-xl);
  box-shadow: var(--article-shadow);
  overflow: hidden;
  border: 1px solid var(--article-gray-200);
}

.admin-blog-table {
  width: 100%;
  border-collapse: collapse;
}

.admin-blog-table thead {
  background: linear-gradient(135deg, var(--article-secondary), #059669);
  color: var(--article-white);
}

.admin-blog-table th {
  padding: var(--article-space-4);
  text-align: left;
  font-weight: 600;
  font-size: var(--article-font-size-sm);
  border-bottom: 2px solid var(--article-gray-200);
}

.admin-blog-table td {
  padding: var(--article-space-4);
  border-bottom: 1px solid var(--article-gray-100);
  vertical-align: middle;
}

.admin-blog-table tbody tr:hover {
  background: var(--article-gray-50);
}

.admin-blog-table tbody tr.pinned-row {
  background: linear-gradient(135deg, #fffbeb, var(--article-white));
}

/* ===== TABLE CELL COMPONENTS ===== */
.admin-blog-title-cell {
  display: flex;
  align-items: center;
  gap: var(--article-space-2);
}

.pin-icon {
  color: var(--article-warning);
  font-size: var(--article-font-size-sm);
}

.blog-title {
  font-weight: 600;
  color: var(--article-gray-900);
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.admin-author-cell {
  display: flex;
  align-items: center;
  gap: var(--article-space-2);
  font-size: var(--article-font-size-sm);
}

.admin-author-cell svg {
  color: var(--article-gray-400);
}

.admin-role-badge {
  background: var(--article-primary);
  color: var(--article-white);
  padding: var(--article-space-1) var(--article-space-2);
  border-radius: var(--article-radius);
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  margin-left: var(--article-space-1);
}

.admin-category-badge {
  background: linear-gradient(135deg, var(--article-secondary), #059669);
  color: var(--article-white);
  padding: var(--article-space-1) var(--article-space-3);
  border-radius: var(--article-radius);
  font-size: var(--article-font-size-xs);
  font-weight: 600;
  display: inline-block;
}

.admin-date-cell {
  display: flex;
  align-items: center;
  gap: var(--article-space-2);
  font-size: var(--article-font-size-sm);
  color: var(--article-gray-600);
}

.admin-date-cell svg {
  color: var(--article-gray-400);
}

.admin-stat-cell {
  display: flex;
  align-items: center;
  gap: var(--article-space-2);
  font-size: var(--article-font-size-sm);
  color: var(--article-gray-600);
}

.admin-stat-cell svg {
  color: var(--article-gray-400);
}

.admin-status-badge {
  padding: var(--article-space-1) var(--article-space-3);
  border-radius: var(--article-radius);
  font-size: var(--article-font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
}

.admin-status-badge.active {
  background: var(--article-success);
  color: var(--article-white);
}

.admin-action-buttons {
  display: flex;
  gap: var(--article-space-2);
}

/* ===== ACTION BUTTONS ===== */

.admin-btn-view,
.admin-btn-delete {
  padding: var(--article-space-2) var(--article-space-3);
  border: none;
  border-radius: var(--article-radius);
  font-size: var(--article-font-size-sm);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
}

.admin-btn-view {
  background: linear-gradient(135deg, var(--article-secondary), #059669);
  color: var(--article-white);
}

.admin-btn-view:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-1px);
}

.admin-btn-delete {
  background: linear-gradient(135deg, var(--article-danger), #dc2626);
  color: var(--article-white);
}

.admin-btn-delete:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-1px);
}

/* ===== PAGINATION ===== */
.admin-blog-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--article-space-4);
  padding: var(--article-space-6);
  background: var(--article-gray-50);
  border-radius: var(--article-radius-lg);
  margin-top: var(--article-space-6);
}

.admin-pagination-btn {
  padding: var(--article-space-2) var(--article-space-4);
  border: 1px solid var(--article-gray-300);
  background: var(--article-white);
  color: var(--article-gray-700);
  border-radius: var(--article-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.admin-pagination-btn:hover:not(:disabled) {
  background: var(--article-primary);
  color: var(--article-white);
  border-color: var(--article-primary);
}

.admin-pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.admin-pagination-info {
  font-size: var(--article-font-size-sm);
  color: var(--article-gray-600);
  font-weight: 500;
}

/* ===== LOADING STATE ===== */
.admin-blog-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--article-space-8);
  text-align: center;
}

.admin-loading-spinner {
  font-size: 2rem;
  color: var(--article-secondary);
  animation: spin 1s linear infinite;
  margin-bottom: var(--article-space-4);
}

/* ===== ERROR STATE ===== */
.admin-blog-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--article-space-8);
  text-align: center;
}

.admin-btn-retry {
  background: var(--article-secondary);
  color: var(--article-white);
  border: none;
  border-radius: var(--article-radius);
  padding: var(--article-space-3) var(--article-space-4);
  cursor: pointer;
  margin-top: var(--article-space-4);
}

/* ===== EMPTY STATE ===== */
.admin-blog-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--article-space-8);
  text-align: center;
  color: var(--article-gray-500);
}

.admin-empty-icon {
  font-size: 3rem;
  color: var(--article-gray-300);
  margin-bottom: var(--article-space-4);
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
  .admin-blog-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .admin-blog-table-container {
    overflow-x: auto;
  }

  .admin-blog-table {
    min-width: 800px;
  }

  .admin-blog-table th,
  .admin-blog-table td {
    padding: var(--article-space-2);
    font-size: var(--article-font-size-xs);
  }

  .blog-title {
    max-width: 150px;
  }
}

/* ===== DELETE CONFIRMATION MODAL ===== */
.admin-delete-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
  animation: admin-modal-fade-in 0.3s ease-out;
}

.admin-delete-modal-content {
  background: white;
  border-radius: 16px;
  max-width: 480px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid #e5e7eb;
  animation: admin-modal-slide-up 0.3s ease-out;
}

.admin-delete-modal-header {
  padding: 24px 24px 16px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

.admin-delete-modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  text-align: center;
}

.admin-delete-modal-body {
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 16px;
}

.admin-delete-modal-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.admin-delete-modal-text {
  flex: 1;
}

.admin-delete-modal-text p {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #374151;
  line-height: 1.6;
}

.admin-delete-modal-article-info {
  padding: 12px;
  background: #f9fafb;
  border-radius: 8px;
  margin: 12px 0;
  border-left: 4px solid #ef4444;
}

.admin-delete-modal-article-info strong {
  color: #1f2937;
  font-size: 14px;
}

.admin-delete-modal-warning {
  font-size: 14px !important;
  color: #ef4444 !important;
  font-weight: 600 !important;
}

.admin-delete-modal-footer {
  padding: 16px 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: center;
  background: #f9fafb;
}

.admin-delete-modal-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-delete-modal-btn-cancel {
  background: #e5e7eb;
  color: #374151;
  border: 2px solid #d1d5db;
}

.admin-delete-modal-btn-cancel:hover {
  background: #d1d5db;
  border-color: #9ca3af;
  transform: translateY(-1px);
}

.admin-delete-modal-btn-confirm {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: 2px solid #ef4444;
}

.admin-delete-modal-btn-confirm:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  border-color: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* Disabled button styles */
.admin-btn-delete.disabled {
  background: #d1d5db !important;
  color: #9ca3af !important;
  cursor: not-allowed !important;
  opacity: 0.6;
}

.admin-btn-delete.disabled:hover {
  background: #d1d5db !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Modal Animations */
@keyframes admin-modal-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes admin-modal-slide-up {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive */
@media (max-width: 480px) {
  .admin-delete-modal-content {
    width: 95%;
    margin: 16px;
  }

  .admin-delete-modal-header,
  .admin-delete-modal-body,
  .admin-delete-modal-footer {
    padding: 16px;
  }

  .admin-delete-modal-footer {
    flex-direction: column;
  }

  .admin-delete-modal-btn {
    width: 100%;
  }
}
