.admin_ui_sidebar {
  background: #ffffff;
  color: #374151;
  width: 280px;
  min-height: 100vh;
  max-height: 100vh;
  overflow-y: auto;
  box-shadow: 1px 0 3px rgba(0, 0, 0, 0.05);
  border-right: 1px solid #e2e8f0;
  position: fixed;
  top: 0;
  left: 0;
  transition: all 0.3s ease;
  flex-shrink: 0;
  z-index: 900;
  display: flex;
  flex-direction: column;
}

.admin_ui_sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #2563eb, #1d4ed8, #1e40af);
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Header Section */
.admin_ui_sidebar_header {
  padding: 24px 20px;
  border-bottom: 1px solid #e2e8f0;
  /* background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); */
  background: #004E9A;
  color: white;
  border-radius: 0 0 16px 16px;
  margin-bottom: 16px;
}

.admin_ui_logo {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.admin_ui_logo_icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.admin_ui_logo_text h1 {
  font-size: 1.25rem;
  font-weight: 700;
  /* color: #1a202c; */
  color: white;
  margin: 0;
  line-height: 1.2;
}

.admin_ui_logo_text span {
  font-size: 0.75rem;
  /* color: #64748b; */
  color: #d1d5db;
  font-weight: 500;
}



.admin_ui_sidebar_nav {
  padding: 24px 0;
  flex: 1;
  overflow-y: auto;
}

.admin_ui_sidebar_nav ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.admin_ui_nav_item {
  margin: 4px 16px;
  padding: 0;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
}

.admin_ui_nav_item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.05), #004E9A);
  transition: left 0.4s ease;
}

.admin_ui_nav_item:hover::before {
  left: 100%;
}

.admin_ui_nav_item:hover {
  background: #f1f5f9;
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);
}

.admin_ui_nav_item.active {
  background: #004E9A;
  color: white;
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.25);
}

.admin_ui_nav_item i {
  font-size: 1.125rem;
  width: 20px;
  text-align: center;
  flex-shrink: 0;
}

.admin_ui_nav_item span {
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}



/* Sidebar Footer */
.admin_ui_sidebar_footer {
  padding: 16px;
  border-top: 1px solid #e2e8f0;
  margin-top: auto;
}

.admin_ui_logout_btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 12px;
  color: #ef4444;
  font-size: 0.875rem;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.admin_ui_logout_btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(239, 68, 68, 0.05), rgba(239, 68, 68, 0.1));
  transition: left 0.4s ease;
}

.admin_ui_logout_btn:hover::before {
  left: 100%;
}

.admin_ui_logout_btn:hover {
  background: #fef2f2;
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.15);
}

.admin_ui_logout_btn i {
  font-size: 1.125rem;
  width: 20px;
  text-align: center;
  flex-shrink: 0;
}

.admin_ui_logout_btn span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Active indicator */
.admin_ui_nav_item.active::after {
  content: '';
  position: absolute;
  right: -16px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-left: 8px solid #6366f1;
}

/* Custom scrollbar */
.admin_ui_sidebar::-webkit-scrollbar {
  width: 6px;
}

.admin_ui_sidebar::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.admin_ui_sidebar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.admin_ui_sidebar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Responsive design */
@media (max-width: 768px) {
  .admin_ui_sidebar {
    width: 70px;
    min-width: 70px;
  }

  .admin_ui_sidebar_header {
    padding: 16px 8px;
  }

  .admin_ui_logo_text {
    display: none;
  }

  .admin_ui_nav_item {
    margin: 8px 5px;
    justify-content: center;
    padding: 12px 8px;
  }

  .admin_ui_nav_item span {
    display: none;
  }

  .admin_ui_nav_item i {
    margin: 0;
    font-size: 18px;
  }



  .admin_ui_sidebar_footer {
    padding: 8px;
  }

  .admin_ui_logout_btn {
    justify-content: center;
    padding: 12px 8px;
  }

  .admin_ui_logout_btn span {
    display: none;
  }

  .admin_ui_logout_btn i {
    margin: 0;
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .admin_ui_sidebar {
    width: 60px;
    min-width: 60px;
  }

  .admin_ui_sidebar_nav {
    padding: 16px 0;
  }

  .admin_ui_nav_item {
    margin: 6px 3px;
    padding: 10px 6px;
  }

  .admin_ui_nav_item i {
    font-size: 16px;
  }

  .admin_ui_sidebar_footer {
    padding: 6px;
  }

  .admin_ui_logout_btn {
    padding: 10px 6px;
  }

  .admin_ui_logout_btn i {
    font-size: 16px;
  }
}

/* Add hover tooltips for mobile */
@media (max-width: 768px) {
  .admin_ui_nav_item {
    position: relative;
  }

  .admin_ui_nav_item::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 80px;
    top: 50%;
    transform: translateY(-50%);
    background: #1a202c;
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .admin_ui_nav_item:hover::after {
    opacity: 1;
    visibility: visible;
    left: 75px;
  }

  .admin_ui_logout_btn {
    position: relative;
  }

  .admin_ui_logout_btn::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 80px;
    top: 50%;
    transform: translateY(-50%);
    background: #1a202c;
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .admin_ui_logout_btn:hover::after {
    opacity: 1;
    visibility: visible;
    left: 75px;
  }
}

/* Loading animation for menu items */
.admin_ui_nav_item {
  animation: slideInLeft 0.4s ease forwards;
  opacity: 0;
  transform: translateX(-20px);
}

.admin_ui_nav_item:nth-child(1) { animation-delay: 0.1s; }
.admin_ui_nav_item:nth-child(2) { animation-delay: 0.15s; }
.admin_ui_nav_item:nth-child(3) { animation-delay: 0.2s; }
.admin_ui_nav_item:nth-child(4) { animation-delay: 0.25s; }
.admin_ui_nav_item:nth-child(5) { animation-delay: 0.3s; }

@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Active section indicator glow effect */
.admin_ui_nav_item.active {
  position: relative;
}

.admin_ui_nav_item.active::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(45deg, #3b82f6, #2563eb, #3b82f6);
  border-radius: 13px;
  z-index: -1;
  animation: activeGlow 3s ease-in-out infinite alternate;
}

@keyframes activeGlow {
  from {
    box-shadow: 0 0 8px rgba(99, 102, 241, 0.3);
  }
  to {
    box-shadow: 0 0 16px rgba(99, 102, 241, 0.5);
  }
}