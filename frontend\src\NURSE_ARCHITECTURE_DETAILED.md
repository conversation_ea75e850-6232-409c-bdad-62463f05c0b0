# 🏥 Chi tiết kiến trúc Nurse Module

## 📂 **1. context/NurseContext**

### 🎯 **<PERSON><PERSON><PERSON> đích:** 
<PERSON><PERSON><PERSON><PERSON> lý state toàn cục cho tất cả chức năng của Y tá sử dụng React Context API

### 📁 **Cấu trúc files:**

#### **index.js** - Central Export Hub
- Export tất cả contexts và custom hooks
- Tạo custom hook `useHealthCheckup()` với error handling
- <PERSON><PERSON><PERSON><PERSON> truy cập duy nhất cho tất cả Nurse contexts

#### **BlogContext.jsx** - Quản lý Blog & Community
- **State:** blogs, posts, loading, error, categories, pagination
- **Functions:** fetchBlogs, getBlogById, removeBlog, uploadImage, likePost, bookmarkPost
- **Service:** Kết nối với `blogService.js`

#### **HealthCheckupContext.jsx** - <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> sức khỏe
- **State:** medicalCheckups, campaigns, students, loading
- **Functions:** fetchMedicalCheckupById, updateMedicalCheckup, sendParentNotification
- **Service:** Kết nối với `healthCheckupService.js` và `studentRecordsService.js`

#### **InventoryContext.jsx** - Quản lý Kho thuốc
- **State:** items, categories, lowStockItems, selectedItem
- **Functions:** fetchItems, addItem, updateItem, deleteItem, searchItems
- **Service:** Kết nối với `inventoryService.js`

#### **MedicalEventsContext.jsx** - Quản lý Sự kiện Y tế
- **State:** events, selectedEvent, loading, error
- **Functions:** fetchEvents, addEvent, updateEvent, deleteEvent, searchEvents
- **Service:** Kết nối với `medicalEventsService.js`

#### **MedicineApprovalContext.jsx** - Quản lý Duyệt thuốc
- **State:** approvals, medicationHistory, loading
- **Functions:** approveRequest, rejectRequest, fetchHistory
- **Service:** Kết nối với `receiveMedicineService.js`

#### **StudentRecordsContext.jsx** - Quản lý Hồ sơ Học sinh
- **State:** students, classes, grades, bloodTypes
- **Functions:** getAllStudents, searchStudents, updateStudentRecord
- **Service:** Kết nối với `studentRecordsService.js`

#### **VaccinationContext.jsx** - Quản lý Tiêm chủng
- **State:** vaccinationPlans, vaccines, studentStatuses
- **Functions:** fetchVaccinationPlans, createRecord, handleMonitoring
- **Service:** Kết nối với `vaccinationApiService.js`

---

## 📂 **2. Pages/Nurse**

### 🎯 **Mục đích:** 
Chứa tất cả giao diện người dùng cho Y tá

### 📁 **Cấu trúc:**

#### **layout/NurseLayout.jsx** - Layout chính
- **Provider Wrapper:** Bọc tất cả contexts cho toàn bộ Nurse module
- **Navigation:** Tích hợp thanh điều hướng
- **Context Hierarchy:** 
  ```jsx
  InventoryProvider > MedicalEventsProvider > MedicineApprovalProvider > 
  StudentRecordsProvider > HealthCheckupProvider > BlogProvider > VaccinationProvider
  ```

#### **pages/** - Các trang chức năng
- **Blog_co/**: Quản lý blog và community posts
- **HealthCheckups_co/**: Quản lý khám sức khỏe và chiến dịch
- **Inventory_co/**: Quản lý kho thuốc
- **MedicalEvents_co/**: Quản lý sự kiện y tế
- **ReceiveMedicine_co/**: Duyệt đơn thuốc từ phụ huynh
- **StudentRecords_co/**: Quản lý hồ sơ học sinh
- **Vaccination_co/**: Quản lý tiêm chủng

#### **components/** - Components tái sử dụng
- **Dashboard/**: Dashboard components
- **Debug/**: Debug utilities
- **Navigation/**: Navigation components

---

## 📂 **3. services/APINurse**

### 🎯 **Mục đích:** 
Xử lý tất cả API calls cho chức năng Y tá

### 📁 **Files:**

#### **blogService.js** - Blog & Community API
- Blog CRUD operations
- Community post management
- Image upload handling

#### **healthCheckupService.js** - Health Checkup API
- Medical checkup management
- Campaign operations
- Parent notifications

#### **inventoryService.js** - Inventory API
- Medicine inventory CRUD
- Stock management
- Category operations

#### **medicalEventsService.js** - Medical Events API
- Medical incident management
- Event search and filtering
- Medication usage tracking

#### **receiveMedicineService.js** - Medicine Approval API
- Approval/rejection of medicine requests
- Medication administration history
- Parent request management

#### **studentRecordsService.js** - Student Records API
- Student data management
- Class and grade operations
- Search functionality

#### **vaccinationApiService.js** - Vaccination API
- Vaccination plan management
- Vaccine record creation
- Monitoring operations

---

## 🔄 **4. Luồng dữ liệu Context → Service**

### **Tại sao cần Context?**

1. **State Management tập trung:** Tránh prop drilling qua nhiều component levels
2. **Caching:** Lưu trữ dữ liệu đã fetch để tránh gọi API không cần thiết
3. **Loading States:** Quản lý trạng thái loading/error thống nhất
4. **Real-time Updates:** Đồng bộ dữ liệu across components

### **Luồng hoạt động:**

```
Component → useContext Hook → Context State → Service Function → API Call → Update Context State → Re-render Components
```

### **Ví dụ cụ thể:**

```jsx
// 1. Component sử dụng hook
const { items, addItem } = useInventory();

// 2. Context cung cấp state và functions
const addItem = async (newItem) => {
  setLoading(true);
  try {
    const result = await inventoryService.addItem(newItem); // 3. Gọi service
    setItems(prev => [...prev, result]); // 4. Update state
  } catch (error) {
    setError(error.message);
  } finally {
    setLoading(false);
  }
};
```

---

## 🔍 **5. Phân tích Import Patterns trong Pages/Nurse/pages**

### **📋 Danh sách đầy đủ các file import Context:**

#### **A. Import từ index.js (Centralized):**
1. **InventoryMain.jsx:**
   ```jsx
   import { useInventory } from '../../../../context/NurseContext';
   ```

2. **CreateVaccinationRecord.jsx:**
   ```jsx
   import { useVaccination } from '../../../../../context/NurseContext/VaccinationContext';
   ```

3. **Posts.jsx:**
   ```jsx
   import { useBlog } from '../../../../../context/NurseContext/BlogContext';
   ```

#### **B. Import trực tiếp từ file Context:**
1. **MedicalIncidentsList.jsx:**
   ```jsx
   import { useMedicalEvents } from '../../../../../context/NurseContext/MedicalEventsContext';
   ```

2. **ScheduleConsultation.jsx:**
   ```jsx
   import { useHealthCheckup } from '../../../../../context/NurseContext/HealthCheckupContext';
   ```

3. **CampaignDetailPage.jsx:**
   ```jsx
   import { useHealthCheckup } from '../../../../../context/NurseContext/HealthCheckupContext';
   ```

#### **C. Provider Imports (trong Layout/Main components):**
1. **NurseLayout.jsx:**
   ```jsx
   import {
     InventoryProvider,
     MedicalEventsProvider,
     MedicineApprovalProvider
   } from "../../../context/NurseContext";
   ```

2. **HealthCheckupsMain.jsx:**
   ```jsx
   import { HealthCheckupProvider } from '../../../../context/NurseContext/HealthCheckupContext';
   ```

3. **VaccinationMain.jsx:**
   ```jsx
   import { VaccinationProvider } from '../../../../context/NurseContext/VaccinationContext';
   ```

### **🔄 Luồng Context → Service trong thực tế:**

#### **Ví dụ 1: Inventory Management**
```jsx
// InventoryMain.jsx
const { items, addItem, loading, error } = useInventory();

// InventoryContext.jsx
const addItem = async (newItem) => {
  setLoading(true);
  try {
    const result = await inventoryService.addItem(newItem); // → API call
    setItems(prev => [...prev, result]); // → Update state
    return result;
  } catch (error) {
    setError(error.message);
    throw error;
  } finally {
    setLoading(false);
  }
};
```

#### **Ví dụ 2: Medical Events**
```jsx
// MedicalIncidentsList.jsx
const { events, fetchEvents, deleteEvent } = useMedicalEvents();

// MedicalEventsContext.jsx
const deleteEvent = async (id) => {
  try {
    await medicalEventsService.deleteEvent(id); // → API call
    setEvents(prev => prev.filter(event => event.id !== id)); // → Update state
  } catch (error) {
    setError(error.message);
  }
};
```

### **🎯 Lý do import Context thay vì gọi Service trực tiếp:**

1. **🔄 State Synchronization:**
   - Context đảm bảo tất cả components cùng nhìn thấy data mới nhất
   - Khi một component thêm/sửa/xóa, các component khác tự động update

2. **⚡ Performance Optimization:**
   - Caching: Tránh gọi API nhiều lần cho cùng dữ liệu
   - Lazy loading: Chỉ fetch data khi cần thiết

3. **🛡️ Error Handling thống nhất:**
   - Tất cả API errors được handle ở một nơi
   - Loading states được quản lý centralized

4. **🔧 Easier Testing:**
   - Mock context dễ hơn mock từng service call
   - Unit test components độc lập với API

### **📊 So sánh 2 approaches:**

| **Aspect** | **Direct Service Call** | **Through Context** |
|------------|------------------------|-------------------|
| **State Sync** | ❌ Manual sync needed | ✅ Automatic sync |
| **Caching** | ❌ No caching | ✅ Built-in caching |
| **Loading States** | ❌ Duplicate code | ✅ Centralized |
| **Error Handling** | ❌ Repetitive | ✅ Consistent |
| **Testing** | ❌ Complex mocking | ✅ Easy mocking |
| **Performance** | ❌ Multiple API calls | ✅ Optimized calls |

### **🏗️ Lợi ích của kiến trúc này:**

- ✅ **Separation of Concerns:** UI logic tách biệt với business logic
- ✅ **Reusability:** Context có thể được sử dụng ở nhiều components
- ✅ **Maintainability:** Dễ maintain và debug
- ✅ **Performance:** Caching giảm số lần gọi API
- ✅ **Consistency:** Error handling và loading states thống nhất
- ✅ **Scalability:** Dễ mở rộng thêm chức năng mới
