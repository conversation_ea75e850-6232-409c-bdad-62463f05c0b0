# 💉 Logic Hiển thị Trạng thái "Đã hoàn thành" - Vaccination Module

## 📍 **Vị trí Logic trong VaccinationPlanDetailPage.jsx**

### 🎯 **Tổng quan:**
Logic hiển thị trạng thái "đã hoàn thành" được phân tán trong nhiều phần của file, bao gồm:
1. **Tính toán trạng thái** (qua `monitoringStatusUtils.js`)
2. **Hiển thị badge trạng thái** (trong component)
3. **Filter theo trạng thái** (dropdown filter)
4. **Điều kiện hiển thị nút tạo hồ sơ**

---

## 🔄 **1. Logic Tính Toán Trạng thái (monitoringStatusUtils.js)**

### **📍 Vị trí:** Lines 26-32 trong `calculateStudentMonitoringStatus()`

<augment_code_snippet path="frontend/src/Pages/Nurse/pages/Vaccination_co/CreateRecord/monitoringStatusUtils.js" mode="EXCERPT">
```javascript
const allCompleted = filteredHistory.every(record => {
  const notes = record.notes;
  return notes && notes.toLowerCase().trim().includes('không có phản ứng phụ');
});

if (allCompleted) {
  return 'Hoàn thành';
} else {
  return 'Cần theo dõi';
}
```
</augment_code_snippet>

### **🎯 Logic:**
- **Điều kiện "Hoàn thành":** TẤT CẢ vaccination records phải có `notes` chứa text "không có phản ứng phụ"
- **Nếu không:** Trạng thái là "Cần theo dõi"
- **Nếu chưa có record:** Trạng thái là "Chưa hoàn thành"

---

## 🔄 **2. Load và Update Trạng thái (VaccinationPlanDetailPage.jsx)**

### **📍 Vị trí:** Lines 84-113 - `useEffect` load monitoring statuses

<augment_code_snippet path="frontend/src/Pages/Nurse/pages/Vaccination_co/CreateRecord/VaccinationPlanDetailPage.jsx" mode="EXCERPT">
```javascript
// Load monitoring statuses when plan details change
useEffect(() => {
  const loadMonitoringStatuses = async () => {
    if (!planDetails?.students || !planDetails?.vaccinationDate || !planDetails?.vaccines) return;

    setMonitoringStatusLoading(true);
    try {
      // Load general monitoring statuses (for overall status display)
      const statuses = await calculateStudentsMonitoringStatus(
        planDetails.students,
        planDetails.vaccinationDate
      );
      setMonitoringStatuses(statuses);
    } catch (error) {
      console.error('Error loading monitoring statuses:', error);
    } finally {
      setMonitoringStatusLoading(false);
    }
  };

  loadMonitoringStatuses();
}, [planDetails?.students, planDetails?.vaccinationDate, planDetails?.vaccines]);
```
</augment_code_snippet>

### **🎯 Logic:**
- **Trigger:** Khi `planDetails` thay đổi
- **Process:** Gọi API để tính toán trạng thái cho tất cả học sinh
- **Result:** Update `monitoringStatuses` state

---

## 🔄 **3. Hiển thị Badge Trạng thái**

### **📍 Vị trí:** Lines 173-190 - `getStatusBadge()` function

<augment_code_snippet path="frontend/src/Pages/Nurse/pages/Vaccination_co/CreateRecord/VaccinationPlanDetailPage.jsx" mode="EXCERPT">
```javascript
// Get status badge
const getStatusBadge = (status) => {
  const color = getVaccinationRecordStatusColor(status);
  const text = getVaccinationRecordStatusText(status);
  
  return (
    <Badge 
      style={{ 
        backgroundColor: color, 
        color: 'white',
        fontSize: '0.75rem',
        padding: '0.25rem 0.5rem'
      }}
    >
      {text}
    </Badge>
  );
};
```
</augment_code_snippet>

### **📍 Vị trí hiển thị:** Lines 503-508 - Trong table row

<augment_code_snippet path="frontend/src/Pages/Nurse/pages/Vaccination_co/CreateRecord/VaccinationPlanDetailPage.jsx" mode="EXCERPT">
```javascript
<td>
  {monitoringStatusLoading ? (
    <Spinner animation="border" size="sm" />
  ) : (
    getStatusBadge(status)
  )}
</td>
```
</augment_code_snippet>

### **🎯 Logic:**
- **Input:** Trạng thái từ `monitoringStatuses[student.healthProfileId]`
- **Output:** Badge với màu sắc và text tương ứng
- **Loading:** Hiển thị spinner khi đang load

---

## 🔄 **4. Mapping Text và Color**

### **📍 Vị trí:** `monitoringStatusUtils.js` - Lines 202-230

<augment_code_snippet path="frontend/src/Pages/Nurse/pages/Vaccination_co/CreateRecord/monitoringStatusUtils.js" mode="EXCERPT">
```javascript
export const getVaccinationRecordStatusText = (monitoringStatus) => {
  switch (monitoringStatus) {
    case 'Hoàn thành':
      return 'Đã tạo HS - Hoàn thành';
    case 'Cần theo dõi':
      return 'Đã tạo HS - Cần theo dõi';
    case 'Chưa hoàn thành':
      return 'Chưa tạo HS';
    default:
      return 'Đang kiểm tra...';
  }
};

export const getVaccinationRecordStatusColor = (monitoringStatus) => {
  switch (monitoringStatus) {
    case 'Hoàn thành':
    case 'Cần theo dõi':
      return '#10b981'; // green - đã tạo HS
    case 'Chưa hoàn thành':
      return '#ef4444'; // red - chưa tạo HS
    default:
      return '#6b7280'; // gray - đang kiểm tra
  }
};
```
</augment_code_snippet>

---

## 🔄 **5. Filter Dropdown**

### **📍 Vị trí:** Lines 445-453 - Status filter dropdown

<augment_code_snippet path="frontend/src/Pages/Nurse/pages/Vaccination_co/CreateRecord/VaccinationPlanDetailPage.jsx" mode="EXCERPT">
```javascript
<Form.Select
  value={statusFilter}
  onChange={(e) => setStatusFilter(e.target.value)}
>
  <option value="">Tất cả trạng thái</option>
  <option value="Chưa hoàn thành">Chưa tạo HS</option>
  <option value="Cần theo dõi">Đã tạo HS - Cần theo dõi</option>
  <option value="Hoàn thành">Đã tạo HS - Hoàn thành</option>
</Form.Select>
```
</augment_code_snippet>

### **📍 Vị trí filter logic:** Lines 267-283 - `filteredStudents` useMemo

<augment_code_snippet path="frontend/src/Pages/Nurse/pages/Vaccination_co/CreateRecord/VaccinationPlanDetailPage.jsx" mode="EXCERPT">
```javascript
const filteredStudents = useMemo(() => {
  if (!planDetails?.students) return [];

  return planDetails.students.filter(student => {
    const matchesSearch = student.fullName.toLowerCase().includes(searchTerm.toLowerCase());

    const studentStatus = monitoringStatuses[student.healthProfileId] || 'Đang kiểm tra...';
    const matchesStatus = !statusFilter || studentStatus === statusFilter;

    return matchesSearch && matchesStatus && matchesResponse;
  });
}, [planDetails?.students, searchTerm, statusFilter, responseFilter, monitoringStatuses]);
```
</augment_code_snippet>

---

## 🔄 **6. Điều kiện Hiển thị Nút "Tạo Hồ sơ"**

### **📍 Vị trí:** Lines 193-194 - `canCreateVaccinationRecord()` function

<augment_code_snippet path="frontend/src/Pages/Nurse/pages/Vaccination_co/CreateRecord/monitoringStatusUtils.js" mode="EXCERPT">
```javascript
export const canCreateVaccinationRecord = (monitoringStatus) => {
  return monitoringStatus === 'Chưa hoàn thành';
};
```
</augment_code_snippet>

### **📍 Vị trí sử dụng:** Lines 228-234 - Trong `renderActionButtons()`

<augment_code_snippet path="frontend/src/Pages/Nurse/pages/Vaccination_co/CreateRecord/VaccinationPlanDetailPage.jsx" mode="EXCERPT">
```javascript
// Kiểm tra status riêng cho từng vaccine
const vaccineStatusKey = `${student.healthProfileId}_${vaccineResponse.vaccineId}`;
const vaccineStatus = vaccineMonitoringStatuses[vaccineStatusKey] || 'Đang kiểm tra...';
const canCreateThisVaccine = canCreateVaccinationRecord(vaccineStatus);

if (canCreateThisVaccine) {
  return (
    <Button
      key={vaccineResponse.vaccineId}
      // ... button props
    >
      Tạo HS {vaccineName}
    </Button>
  );
}
```
</augment_code_snippet>

---

## 📊 **Luồng hoạt động hoàn chỉnh:**

```
1. Component mount → Load plan details
2. Plan details loaded → Trigger useEffect
3. useEffect → Call calculateStudentsMonitoringStatus()
4. For each student → Call calculateStudentMonitoringStatus()
5. Check vaccination history → Filter by date
6. Check notes → Look for "không có phản ứng phụ"
7. All records have this note → Status = "Hoàn thành"
8. Update monitoringStatuses state
9. Re-render table → Show badge with green color
10. Filter dropdown → Allow filtering by "Hoàn thành"
11. Action buttons → Hide "Tạo HS" button for completed students
```

---

## 🎯 **Key Points:**

1. **Trạng thái "Hoàn thành"** = Tất cả vaccination records có notes chứa "không có phản ứng phụ"
2. **Hiển thị:** Badge màu xanh với text "Đã tạo HS - Hoàn thành"
3. **Nút tạo hồ sơ:** Bị ẩn khi trạng thái là "Hoàn thành"
4. **Filter:** Có thể lọc chỉ hiển thị học sinh đã hoàn thành
5. **Real-time:** Trạng thái được reload sau khi tạo hồ sơ mới
