/* Modern Features Section - Professional Blue Theme */

/* Professional Blue Color Palette */
:root {
  --primary-blue: #015C92;
  --secondary-blue: #2D82B5;
  --accent-blue: #428CD4;
  --light-blue: #88CDF6;
  --lightest-blue: #BCE6FF;
  --blue-gradient: linear-gradient(135deg, #015C92 0%, #2D82B5 25%, #428CD4 50%, #88CDF6 75%, #BCE6FF 100%);
  --blue-gradient-secondary: linear-gradient(135deg, #428CD4 0%, #88CDF6 50%, #BCE6FF 100%);
  --neutral-50: #f8fafc;
  --neutral-100: #f1f5f9;
  --neutral-200: #e2e8f0;
  --neutral-300: #cbd5e1;
  --neutral-400: #94a3b8;
  --neutral-500: #64748b;
  --neutral-600: #475569;
  --neutral-700: #334155;
  --neutral-800: #1e293b;
  --neutral-900: #0f172a;
}

.features-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  position: relative;
  overflow: hidden;
}

/* Container để tránh xung đột với global.css container */
.features-container {
  max-width: 1360px;
  width: 100%;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

/* Background elements */
.features-bg-element {
  position: absolute;
  z-index: 1;
  pointer-events: none;
  opacity: 0.3;
}

.features-bg-circle {
  width: 400px;
  height: 400px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(1, 92, 146, 0.06) 0%, rgba(1, 92, 146, 0) 70%);
  top: -150px;
  left: -100px;
  animation: pulse 15s infinite alternate;
}

.features-bg-square {
  top: 20%;
  right: 5%;
  width: 200px;
  height: 200px;
  background-image: linear-gradient(45deg, rgba(1, 92, 146, 0.03) 25%, transparent 25%),
                    linear-gradient(-45deg, rgba(1, 92, 146, 0.03) 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, rgba(1, 92, 146, 0.03) 75%),
                    linear-gradient(-45deg, transparent 75%, rgba(1, 92, 146, 0.03) 75%);
  background-size: 20px 20px;
  border-radius: 30px;
  transform: rotate(15deg);
  animation: float 20s infinite ease-in-out;
}

.features-bg-dots {
  bottom: 10%;
  left: 5%;
  width: 180px;
  height: 180px;
  background-image: radial-gradient(circle, rgba(45, 130, 181, 0.15) 2px, transparent 2px);
  background-size: 18px 18px;
  border-radius: 50%;
  animation: float 15s infinite ease-in-out alternate-reverse;
}

/* Header styling */
.features-header {
  text-align: center;
  margin-bottom: 70px;
  position: relative;
}

.features-pre-title {
  display: inline-block;
  padding: 6px 16px;
  background-color: rgba(1, 92, 146, 0.1);
  color: var(--primary-blue);
  border-radius: 30px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 20px;
  letter-spacing: 1px;
  transform: translateY(20px);
  opacity: 0;
  animation: slideUp 0.8s forwards;
}

.features-title {
  font-size: 2.5rem;
  margin-bottom: 20px;
  background: var(--blue-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transform: translateY(20px);
  opacity: 0;
  animation: slideUp 0.8s 0.2s forwards;
}

.features-title-underline {
  height: 4px;
  width: 80px;
  background: var(--blue-gradient-secondary);
  margin: 0 auto 30px;
  border-radius: 2px;
  transform: scaleX(0);
  animation: expandWidth 1s 0.5s forwards;
}

.features-subtitle {
  max-width: 700px;
  margin: 0 auto;
  color: var(--neutral-600);
  font-size: 1.1rem;
  line-height: 1.7;
  transform: translateY(20px);
  opacity: 0;
  animation: slideUp 0.8s 0.4s forwards;
}

/* Grid layout */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
  gap: 30px;
}

/* Feature card styling */
.feature-card {
  position: relative;
  background: #ffffff;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(1, 92, 146, 0.06);
  transition: all 0.4s ease;
  opacity: 0;
  transform: translateY(40px);
}

.feature-card-inner {
  padding: 40px 30px;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(1, 92, 146, 0.12);
}

.feature-card.feature-visible {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

/* Professional Blue theme for all feature icons */
.feature-card:nth-child(1) .feature-icon-wrapper {
  background: var(--primary-blue);
}

.feature-card:nth-child(2) .feature-icon-wrapper {
  background: var(--secondary-blue);
}

.feature-card:nth-child(3) .feature-icon-wrapper {
  background: var(--accent-blue);
}

.feature-card:nth-child(4) .feature-icon-wrapper {
  background: var(--light-blue);
  color: var(--primary-blue) !important;
}

.feature-card:nth-child(5) .feature-icon-wrapper {
  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
}

.feature-card:nth-child(6) .feature-icon-wrapper {
  background: linear-gradient(135deg, var(--accent-blue), var(--light-blue));
}

/* Icon styling */
.feature-icon-wrapper {
  width: 70px;
  height: 70px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 25px;
  position: relative;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(1, 92, 146, 0.2);
}

.feature-card:hover .feature-icon-wrapper {
  transform: scale(1.1) rotate(5deg);
}

.feature-icon-wrapper i {
  font-size: 1.8rem;
  color: #fff;
  position: relative;
  z-index: 2;
}

.feature-card:nth-child(4) .feature-icon-wrapper i {
  color: var(--primary-blue) !important;
}

.feature-icon-glow {
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.4), transparent 70%);
  opacity: 0.7;
  top: 0;
  left: 0;
  transition: all 0.4s ease;
}

.feature-card:hover .feature-icon-glow {
  transform: scale(1.5) rotate(25deg);
  opacity: 0.3;
}

/* Content styling */
.feature-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.feature-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--neutral-800);
  margin-bottom: 15px;
  position: relative;
  padding-bottom: 15px;
  transition: color 0.3s ease;
  font-family: var(--font-heading);
}

/* Professional Blue underlines for titles */
.feature-card:nth-child(1) .feature-title::after { background: var(--primary-blue); }
.feature-card:nth-child(2) .feature-title::after { background: var(--secondary-blue); }
.feature-card:nth-child(3) .feature-title::after { background: var(--accent-blue); }
.feature-card:nth-child(4) .feature-title::after { background: var(--light-blue); }
.feature-card:nth-child(5) .feature-title::after { background: var(--primary-blue); }
.feature-card:nth-child(6) .feature-title::after { background: var(--accent-blue); }

.feature-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  transition: width 0.4s ease;
  border-radius: 3px;
}

.feature-card:hover .feature-title::after {
  width: 60px;
}

.feature-description {
  color: var(--neutral-500);
  line-height: 1.7;
  margin-bottom: 25px;
  flex: 1;
  font-size: 1rem;
}

/* Professional Blue links */
.feature-card:nth-child(1) .feature-learn-more { color: var(--primary-blue); }
.feature-card:nth-child(1) .feature-learn-more::after { background: var(--primary-blue); }

.feature-card:nth-child(2) .feature-learn-more { color: var(--secondary-blue); }
.feature-card:nth-child(2) .feature-learn-more::after { background: var(--secondary-blue); }

.feature-card:nth-child(3) .feature-learn-more { color: var(--accent-blue); }
.feature-card:nth-child(3) .feature-learn-more::after { background: var(--accent-blue); }

.feature-card:nth-child(4) .feature-learn-more { color: var(--light-blue); }
.feature-card:nth-child(4) .feature-learn-more::after { background: var(--light-blue); }

.feature-card:nth-child(5) .feature-learn-more { color: var(--primary-blue); }
.feature-card:nth-child(5) .feature-learn-more::after { background: var(--primary-blue); }

.feature-card:nth-child(6) .feature-learn-more { color: var(--accent-blue); }
.feature-card:nth-child(6) .feature-learn-more::after { background: var(--accent-blue); }

.feature-learn-more {
  display: inline-flex;
  align-items: center;
  font-weight: 600;
  text-decoration: none;
  padding: 8px 0;
  transition: all 0.3s ease;
  position: relative;
  margin-top: auto;
  width: fit-content;
}

.feature-learn-more::after {
  content: '';
  position: absolute;
  bottom: 5px;
  left: 0;
  width: 0;
  height: 2px;
  transition: width 0.3s ease;
  border-radius: 1px;
}

.feature-learn-more i {
  margin-left: 8px;
  font-size: 0.8rem;
  transition: transform 0.3s ease;
}

.feature-learn-more:hover::after {
  width: calc(100% - 25px);
}

.feature-learn-more:hover i {
  transform: translateX(5px);
}

/* Card decorations with Professional Blue */
.feature-card-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  z-index: 1;
  opacity: 0.2;
}

.feature-card:nth-child(1) .feature-decoration-circle,
.feature-card:nth-child(1) .feature-decoration-square {
  background: rgba(1, 92, 146, 0.1);
}

.feature-card:nth-child(2) .feature-decoration-circle,
.feature-card:nth-child(2) .feature-decoration-square {
  background: rgba(45, 130, 181, 0.1);
}

.feature-card:nth-child(3) .feature-decoration-circle,
.feature-card:nth-child(3) .feature-decoration-square {
  background: rgba(66, 140, 212, 0.1);
}

.feature-card:nth-child(4) .feature-decoration-circle,
.feature-card:nth-child(4) .feature-decoration-square {
  background: rgba(136, 205, 246, 0.1);
}

.feature-card:nth-child(5) .feature-decoration-circle,
.feature-card:nth-child(5) .feature-decoration-square {
  background: rgba(1, 92, 146, 0.1);
}

.feature-card:nth-child(6) .feature-decoration-circle,
.feature-card:nth-child(6) .feature-decoration-square {
  background: rgba(66, 140, 212, 0.1);
}

.feature-decoration-circle,
.feature-decoration-square {
  position: absolute;
  opacity: 0.2;
  animation: floatDecoration 10s infinite ease-in-out alternate;
  animation-delay: var(--delay, 0s);
}

.feature-decoration-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  top: -60px;
  right: -60px;
}

.feature-decoration-square {
  width: 80px;
  height: 80px;
  border-radius: 12px;
  bottom: -40px;
  right: 40px;
  transform: rotate(30deg);
}

/* Animations */
@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes expandWidth {
  to {
    transform: scaleX(1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes float {
  0%, 100% {
    transform: translate(0, 0) rotate(15deg);
  }
  50% {
    transform: translate(10px, -15px) rotate(25deg);
  }
}

@keyframes floatDecoration {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
  }
  50% {
    transform: translate(5px, -5px) rotate(5deg);
  }
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .features-grid {
    gap: 25px;
  }
  
  .feature-card-inner {
    padding: 35px 25px;
  }
  
  .features-title {
    font-size: 2.2rem;
  }
}

@media (max-width: 992px) {
  .features-section {
    padding: 80px 0;
  }
  
  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
  }
  
  .features-title {
    font-size: 2rem;
  }
  
  .features-subtitle {
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .features-section {
    padding: 60px 0;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .features-header {
    margin-bottom: 50px;
  }
  
  .features-title {
    font-size: 1.8rem;
  }
  
  .feature-icon-wrapper {
    width: 60px;
    height: 60px;
  }
  
  .feature-icon-wrapper i {
    font-size: 1.5rem;
  }
  
  .feature-card-inner {
    padding: 30px 25px;
  }
}

@media (max-width: 480px) {
  .features-section {
    padding: 50px 0;
  }
  
  .features-pre-title {
    padding: 4px 12px;
    font-size: 0.8rem;
  }
  
  .features-title {
    font-size: 1.5rem;
  }
  
  .features-subtitle {
    font-size: 0.95rem;
  }
  
  .feature-card-inner {
    padding: 25px 20px;
  }
  
  .feature-title {
    font-size: 1.1rem;
  }
}

@media (prefers-color-scheme: dark) {
  .features-section {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  }
  
  .feature-card {
    background: #1e293b;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  }
  
  .feature-title {
    color: #f1f5f9;
  }
  
  .feature-description {
    color: #94a3b8;
  }
  
  .features-subtitle {
    color: #cbd5e1;
  }
  
  .features-pre-title {
    background-color: rgba(66, 140, 212, 0.2);
    color: var(--accent-blue);
  }
}
