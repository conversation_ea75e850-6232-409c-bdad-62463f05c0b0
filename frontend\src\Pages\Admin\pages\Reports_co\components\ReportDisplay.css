/* Report Display - Minimal Modern Design */
.reports-generated-report {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-top: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #f1f5f9;
}

/* Report Header */
.reports-report-header {
  margin-bottom: 24px;
  text-align: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #f1f5f9;
}

.reports-report-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  font-family: var(--font-heading);
  color: #1e293b;
  margin: 0 0 8px 0;
  letter-spacing: -0.025em;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.reports-report-header i {
  color: #3b82f6;
}

.reports-report-meta {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Report Summary */
.reports-report-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8px;
}

.reports-summary-item {
  text-align: center;
}

.reports-summary-item .reports-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1;
  margin-bottom: 4px;
}

.reports-summary-item .reports-label {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

/* Data Table */
.reports-data-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 16px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #f1f5f9;
}

.reports-data-table thead {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.reports-data-table th {
  padding: 16px 12px;
  text-align: left;
  font-weight: 600;
  color: white;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  border-bottom: 2px solid #1d4ed8;
}

.reports-data-table td {
  padding: 12px;
  border-bottom: 1px solid #f1f5f9;
  color: #334155;
  font-size: 0.875rem;
  line-height: 1.5;
}

.reports-data-table tbody tr {
  transition: background-color 0.15s ease;
}

.reports-data-table tbody tr:hover {
  background-color: #f8fafc;
}

.reports-data-table tbody tr:last-child td {
  border-bottom: none;
}

/* Status indicators */
.reports-status {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.reports-status.active {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.reports-status.inactive {
  background: #fef2f2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

.reports-status.pending {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fed7aa;
}

.reports-status.completed {
  background: #dbeafe;
  color: #1d4ed8;
  border: 1px solid #bfdbfe;
}

/* Action buttons in table */
.reports-table-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.reports-action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: 1px solid transparent;
  background: transparent;
  cursor: pointer;
  transition: all 0.15s ease;
  font-size: 0.875rem;
  color: #64748b;
}

.reports-action-btn:hover {
  background: #f1f5f9;
  color: #334155;
  border-color: #e2e8f0;
}

.reports-action-btn.view {
  color: #3b82f6;
}

.reports-action-btn.view:hover {
  background: #dbeafe;
  border-color: #bfdbfe;
}

.reports-action-btn.edit {
  color: #f59e0b;
}

.reports-action-btn.edit:hover {
  background: #fef3c7;
  border-color: #fed7aa;
}

.reports-action-btn.delete {
  color: #ef4444;
}

.reports-action-btn.delete:hover {
  background: #fef2f2;
  border-color: #fecaca;
}

/* Empty state */
.reports-empty-state {
  text-align: center;
  padding: 48px 24px;
  color: #64748b;
}

.reports-empty-state i {
  font-size: 3rem;
  color: #cbd5e1;
  margin-bottom: 16px;
  display: block;
}

.reports-empty-state h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #475569;
  margin: 0 0 8px 0;
}

.reports-empty-state p {
  margin: 0;
  font-size: 0.875rem;
}

/* Loading state */
.reports-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 48px;
  color: #64748b;
  gap: 12px;
}

.reports-loading i {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Export actions */
.reports-export-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f1f5f9;
  justify-content: flex-end;
}

.reports-export-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 1px solid #e2e8f0;
  background: white;
  border-radius: 6px;
  color: #475569;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  text-decoration: none;
}

.reports-export-btn:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  color: #334155;
}

.reports-export-btn.primary {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.reports-export-btn.primary:hover {
  background: #2563eb;
  border-color: #2563eb;
}

/* Print styles */
@media print {
  .reports-generated-report {
    box-shadow: none;
    border: none;
    margin: 0;
    padding: 20px;
  }
  
  .reports-export-actions {
    display: none;
  }
  
  .reports-action-btn {
    display: none;
  }
  
  .reports-data-table {
    font-size: 0.75rem;
  }
  
  .reports-data-table th,
  .reports-data-table td {
    padding: 8px 6px;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .reports-generated-report {
    padding: 16px;
    margin-top: 16px;
  }
  
  .reports-report-summary {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 16px;
  }
  
  .reports-data-table {
    font-size: 0.75rem;
  }
  
  .reports-data-table th,
  .reports-data-table td {
    padding: 8px 6px;
  }
  
  .reports-export-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .reports-export-btn {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .reports-report-summary {
    grid-template-columns: 1fr;
  }
  
  .reports-summary-item .reports-number {
    font-size: 1.5rem;
  }
  
  .reports-data-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }
  
  .reports-table-actions {
    flex-direction: column;
    gap: 4px;
  }
  
  .reports-action-btn {
    width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }
}
