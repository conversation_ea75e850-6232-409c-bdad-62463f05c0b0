/* Date Range Selector Component */
.reports-date-range-selector {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  margin-bottom: 24px;
}

.reports-date-range-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.reports-date-range-label i {
  color: #3b82f6;
  font-size: 1rem;
}

.reports-date-inputs {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.reports-date-input-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 160px;
}

.reports-date-input-group label {
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.reports-date-input {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  color: #374151;
  background: white;
  transition: all 0.2s ease;
  min-width: 160px;
}

.reports-date-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: #fefefe;
}

.reports-date-input:hover {
  border-color: #9ca3af;
}

/* Separator */
.reports-date-separator {
  color: #9ca3af;
  font-weight: 500;
  margin: 0 8px;
  align-self: end;
  margin-bottom: 6px;
}

/* Quick date buttons */
.reports-quick-dates {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e5e7eb;
}

.reports-quick-date-btn {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.15s ease;
}

.reports-quick-date-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  color: #374151;
}

.reports-quick-date-btn.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

/* Apply button */
.reports-apply-dates {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.reports-apply-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.reports-apply-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.reports-apply-btn:active {
  transform: translateY(0);
}

.reports-apply-btn:disabled {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Error state */
.reports-date-error {
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.reports-date-error i {
  font-size: 0.875rem;
}

.reports-date-input.error {
  border-color: #ef4444;
  background: #fef2f2;
}

.reports-date-input.error:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Responsive */
@media (max-width: 768px) {
  .reports-date-range-selector {
    padding: 16px;
  }

  .reports-date-inputs {
    flex-direction: column;
    align-items: stretch;
  }

  .reports-date-input-group {
    min-width: auto;
  }

  .reports-date-input {
    min-width: auto;
  }

  .reports-date-separator {
    align-self: center;
    margin: 0;
    transform: rotate(90deg);
  }

  .reports-quick-dates {
    justify-content: center;
  }

  .reports-apply-dates {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .reports-quick-dates {
    flex-direction: column;
  }

  .reports-quick-date-btn {
    text-align: center;
  }
}
