// Import safe date utilities
import { formatDate as safeDateFormat, safeParseDate } from '../../../utils/dateUtils';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

// Helper function to get auth token using sessionService
const getAuthToken = async () => {
  try {
    const { default: sessionService } = await import('../../../../../services/sessionService');
    const token = sessionService.getToken();

    if (token) {
      // Extend session on API activity
      sessionService.extendSession();
    }

    return token;
  } catch (error) {
    console.warn('SessionService not available, using localStorage fallback:', error);
    return localStorage.getItem('authToken'); // Fallback for compatibility
  }
};

// Dashboard service để lấy dữ liệu cho các biểu đồ
export const dashboardService = {
  // 1. L<PERSON>y thống kê người dùng theo vai trò
  getUserStatistics: async () => {
    try {
      // Import sessionService dynamically to avoid circular dependencies
      const { default: sessionService } = await import('../../../../../services/sessionService');

      const token = sessionService.getToken();

      if (!token) {
        console.warn('No authentication token found');
        throw new Error('Authentication token not found');
      }

      console.log('Fetching user statistics with authToken:', token ? 'Token exists' : 'No token');

      // Extend session on API activity
      sessionService.extendSession();

      // Use the same headers and credentials as User Management
      const response = await fetch(`${API_BASE_URL}/account-members/getAll`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        credentials: 'include'
      });
      
      console.log('Response status:', response.status);
      console.log('Response ok:', response.ok);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Server response:', errorText);
        
        if (response.status === 401) {
          throw new Error('Unauthorized: Bạn cần đăng nhập để truy cập tính năng này');
        } else if (response.status === 403) {
          throw new Error('Forbidden: Bạn không có quyền truy cập tính năng này');
        } else if (response.status === 500) {
          throw new Error('Server error - please try again later');
        } else {
          throw new Error(`Failed to fetch users: ${response.status} ${response.statusText}`);
        }
      }
      
      const users = await response.json();
      console.log('Successfully fetched users:', users.length);
      
      // Use fallback data if no users found
      if (users.length === 0) {
        console.log('No users found in database, using fallback data');
        return {
          admin: 5,
          nurse: 12,
          parent: 450,
          total: 467,
          active: 445,
          inactive: 22,
          usingFallback: true
        };
      }
      
      // Đếm số lượng user theo role
      const roleCount = users.reduce((acc, user) => {
        acc[user.role] = (acc[user.role] || 0) + 1;
        return acc;
      }, {});
      
      return {
        admin: roleCount.ADMIN || 0,
        nurse: roleCount.NURSE || 0,
        parent: roleCount.PARENT || 0,
        total: users.length,
        active: users.filter(u => u.isActive).length,
        inactive: users.filter(u => !u.isActive).length
      };
    } catch (error) {
      console.error('Error fetching user statistics:', error);
      
      // Check if it's an authentication error
      if (error.message.includes('Unauthorized') || error.message.includes('Forbidden')) {
        // You might want to redirect to login page here
        console.warn('Authentication issue detected - user may need to re-login');
      }
      
      // Fallback data
      return {
        admin: 5,
        nurse: 12,
        parent: 450,
        total: 467,
        active: 445,
        inactive: 22
      };
    }
  },

  // 2. Lấy báo cáo khám sức khỏe
  getHealthCheckupReport: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/notifications/nurse/getNotificationsByType/HEALTH_CHECKUP`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch health checkup data');
      }
      
      const data = await response.json();
      
      let totalRecipients = 0;
      let acceptedCount = 0;
      let pendingCount = 0;
      let rejectedCount = 0;
      
      data.forEach(notification => {
        totalRecipients += notification.recipients.length;
        notification.recipients.forEach(recipient => {
          switch(recipient.response) {
            case 'ACCEPTED':
              acceptedCount++;
              break;
            case 'PENDING':
              pendingCount++;
              break;
            case 'REJECTED':
              rejectedCount++;
              break;
          }
        });
      });
      
      return {
        total: totalRecipients,
        accepted: acceptedCount,
        pending: pendingCount,
        rejected: rejectedCount,
        participationRate: totalRecipients > 0 ? ((acceptedCount / totalRecipients) * 100).toFixed(1) : 0
      };
    } catch (error) {
      console.error('Error fetching health checkup report:', error);
      // Fallback data
      return {
        total: 1200,
        accepted: 980,
        pending: 150,
        rejected: 70,
        participationRate: 81.7
      };
    }
  },

  // 3. Lấy báo cáo tiêm chủng
  getVaccinationReport: async () => {
    try {
      const token = await getAuthToken();

      if (!token) {
        console.warn('No authentication token found for vaccination plans');
        throw new Error('Authentication token not found');
      }
      
      console.log('Fetching vaccination plans from API');
      
      const response = await fetch(`${API_BASE_URL}/vaccination-plans/getAllVaccinationPlans`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        credentials: 'include'
      });
      
      console.log('Vaccination plans response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Vaccination plans API error:', errorText);
        
        if (response.status === 401) {
          throw new Error('Unauthorized: Bạn cần đăng nhập để truy cập dữ liệu tiêm chủng');
        } else if (response.status === 403) {
          throw new Error('Forbidden: Bạn không có quyền truy cập dữ liệu tiêm chủng');
        } else {
          throw new Error(`Failed to fetch vaccination plans: ${response.status} ${response.statusText}`);
        }
      }
      
      const vaccinationPlans = await response.json();
      console.log('Successfully fetched vaccination plans:', vaccinationPlans.length);
      
      // Count by status (WAITING_PARENT, IN_PROGRESS, CANCELED, COMPLETED)
      const statusCount = vaccinationPlans.reduce((acc, plan) => {
        const status = plan.status;
        if (status) {
          acc[status] = (acc[status] || 0) + 1;
        }
        return acc;
      }, {});
      
      console.log('Vaccination plan status distribution:', statusCount);
      
      // Map to expected format for the chart
      const waitingParent = statusCount.WAITING_PARENT || 0;
      const inProgress = statusCount.IN_PROGRESS || 0;
      const completed = statusCount.COMPLETED || 0;
      const canceled = statusCount.CANCELED || 0;
      
      const total = vaccinationPlans.length;
      const completionRate = total > 0 ? (((completed + canceled) / total) * 100).toFixed(1) : 0;
      
      return {
        total: total,
        accepted: completed, // Completed plans as "accepted"
        pending: waitingParent + inProgress, // Waiting and in-progress as "pending"
        rejected: canceled, // Canceled plans as "rejected"
        completionRate: completionRate,
        statusBreakdown: {
          waitingParent: waitingParent,
          inProgress: inProgress,
          completed: completed,
          canceled: canceled
        },
        rawData: vaccinationPlans // Include raw data for debugging
      };
    } catch (error) {
      console.error('Error fetching vaccination report:', error);
      
      // Check if it's an authentication error
      if (error.message.includes('Unauthorized') || error.message.includes('Forbidden')) {
        console.warn('Authentication issue detected for vaccination plans');
      }
      
      // Fallback data
      return {
        total: 800,
        accepted: 650,
        pending: 100,
        rejected: 50,
        completionRate: 87.5,
        statusBreakdown: {
          waitingParent: 80,
          inProgress: 20,
          completed: 650,
          canceled: 50
        }
      };
    }
  },

  // 4. Lấy thống kê sự cố y tế
  getMedicalEventsStatistics: async () => {
    try {
      const token = await getAuthToken();

      if (!token) {
        console.warn('No authentication token found for medical incidents');
        throw new Error('Authentication token not found');
      }
      
      console.log('Fetching medical incidents from API');
      
      const response = await fetch(`${API_BASE_URL}/medical-incidents`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        credentials: 'include'
      });
      
      console.log('Medical incidents response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Medical incidents API error:', errorText);
        
        if (response.status === 401) {
          throw new Error('Unauthorized: Bạn cần đăng nhập để truy cập dữ liệu sự cố y tế');
        } else if (response.status === 403) {
          throw new Error('Forbidden: Bạn không có quyền truy cập dữ liệu sự cố y tế');
        } else {
          throw new Error(`Failed to fetch medical incidents: ${response.status} ${response.statusText}`);
        }
      }
      
      const incidents = await response.json();
      console.log('Successfully fetched medical incidents:', incidents.length);
      
      // Count by severity level
      const severityCount = incidents.reduce((acc, incident) => {
        const severity = incident.severityLevel;
        if (severity) {
          // Normalize severity level names
          const normalizedSeverity = severity.toLowerCase();
          acc[normalizedSeverity] = (acc[normalizedSeverity] || 0) + 1;
        }
        return acc;
      }, {});
      
      // Count by incident type
      const typeCount = incidents.reduce((acc, incident) => {
        const type = incident.incidentType;
        if (type) {
          // Normalize type names
          const normalizedType = type.toLowerCase();
          acc[normalizedType] = (acc[normalizedType] || 0) + 1;
        }
        return acc;
      }, {});
      
             console.log('Severity distribution:', severityCount);
       console.log('Type distribution:', typeCount);
       
       // Use fallback data if no incidents found
       if (incidents.length === 0) {
         console.log('No medical incidents found in database, using fallback data');
         return {
           severity: {
             mild: 45,
             moderate: 12,
             severe: 3
           },
           type: {
             illness: 35,
             injury: 20,
             emergency: 5
           },
           total: 60,
           rawData: [],
           usingFallback: true
         };
       }
       
       return {
         severity: {
           mild: severityCount.mild || severityCount.nhẹ || severityCount.nhe || 0,
           moderate: severityCount.moderate || severityCount.vừa || severityCount.vua || severityCount.trungbình || severityCount['trung bình'] || 0,
           severe: severityCount.severe || severityCount.nặng || severityCount.nang || severityCount.nghiêmtrọng || severityCount['nghiêm trọng'] || 0
         },
         type: {
           illness: typeCount.illness || typeCount.bệnh || typeCount.benh || 0,
           injury: typeCount.injury || typeCount.chấnthương || typeCount['chấn thương'] || 0,
           emergency: typeCount.emergency || typeCount.cấpcứu || typeCount['cấp cứu'] || 0
         },
         total: incidents.length,
         rawData: incidents // Include raw data for debugging
       };
    } catch (error) {
      console.error('Error fetching medical events statistics:', error);
      
      // Check if it's an authentication error
      if (error.message.includes('Unauthorized') || error.message.includes('Forbidden')) {
        console.warn('Authentication issue detected for medical incidents');
      }
      
      // Fallback data
      return {
        severity: {
          mild: 45,
          moderate: 12,
          severe: 3
        },
        type: {
          illness: 35,
          injury: 20,
          emergency: 5
        },
        total: 60
      };
    }
  },

  // 5. Lấy thống kê tư vấn y tế
  getConsultationStatistics: async () => {
    try {
      // Import service để sử dụng
      const { getConsultations, getConsultationStats } = await import('../../../../../services/APINurse/consultationService.js');
      
      const consultations = await getConsultations();
      const stats = await getConsultationStats();
      
      // Đếm theo loại tư vấn
      const typeCount = consultations.reduce((acc, consultation) => {
        acc[consultation.type] = (acc[consultation.type] || 0) + 1;
        return acc;
      }, {});
      
      return {
        types: typeCount,
        stats: stats,
        total: consultations.length
      };
    } catch (error) {
      console.error('Error fetching consultation statistics:', error);
      // Fallback data
      return {
        types: {
          'Cảnh báo sức khỏe': 25,
          'Tư vấn dinh dưỡng': 18,
          'Nhắc nhở tiêm chủng': 15,
          'Đề xuất hoạt động': 8,
          'Khác': 12
        },
        stats: {
          total: 78,
          read: 65,
          unread: 13,
          replied: 45,
          urgent: 8
        },
        total: 78
      };
    }
  },

  // 6. Lấy thống kê BMI theo khối lớp (từ medical checkups API)
  getBMIStatisticsByGrade: async () => {
    try {
      const token = await getAuthToken();

      if (!token) {
        console.warn('No authentication token found for medical checkups');
        throw new Error('Authentication token not found');
      }
      
      console.log('Fetching medical checkups for BMI analysis');
      
      const response = await fetch(`${API_BASE_URL}/medical-checkups`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        credentials: 'include'
      });
      
      console.log('Medical checkups response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Medical checkups API error:', errorText);
        
        if (response.status === 401) {
          throw new Error('Unauthorized: Bạn cần đăng nhập để truy cập dữ liệu khám sức khỏe');
        } else if (response.status === 403) {
          throw new Error('Forbidden: Bạn không có quyền truy cập dữ liệu khám sức khỏe');
        } else {
          throw new Error(`Failed to fetch medical checkups: ${response.status} ${response.statusText}`);
        }
      }
      
      const checkups = await response.json();
      console.log('Successfully fetched medical checkups:', checkups.length);
      
      // Nhóm theo khối lớp và thống kê BMI
      const gradeStats = {};
      
      checkups.forEach(checkup => {
        // Extract grade from student class
        const grade = extractGradeFromClass(checkup.studentClass) || 'Khối Khác';
        
        if (!gradeStats[grade]) {
          gradeStats[grade] = {
            thieuCan: 0,
            binhThuong: 0,
            thuaCan: 0,
            beoXhi: 0
          };
        }
        
        // Phân loại BMI theo tiêu chuẩn Việt Nam cho trẻ em/thanh thiếu niên
        const bmi = checkup.bmi;
        if (bmi && bmi > 0) {
          if (bmi < 18.5) {
            gradeStats[grade].thieuCan++;
          } else if (bmi < 23) {
            gradeStats[grade].binhThuong++;
          } else if (bmi < 25) {
            gradeStats[grade].thuaCan++;
          } else {
            gradeStats[grade].beoXhi++;
          }
        }
      });
      
             console.log('BMI statistics by grade:', gradeStats);
       
       // Ensure we have data for common grades even if empty (Primary school: grades 1-5)
       const commonGrades = ['Khối 1', 'Khối 2', 'Khối 3', 'Khối 4', 'Khối 5'];
       commonGrades.forEach(grade => {
         if (!gradeStats[grade]) {
           gradeStats[grade] = {
             thieuCan: 0,
             binhThuong: 0,
             thuaCan: 0,
             beoXhi: 0
           };
         }
       });
       
              // Add metadata for display
       const totalWithBMI = checkups.filter(c => c.bmi && c.bmi > 0).length;
       
       // Use fallback data if no checkups or no BMI data found
       if (checkups.length === 0 || totalWithBMI === 0) {
         console.log('No medical checkups with BMI data found, using fallback data');
         return {
           'Khối 1': { thieuCan: 3, binhThuong: 25, thuaCan: 4, beoXhi: 1 },
           'Khối 2': { thieuCan: 4, binhThuong: 28, thuaCan: 5, beoXhi: 2 },
           'Khối 3': { thieuCan: 2, binhThuong: 30, thuaCan: 6, beoXhi: 1 },
           'Khối 4': { thieuCan: 5, binhThuong: 32, thuaCan: 7, beoXhi: 2 },
           'Khối 5': { thieuCan: 3, binhThuong: 35, thuaCan: 8, beoXhi: 3 },
           _metadata: {
             totalCheckups: 0,
             totalWithBMI: 0,
             rawData: [],
             usingFallback: true
           }
         };
       }
       
       gradeStats._metadata = {
         totalCheckups: checkups.length,
         totalWithBMI: totalWithBMI,
         rawData: checkups
       };
      
      return gradeStats;
    } catch (error) {
      console.error('Error fetching BMI statistics:', error);
      
      // Check if it's an authentication error
      if (error.message.includes('Unauthorized') || error.message.includes('Forbidden')) {
        console.warn('Authentication issue detected for medical checkups');
      }
      
      // Fallback data (Primary school grades 1-5)
      return {
        'Khối 1': { thieuCan: 3, binhThuong: 25, thuaCan: 4, beoXhi: 1 },
        'Khối 2': { thieuCan: 4, binhThuong: 28, thuaCan: 5, beoXhi: 2 },
        'Khối 3': { thieuCan: 2, binhThuong: 30, thuaCan: 6, beoXhi: 1 },
        'Khối 4': { thieuCan: 5, binhThuong: 32, thuaCan: 7, beoXhi: 2 },
        'Khối 5': { thieuCan: 3, binhThuong: 35, thuaCan: 8, beoXhi: 3 }
      };
    }
  },

  // 7. Lấy tiến độ tiêm chủng 6 tháng qua (mock data vì cần thời gian thực)
  getVaccinationProgress: async () => {
    try {
      // Vì dữ liệu thực cần tracking theo thời gian, tạm dùng mock data
      const months = ['T1', 'T2', 'T3', 'T4', 'T5', 'T6'];
      const data = [120, 140, 160, 180, 200, 220]; // Mock tiến độ
      
      return {
        labels: months,
        datasets: [{
          label: 'Số lượng đã tiêm',
          data: data,
          borderColor: '#059669',
          backgroundColor: 'rgba(5, 150, 105, 0.1)',
          tension: 0.4
        }]
      };
    } catch (error) {
      console.error('Error fetching vaccination progress:', error);
      return {
        labels: ['T1', 'T2', 'T3', 'T4', 'T5', 'T6'],
        datasets: [{
          label: 'Số lượng đã tiêm',
          data: [120, 140, 160, 180, 200, 220],
          borderColor: '#059669',
          backgroundColor: 'rgba(5, 150, 105, 0.1)',
          tension: 0.4
        }]
      };
    }
  },

  // Helper function to test medical incidents API
  testMedicalIncidentsAPI: async () => {
    try {
      const token = await getAuthToken();
      
      if (!token) {
        throw new Error('No authentication token');
      }
      
      const response = await fetch(`${API_BASE_URL}/medical-incidents`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
      }
      
      const incidents = await response.json();
      console.log('Medical incidents API test successful:', {
        count: incidents.length,
        sampleIncident: incidents[0] || null,
        severityLevels: [...new Set(incidents.map(i => i.severityLevel).filter(Boolean))],
        incidentTypes: [...new Set(incidents.map(i => i.incidentType).filter(Boolean))]
      });
      
      return {
        success: true,
        count: incidents.length,
        incidents: incidents
      };
    } catch (error) {
      console.error('Medical incidents API test failed:', error);
             return {
         success: false,
         error: error.message
       };
     }
   },

   // Helper function to test vaccination plans API
   testVaccinationPlansAPI: async () => {
     try {
       const token = await getAuthToken();
       
       if (!token) {
         throw new Error('No authentication token');
       }
       
       const response = await fetch(`${API_BASE_URL}/vaccination-plans/getAllVaccinationPlans`, {
         method: 'GET',
         headers: {
           'Accept': 'application/json',
           'Authorization': `Bearer ${token}`
         },
         credentials: 'include'
       });
       
       if (!response.ok) {
         throw new Error(`API Error: ${response.status} ${response.statusText}`);
       }
       
       const plans = await response.json();
       console.log('Vaccination plans API test successful:', {
         count: plans.length,
         samplePlan: plans[0] || null,
         statuses: [...new Set(plans.map(p => p.status).filter(Boolean))],
         vaccineTypes: [...new Set(plans.map(p => p.vaccineName).filter(Boolean))]
       });
       
       return {
         success: true,
         count: plans.length,
         plans: plans
       };
     } catch (error) {
       console.error('Vaccination plans API test failed:', error);
       return {
         success: false,
         error: error.message
       };
     }
   },

   // Helper function to test medical checkups API
   testMedicalCheckupsAPI: async () => {
     try {
       const token = await getAuthToken();
       
       if (!token) {
         throw new Error('No authentication token');
       }
       
       const response = await fetch(`${API_BASE_URL}/medical-checkups`, {
         method: 'GET',
         headers: {
           'Accept': 'application/json',
           'Authorization': `Bearer ${token}`
         },
         credentials: 'include'
       });
       
       if (!response.ok) {
         throw new Error(`API Error: ${response.status} ${response.statusText}`);
       }
       
       const checkups = await response.json();
       console.log('Medical checkups API test successful:', {
         count: checkups.length,
         sampleCheckup: checkups[0] || null,
         withBMI: checkups.filter(c => c.bmi && c.bmi > 0).length,
         grades: [...new Set(checkups.map(c => extractGradeFromClass(c.studentClass)).filter(Boolean))],
         classes: [...new Set(checkups.map(c => c.studentClass).filter(Boolean))]
       });
       
       return {
         success: true,
         count: checkups.length,
         checkups: checkups
       };
     } catch (error) {
       console.error('Medical checkups API test failed:', error);
       return {
         success: false,
         error: error.message
       };
     }
   },

   // Helper function to test health campaigns API
   testHealthCampaignsAPI: async () => {
     try {
       const token = await getAuthToken();
       
       if (!token) {
         throw new Error('No authentication token');
       }
       
       const response = await fetch(`${API_BASE_URL}/health-campaigns`, {
         method: 'GET',
         headers: {
           'Accept': 'application/json',
           'Authorization': `Bearer ${token}`
         },
         credentials: 'include'
       });
       
       if (!response.ok) {
         throw new Error(`API Error: ${response.status} ${response.statusText}`);
       }
       
       const campaigns = await response.json();
       console.log('Health campaigns API test successful:', {
         count: campaigns.length,
         sampleCampaign: campaigns[0] || null,
         statuses: [...new Set(campaigns.map(c => c.status).filter(Boolean))],
         titles: campaigns.slice(0, 3).map(c => c.title || c.name)
       });
       
       return {
         success: true,
         count: campaigns.length,
         campaigns: campaigns
       };
     } catch (error) {
       console.error('Health campaigns API test failed:', error);
       return {
         success: false,
         error: error.message
       };
     }
   },

   // 8. Lấy thống kê chiến dịch sức khỏe theo trạng thái
   getHealthCampaignStatistics: async () => {
     try {
       const token = await getAuthToken();
       
       if (!token) {
         console.warn('No authentication token found for health campaigns');
         throw new Error('Authentication token not found');
       }
       
       console.log('Fetching health campaigns from API');
       
       const response = await fetch(`${API_BASE_URL}/health-campaigns`, {
         method: 'GET',
         headers: {
           'Accept': 'application/json',
           'Authorization': `Bearer ${token}`
         },
         credentials: 'include'
       });
       
       console.log('Health campaigns response status:', response.status);
       
       if (!response.ok) {
         const errorText = await response.text();
         console.error('Health campaigns API error:', errorText);
         
         if (response.status === 401) {
           throw new Error('Unauthorized: Bạn cần đăng nhập để truy cập dữ liệu chiến dịch sức khỏe');
         } else if (response.status === 403) {
           throw new Error('Forbidden: Bạn không có quyền truy cập dữ liệu chiến dịch sức khỏe');
         } else {
           throw new Error(`Failed to fetch health campaigns: ${response.status} ${response.statusText}`);
         }
       }
       
       const campaigns = await response.json();
       console.log('Successfully fetched health campaigns:', campaigns.length);
       
       // Count by status
       const statusCount = campaigns.reduce((acc, campaign) => {
         const status = campaign.status;
         if (status) {
           acc[status] = (acc[status] || 0) + 1;
         }
         return acc;
       }, {});
       
       console.log('Health campaign status distribution:', statusCount);
       
       // Use fallback data if no campaigns found
       if (campaigns.length === 0) {
         console.log('No health campaigns found in database, using fallback data');
         return {
           preparing: 3,
           ongoing: 2,
           completed: 8,
           cancelled: 1,
           total: 14,
           rawData: [],
           usingFallback: true
         };
       }
       
       return {
         preparing: statusCount.PREPARING || 0,
         ongoing: statusCount.ONGOING || 0,
         completed: statusCount.COMPLETED || 0,
         cancelled: statusCount.CANCELLED || 0,
         total: campaigns.length,
         rawData: campaigns
       };
     } catch (error) {
       console.error('Error fetching health campaign statistics:', error);
       
       // Check if it's an authentication error
       if (error.message.includes('Unauthorized') || error.message.includes('Forbidden')) {
         console.warn('Authentication issue detected for health campaigns');
       }
       
       // Fallback data
       return {
         preparing: 3,
         ongoing: 2,
         completed: 8,
         cancelled: 1,
         total: 14
       };
     }
   },

   // 9. Lấy danh sách chiến dịch sức khỏe gần đây cho bảng sự kiện
   getRecentHealthCampaigns: async (limit = 10) => {
     try {
       const token = await getAuthToken();
       
       if (!token) {
         console.warn('No authentication token found for health campaigns');
         throw new Error('Authentication token not found');
       }
       
       console.log('Fetching recent health campaigns from API');
       
       const response = await fetch(`${API_BASE_URL}/health-campaigns`, {
         method: 'GET',
         headers: {
           'Accept': 'application/json',
           'Authorization': `Bearer ${token}`
         },
         credentials: 'include'
       });
       
       console.log('Health campaigns response status:', response.status);
       
       if (!response.ok) {
         const errorText = await response.text();
         console.error('Health campaigns API error:', errorText);
         
         if (response.status === 401) {
           throw new Error('Unauthorized: Bạn cần đăng nhập để truy cập dữ liệu chiến dịch sức khỏe');
         } else if (response.status === 403) {
           throw new Error('Forbidden: Bạn không có quyền truy cập dữ liệu chiến dịch sức khỏe');
         } else {
           throw new Error(`Failed to fetch health campaigns: ${response.status} ${response.statusText}`);
         }
       }
       
       const campaigns = await response.json();
       console.log('Successfully fetched health campaigns:', campaigns.length);
       
       // Sort by most recent and limit results
       const sortedCampaigns = campaigns
         .sort((a, b) => safeParseDate(b.createdAt || b.startDate) - safeParseDate(a.createdAt || a.startDate))
         .slice(0, limit);
       
       // Transform data for the events table
       const recentEvents = sortedCampaigns.map(campaign => ({
         id: campaign.id,
         title: campaign.title || campaign.name || 'Chiến dịch sức khỏe',
         date: formatDate(campaign.startDate || campaign.createdAt),
         type: 'health-campaign',
         status: getStatusFromCampaignStatus(campaign.status),
         originalData: campaign
       }));
       
       return recentEvents;
     } catch (error) {
       console.error('Error fetching recent health campaigns:', error);
       
       // Check if it's an authentication error
       if (error.message.includes('Unauthorized') || error.message.includes('Forbidden')) {
         console.warn('Authentication issue detected for health campaigns');
       }
       
       // Fallback data
       return [
         {
           id: 1,
           title: "Kiểm tra sức khỏe định kỳ K1-K5",
           date: "20/06/2025",
           type: "health-campaign",
           status: "ongoing",
         },
         {
           id: 2,
           title: "Chiến dịch tiêm chủng mùa hè",
           date: "15/06/2025",
           type: "health-campaign",
           status: "completed",
         },
         {
           id: 3,
           title: "Khám sàng lọc răng miệng",
           date: "10/06/2025",
           type: "health-campaign",
           status: "preparing",
         },
       ];
     }
   },

   // 10. Lấy thống kê số lượng học sinh theo khối lớp
   getStudentsByGradeLevel: async () => {
     try {
       const token = await getAuthToken();
       
       if (!token) {
         console.warn('No authentication token found for students');
         throw new Error('Authentication token not found');
       }
       
       console.log('Fetching students from API');
       
       const response = await fetch(`${API_BASE_URL}/students`, {
         method: 'GET',
         headers: {
           'Accept': 'application/json',
           'Authorization': `Bearer ${token}`
         },
         credentials: 'include'
       });
       
       console.log('Students response status:', response.status);
       
       if (!response.ok) {
         const errorText = await response.text();
         console.error('Students API error:', errorText);
         
         if (response.status === 401) {
           throw new Error('Unauthorized: Bạn cần đăng nhập để truy cập dữ liệu học sinh');
         } else if (response.status === 403) {
           throw new Error('Forbidden: Bạn không có quyền truy cập dữ liệu học sinh');
         } else {
           throw new Error(`Failed to fetch students: ${response.status} ${response.statusText}`);
         }
       }
       
       const students = await response.json();
       console.log('Successfully fetched students:', students.length);
       
       // Count students by grade level
       const gradeStats = students.reduce((acc, student) => {
         const gradeLevel = student.gradeLevel;
         if (gradeLevel) {
           // Normalize grade level format
           const normalizedGrade = normalizeGradeLevel(gradeLevel);
           acc[normalizedGrade] = (acc[normalizedGrade] || 0) + 1;
         }
         return acc;
       }, {});
       
       console.log('Students by grade level:', gradeStats);
       
       // Sort grades for consistent display (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12)
       const sortedGrades = Object.keys(gradeStats).sort((a, b) => {
         const aNum = parseInt(a.replace(/\D/g, ''));
         const bNum = parseInt(b.replace(/\D/g, ''));
         return aNum - bNum;
       });
       
       const sortedGradeStats = {};
       sortedGrades.forEach(grade => {
         sortedGradeStats[grade] = gradeStats[grade];
       });
       
       // Use fallback data if no students found
       if (students.length === 0) {
         console.log('No students found in database, using fallback data');
         return {
           gradeStats: {
             'Khối 1': 45,
             'Khối 2': 48,
             'Khối 3': 52,
             'Khối 4': 47,
             'Khối 5': 50,
             'Khối 6': 55,
             'Khối 7': 53,
             'Khối 8': 49,
             'Khối 9': 51,
             'Khối 10': 46,
             'Khối 11': 44,
             'Khối 12': 42
           },
           total: 582,
           rawData: [],
           usingFallback: true
         };
       }
       
       return {
         gradeStats: sortedGradeStats,
         total: students.length,
         rawData: students
       };
     } catch (error) {
       console.error('Error fetching students by grade level:', error);
       
       // Check if it's an authentication error
       if (error.message.includes('Unauthorized') || error.message.includes('Forbidden')) {
         console.warn('Authentication issue detected for students');
       }
       
       // Fallback data
       return {
         gradeStats: {
           'Khối 1': 45,
           'Khối 2': 48,
           'Khối 3': 52,
           'Khối 4': 47,
           'Khối 5': 50,
           'Khối 6': 55,
           'Khối 7': 53,
           'Khối 8': 49,
           'Khối 9': 51,
           'Khối 10': 46,
           'Khối 11': 44,
           'Khối 12': 42
         },
         total: 582
       };
     }
   },

   // Helper function to test students API
   testStudentsAPI: async () => {
     try {
       const token = await getAuthToken();
       
       if (!token) {
         throw new Error('No authentication token');
       }
       
       const response = await fetch(`${API_BASE_URL}/students`, {
         method: 'GET',
         headers: {
           'Accept': 'application/json',
           'Authorization': `Bearer ${token}`
         },
         credentials: 'include'
       });
       
       if (!response.ok) {
         throw new Error(`API Error: ${response.status} ${response.statusText}`);
       }
       
       const students = await response.json();
       console.log('Students API test successful:', {
         count: students.length,
         sampleStudent: students[0] || null,
         gradeLevels: [...new Set(students.map(s => s.gradeLevel).filter(Boolean))],
         classes: [...new Set(students.map(s => s.studentClass).filter(Boolean))]
       });
       
       return {
         success: true,
         count: students.length,
         students: students
       };
     } catch (error) {
       console.error('Students API test failed:', error);
       return {
         success: false,
         error: error.message
       };
     }
   },

   // 11. Lấy thống kê medication instructions theo trạng thái
   getMedicationInstructionStatistics: async () => {
     try {
       const token = await getAuthToken();
       
       if (!token) {
         console.warn('No authentication token found for medication instructions');
         throw new Error('Authentication token not found');
       }
       
       console.log('Fetching medication instructions from API');
       
       const response = await fetch(`${API_BASE_URL}/nurse-medication-approvals/all-requests`, {
         method: 'GET',
         headers: {
           'Accept': 'application/json',
           'Authorization': `Bearer ${token}`
         },
         credentials: 'include'
       });
       
       console.log('Medication instructions response status:', response.status);
       
       if (!response.ok) {
         const errorText = await response.text();
         console.error('Medication instructions API error:', errorText);
         
         if (response.status === 401) {
           throw new Error('Unauthorized: Bạn cần đăng nhập để truy cập dữ liệu thuốc');
         } else if (response.status === 403) {
           throw new Error('Forbidden: Bạn không có quyền truy cập dữ liệu thuốc');
         } else {
           throw new Error(`Failed to fetch medication instructions: ${response.status} ${response.statusText}`);
         }
       }
       
       const medications = await response.json();
       console.log('Successfully fetched medication instructions:', medications.length);
       
       // Count by status for approval/rejection chart
       const approvalStatusCount = medications.reduce((acc, med) => {
         const status = med.status;
         if (status) {
           acc[status] = (acc[status] || 0) + 1;
         }
         return acc;
       }, {});
       
       // Count by consumption status chart
       const consumptionStatusCount = medications.reduce((acc, med) => {
         const status = med.status;
         if (status && ['FULLY_TAKEN', 'PARTIALLY_TAKEN', 'EXPIRED'].includes(status)) {
           acc[status] = (acc[status] || 0) + 1;
         }
         return acc;
       }, {});
       
       console.log('Medication approval status distribution:', approvalStatusCount);
       console.log('Medication consumption status distribution:', consumptionStatusCount);
       
       // Use fallback data if no medications found
       if (medications.length === 0) {
         console.log('No medications found in database, using fallback data');
         return {
           approvalStats: {
             approved: 45,
             rejected: 8,
             pending: 12,
             total: 65
           },
           consumptionStats: {
             fullyTaken: 38,
             partiallyTaken: 5,
             expired: 2,
             total: 45
           },
           totalMedications: 65,
           statusBreakdown: {
             PENDING_APPROVAL: 12,
             APPROVED: 45,
             REJECTED: 8,
             FULLY_TAKEN: 38,
             PARTIALLY_TAKEN: 5,
             EXPIRED: 2
           },
           rawData: [],
           usingFallback: true
         };
       }
       
       return {
         // Approval/Rejection statistics
         approvalStats: {
           approved: approvalStatusCount.APPROVED || 0,
           rejected: approvalStatusCount.REJECTED || 0,
           pending: approvalStatusCount.PENDING_APPROVAL || 0,
           total: (approvalStatusCount.APPROVED || 0) + (approvalStatusCount.REJECTED || 0) + (approvalStatusCount.PENDING_APPROVAL || 0)
         },
         // Consumption statistics  
         consumptionStats: {
           fullyTaken: consumptionStatusCount.FULLY_TAKEN || 0,
           partiallyTaken: consumptionStatusCount.PARTIALLY_TAKEN || 0,
           expired: consumptionStatusCount.EXPIRED || 0,
           total: (consumptionStatusCount.FULLY_TAKEN || 0) + (consumptionStatusCount.PARTIALLY_TAKEN || 0) + (consumptionStatusCount.EXPIRED || 0)
         },
         totalMedications: medications.length,
         statusBreakdown: approvalStatusCount,
         rawData: medications
       };
     } catch (error) {
       console.error('Error fetching medication instruction statistics:', error);
       
       // Check if it's an authentication error
       if (error.message.includes('Unauthorized') || error.message.includes('Forbidden')) {
         console.warn('Authentication issue detected for medication instructions');
       }
       
       // Fallback data
       return {
         approvalStats: {
           approved: 45,
           rejected: 8,
           pending: 12,
           total: 65
         },
         consumptionStats: {
           fullyTaken: 38,
           partiallyTaken: 5,
           expired: 2,
           total: 45
         },
         totalMedications: 65,
         statusBreakdown: {
           PENDING_APPROVAL: 12,
           APPROVED: 45,
           REJECTED: 8,
           FULLY_TAKEN: 38,
           PARTIALLY_TAKEN: 5,
           EXPIRED: 2
         }
       };
     }
   },

   // Helper function to test medication instructions API
   testMedicationInstructionsAPI: async () => {
     try {
       const token = await getAuthToken();
       
       if (!token) {
         throw new Error('No authentication token');
       }
       
       const response = await fetch(`${API_BASE_URL}/nurse-medication-approvals/all-requests`, {
         method: 'GET',
         headers: {
           'Accept': 'application/json',
           'Authorization': `Bearer ${token}`
         },
         credentials: 'include'
       });
       
       if (!response.ok) {
         throw new Error(`API Error: ${response.status} ${response.statusText}`);
       }
       
       const medications = await response.json();
       console.log('Medication instructions API test successful:', {
         count: medications.length,
         sampleMedication: medications[0] || null,
         statuses: [...new Set(medications.map(m => m.status).filter(Boolean))],
         medicationNames: [...new Set(medications.map(m => m.medicationName).filter(Boolean))].slice(0, 5)
       });
       
       return {
         success: true,
         count: medications.length,
         medications: medications
       };
     } catch (error) {
       console.error('Medication instructions API test failed:', error);
       return {
         success: false,
         error: error.message
       };
     }
   },

   // 12. Lấy thống kê vaccinations theo loại
   getVaccinationTypeStatistics: async () => {
     try {
       const token = await getAuthToken();
       
       if (!token) {
         console.warn('No authentication token found for vaccinations');
         throw new Error('Authentication token not found');
       }
       
       console.log('Fetching vaccinations from API');
       
       const response = await fetch(`${API_BASE_URL}/vaccinations/getAllVaccination`, {
         method: 'GET',
         headers: {
           'Accept': 'application/json',
           'Authorization': `Bearer ${token}`
         },
         credentials: 'include'
       });
       
       console.log('Vaccinations response status:', response.status);
       
       if (!response.ok) {
         const errorText = await response.text();
         console.error('Vaccinations API error:', errorText);
         
         if (response.status === 401) {
           throw new Error('Unauthorized: Bạn cần đăng nhập để truy cập dữ liệu tiêm chủng');
         } else if (response.status === 403) {
           throw new Error('Forbidden: Bạn không có quyền truy cập dữ liệu tiêm chủng');
         } else {
           throw new Error(`Failed to fetch vaccinations: ${response.status} ${response.statusText}`);
         }
       }
       
       const vaccinations = await response.json();
       console.log('Successfully fetched vaccinations:', vaccinations.length);
       
       // Count by vaccination type
       const typeCount = vaccinations.reduce((acc, vaccination) => {
         const type = vaccination.vaccinationType;
         if (type) {
           acc[type] = (acc[type] || 0) + 1;
         }
         return acc;
       }, {});
       
       console.log('Vaccination type distribution:', typeCount);
       
       // Use fallback data if no vaccinations found
       if (vaccinations.length === 0) {
         console.log('No vaccinations found in database, using fallback data');
         return {
           schoolPlan: 320,
           parentDeclared: 180,
           catchUp: 45,
           total: 545,
           typeBreakdown: {
             SCHOOL_PLAN: 320,
             PARENT_DECLARED: 180,
             CATCH_UP: 45
           },
           rawData: [],
           usingFallback: true
         };
       }
       
       return {
         schoolPlan: typeCount.SCHOOL_PLAN || 0,
         parentDeclared: typeCount.PARENT_DECLARED || 0,
         catchUp: typeCount.CATCH_UP || 0,
         total: vaccinations.length,
         rawData: vaccinations,
         typeBreakdown: typeCount
       };
     } catch (error) {
       console.error('Error fetching vaccination type statistics:', error);
       
       // Check if it's an authentication error
       if (error.message.includes('Unauthorized') || error.message.includes('Forbidden')) {
         console.warn('Authentication issue detected for vaccinations');
       }
       
       // Fallback data
       return {
         schoolPlan: 320,
         parentDeclared: 180,
         catchUp: 45,
         total: 545,
         typeBreakdown: {
           SCHOOL_PLAN: 320,
           PARENT_DECLARED: 180,
           CATCH_UP: 45
         }
       };
     }
   },

   // Helper function to test vaccinations API
   testVaccinationsAPI: async () => {
     try {
       const token = await getAuthToken();
       
       if (!token) {
         throw new Error('No authentication token');
       }
       
       const response = await fetch(`${API_BASE_URL}/vaccinations/getAllVaccination`, {
         method: 'GET',
         headers: {
           'Accept': 'application/json',
           'Authorization': `Bearer ${token}`
         },
         credentials: 'include'
       });
       
       if (!response.ok) {
         throw new Error(`API Error: ${response.status} ${response.statusText}`);
       }
       
       const vaccinations = await response.json();
       console.log('Vaccinations API test successful:', {
         count: vaccinations.length,
         sampleVaccination: vaccinations[0] || null,
         types: [...new Set(vaccinations.map(v => v.vaccinationType).filter(Boolean))],
         vaccineNames: [...new Set(vaccinations.map(v => v.vaccineName).filter(Boolean))].slice(0, 5)
       });
       
       return {
         success: true,
         count: vaccinations.length,
         vaccinations: vaccinations
       };
     } catch (error) {
       console.error('Vaccinations API test failed:', error);
       return {
         success: false,
         error: error.message
       };
     }
   },

   // 13. Lấy danh sách kế hoạch tiêm chủng gần đây cho bảng vaccine campaigns
   getRecentVaccinationPlans: async (limit = 10) => {
     try {
       const token = await getAuthToken();
       
       if (!token) {
         console.warn('No authentication token found for vaccination plans');
         throw new Error('Authentication token not found');
       }
       
       console.log('Fetching recent vaccination plans from API');
       
       const response = await fetch(`${API_BASE_URL}/vaccination-plans/getAllVaccinationPlans`, {
         method: 'GET',
         headers: {
           'Accept': 'application/json',
           'Authorization': `Bearer ${token}`
         },
         credentials: 'include'
       });
       
       console.log('Vaccination plans response status:', response.status);
       
       if (!response.ok) {
         const errorText = await response.text();
         console.error('Vaccination plans API error:', errorText);
         
         if (response.status === 401) {
           throw new Error('Unauthorized: Bạn cần đăng nhập để truy cập dữ liệu kế hoạch tiêm chủng');
         } else if (response.status === 403) {
           throw new Error('Forbidden: Bạn không có quyền truy cập dữ liệu kế hoạch tiêm chủng');
         } else {
           throw new Error(`Failed to fetch vaccination plans: ${response.status} ${response.statusText}`);
         }
       }
       
       const vaccinationPlans = await response.json();
       console.log('Successfully fetched vaccination plans:', vaccinationPlans.length);
       
       // Sort by vaccination date (most recent first) and limit results
       const sortedPlans = vaccinationPlans
         .sort((a, b) => {
           const dateA = safeParseDate(a.vaccinationDate);
           const dateB = safeParseDate(b.vaccinationDate);
           return dateB - dateA; // Most recent first
         })
         .slice(0, limit);
       
       // Transform data for the vaccine campaigns table
       const recentVaccinations = sortedPlans.map(plan => ({
         id: plan.id || Math.random(), // Fallback ID if not provided
         name: plan.name || 'Kế hoạch tiêm chủng',
         date: formatDate(plan.vaccinationDate),
         type: 'vaccination-plan',
         status: getStatusFromVaccinationStatus(plan.status),
         originalData: plan
       }));
       
       return recentVaccinations;
     } catch (error) {
       console.error('Error fetching recent vaccination plans:', error);
       
       // Check if it's an authentication error
       if (error.message.includes('Unauthorized') || error.message.includes('Forbidden')) {
         console.warn('Authentication issue detected for vaccination plans');
       }
       
       // Fallback data
       return [
         {
           id: 1,
           name: "Tiêm phòng Sởi - Rubella",
           date: "25/06/2025",
           type: "vaccination-plan",
           status: "in-progress",
         },
         {
           id: 2,
           name: "Tiêm chủng Viêm gan B",
           date: "22/06/2025",
           type: "vaccination-plan",
           status: "completed",
         },
         {
           id: 3,
           name: "Tiêm phòng Cúm mùa",
           date: "18/06/2025",
           type: "vaccination-plan",
           status: "upcoming",
         },
         {
           id: 4,
           name: "Tiêm phòng HPV",
           date: "15/06/2025",
           type: "vaccination-plan",
           status: "cancelled",
         },
         {
           id: 5,
           name: "Tiêm phòng Bạch hầu - Ho gà",
           date: "12/06/2025",
           type: "vaccination-plan",
           status: "completed",
         },
       ];
     }
   }
 };

// Helper function để normalize grade level
function normalizeGradeLevel(gradeLevel) {
  if (!gradeLevel) return 'Khác';
  
  const grade = gradeLevel.toString().toLowerCase().trim();
  
  // Extract number from grade level
  const gradeMatch = grade.match(/(\d+)/);
  if (gradeMatch) {
    const gradeNumber = parseInt(gradeMatch[1]);
    if (gradeNumber >= 1 && gradeNumber <= 12) {
      return `Khối ${gradeNumber}`;
    }
  }
  
  // Handle various formats
  if (grade.includes('1') || grade.includes('một')) return 'Khối 1';
  if (grade.includes('2') || grade.includes('hai')) return 'Khối 2';
  if (grade.includes('3') || grade.includes('ba')) return 'Khối 3';
  if (grade.includes('4') || grade.includes('bốn')) return 'Khối 4';
  if (grade.includes('5') || grade.includes('năm')) return 'Khối 5';
  if (grade.includes('6') || grade.includes('sáu')) return 'Khối 6';
  if (grade.includes('7') || grade.includes('bảy')) return 'Khối 7';
  if (grade.includes('8') || grade.includes('tám')) return 'Khối 8';
  if (grade.includes('9') || grade.includes('chín')) return 'Khối 9';
  if (grade.includes('10') || grade.includes('mười')) return 'Khối 10';
  if (grade.includes('11') || grade.includes('mười một')) return 'Khối 11';
  if (grade.includes('12') || grade.includes('mười hai')) return 'Khối 12';
  
  return gradeLevel; // Return original if can't normalize
}

// Helper function để format date
function formatDate(dateString) {
  return safeDateFormat(dateString);
}

// Helper function để map campaign status to display status
function getStatusFromCampaignStatus(status) {
  if (!status) return 'pending';
  
  switch (status.toUpperCase()) {
    case 'COMPLETED':
      return 'completed';
    case 'ONGOING':
      return 'in-progress';
    case 'PREPARING':
      return 'upcoming';
    case 'CANCELLED':
      return 'cancelled';
    default:
      return 'pending';
  }
}

// Helper function để map vaccination plan status to display status
function getStatusFromVaccinationStatus(status) {
  if (!status) return 'pending';
  
  switch (status.toUpperCase()) {
    case 'COMPLETED':
      return 'completed';
    case 'IN_PROGRESS':
      return 'in-progress';
    case 'WAITING_PARENT':
      return 'upcoming';
    case 'CANCELED':
    case 'CANCELLED':
      return 'cancelled';
    default:
      return 'pending';
  }
}

// Helper function để extract grade từ class name
function extractGradeFromClass(className) {
  if (!className) return null;
  
  // Handle various Vietnamese class name formats
  // Examples: "Lớp 6A", "6A", "6a", "lớp 6A", "Khối 6", "Grade 6", etc.
  const normalizedClassName = className.toString().toLowerCase();
  
  // Extract number from class name
  const gradeMatch = normalizedClassName.match(/(\d+)/);
  if (!gradeMatch) return null;
  
  const gradeNumber = parseInt(gradeMatch[1]);
  
  // Handle primary school grades (1-5) - main focus
  if (gradeNumber >= 1 && gradeNumber <= 5) {
    return `Khối ${gradeNumber}`;
  }
  
  // Also handle secondary school grades (6-12) if needed
  if (gradeNumber >= 6 && gradeNumber <= 12) {
    return `Khối ${gradeNumber}`;
  }
  
  return null;
}

// Make service available for debugging
if (typeof window !== 'undefined') {
  window.dashboardService = dashboardService;
}

export default dashboardService; 