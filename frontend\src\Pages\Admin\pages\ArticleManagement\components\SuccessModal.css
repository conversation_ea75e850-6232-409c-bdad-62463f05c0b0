/* ===== SUCCESS MODAL ===== */
.success-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.success-modal-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 400px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  animation: slideIn 0.3s ease;
  position: relative;
}

.success-modal-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 2rem 1rem;
  position: relative;
}

.success-modal-icon {
  font-size: 4rem;
  color: #10b981;
  animation: bounceIn 0.6s ease;
}

.success-modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.success-modal-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.success-modal-body {
  padding: 0 2rem 1.5rem;
  text-align: center;
}

.success-modal-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin: 0 0 0.5rem 0;
}

.success-modal-message {
  font-size: 1rem;
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
}

.success-modal-footer {
  padding: 1.5rem 2rem 2rem;
  display: flex;
  justify-content: center;
}

.success-modal-btn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.success-modal-btn:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.success-modal-btn:active {
  transform: translateY(0);
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
  .success-modal-content {
    margin: 1rem;
    max-width: none;
    width: calc(100% - 2rem);
  }
  
  .success-modal-header {
    padding: 1.5rem 1.5rem 1rem;
  }
  
  .success-modal-body {
    padding: 0 1.5rem 1rem;
  }
  
  .success-modal-footer {
    padding: 1rem 1.5rem 1.5rem;
  }
  
  .success-modal-title {
    font-size: 1.25rem;
  }
  
  .success-modal-message {
    font-size: 0.9rem;
  }
}
