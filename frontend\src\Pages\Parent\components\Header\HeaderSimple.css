/* Simple Header - Clean and Stable */

.simple-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 130px; /* Fixed height - no dynamic changes */
  z-index: 1000;
  background: linear-gradient(135deg, #015c92 0%, #2d82b5 50%, #428cd4 100%);
  box-shadow: 0 2px 10px rgba(1, 92, 146, 0.3);
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
}

.simple-header.scrolled {
  background: linear-gradient(135deg, rgba(1, 92, 146, 0.95) 0%, rgba(45, 130, 181, 0.95) 50%, rgba(66, 140, 212, 0.95) 100%);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(1, 92, 146, 0.4);
}

.simple-header-container {
  height: 100%;
  width: 100%;
  max-width: 1520px; /* Updated to match layout width */
  margin: 0 auto;
  padding: 0 2rem;
}

.simple-header-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
}

/* Logo */
.simple-header-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-decoration: none;
  color: white;
  font-weight: 600;
  font-size: 1.5rem;
  transition: transform 0.2s ease;
}

.simple-header-logo:hover {
  transform: scale(1.05);
}

.simple-header-logo img {
  width: 40px;
  height: 40px;
  border-radius: 8px;
}

.simple-logo-text {
  font-family: 'Be Vietnam Pro', sans-serif;
  font-weight: 700;
}

.simple-logo-text span {
  color: #88cdf6;
}

/* Navigation */
.simple-nav {
  flex: 1;
  display: flex;
  justify-content: center;
  position: relative;
}

.simple-nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 0.5rem;
  align-items: center;
}

.simple-nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  border-radius: 6px;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.simple-nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transform: translateY(-1px);
}

.simple-nav-link.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Mobile toggle */
.simple-mobile-toggle {
  display: none;
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.simple-mobile-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Header Actions */
.simple-header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.simple-notification-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  color: white;
  text-decoration: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;
  font-size: 1.1rem;
}

.simple-notification-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.simple-user-greeting {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
  font-size: 0.9rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  font-weight: 500;
}

.simple-login-btn,
.simple-logout-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  text-decoration: none;
  border: none;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
}

.simple-login-btn:hover,
.simple-logout-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 992px) {
  .simple-header {
    height: 120px;
  }
  
  .simple-header-container {
    padding: 0 1.5rem;
  }
  
  .simple-nav-list {
    gap: 0.25rem;
  }
  
  .simple-nav-link {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 640px) { /* Changed from 768px to 640px so navigation shows on tablets */
  .simple-header {
    height: 110px;
  }
  
  .simple-header-container {
    padding: 0 1rem;
  }
  
  .simple-mobile-toggle {
    display: block;
  }
  
  .simple-nav-list {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #015c92 0%, #2d82b5 50%, #428cd4 100%);
    flex-direction: column;
    padding: 1rem;
    box-shadow: 0 4px 20px rgba(1, 92, 146, 0.4);
    border-radius: 0 0 12px 12px;
    transform: translateY(-10px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }
  
  .simple-nav-list.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }
  
  .simple-nav-list li {
    width: 100%;
  }
  
  .simple-nav-link {
    display: block;
    padding: 0.75rem 1rem;
    text-align: center;
    border-radius: 8px;
  }
  
  .simple-header-actions {
    gap: 0.5rem;
  }
  
  .simple-user-greeting span,
  .simple-login-btn span,
  .simple-logout-btn span {
    display: none;
  }
}

@media (max-width: 480px) {
  .simple-header {
    height: 100px;
  }
  
  .simple-header-container {
    padding: 0 0.75rem;
  }
  
  .simple-header-logo {
    font-size: 1.25rem;
  }
  
  .simple-header-logo img {
    width: 32px;
    height: 32px;
  }
} 