/* reset.css - Reset các style mặc định của trình du<PERSON> */

/* Box sizing rules */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Remove default margin and padding */
html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

/* Set core body defaults */
body {
  min-height: 100vh;
  text-rendering: optimizeSpeed;
  line-height: 1.5;
  overflow-x: hidden;
  position: relative;
}

/* Remove list styles */
ol,
ul {
  list-style: none;
}

/* Reset tables */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* Reset links */
a {
  text-decoration: none;
  color: inherit;
}

/* Reset button styles */
button {
  border: none;
  margin: 0;
  padding: 0;
  width: auto;
  overflow: visible;
  background: transparent;
  color: inherit;
  font: inherit;
  line-height: normal;
  -webkit-font-smoothing: inherit;
  -moz-osx-font-smoothing: inherit;
  -webkit-appearance: none;
  cursor: pointer;
}

/* Remove button focus styles for non-keyboard interactions */
button::-moz-focus-inner {
  border: 0;
  padding: 0;
}

/* Reset form elements */
input,
button,
textarea,
select {
  font: inherit;
}

/* Make images easier to work with */
img,
picture {
  max-width: 100%;
  display: block;
}

/* Inherit fonts for inputs and buttons */
input,
button,
textarea,
select {
  font: inherit;
}

/* Remove all animations and transitions for people that prefer not to see them */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Fix for iOS input zoom */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  select,
  textarea,
  input {
    font-size: 16px;
  }
}

/* Fix for Firefox focus styles */
:focus {
  outline: 0;
}

/* Fix for iOS tap highlight */
* {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

/* Fix for iOS momentum scrolling */
html {
  -webkit-overflow-scrolling: touch;
}

/* Fix for text size adjust */
html {
  -webkit-text-size-adjust: 100%;
}

/* Fix for margin collapse */
.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

/* Fix for iOS input shadow */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="search"],
input[type="tel"],
input[type="url"],
textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* Fix for iOS button styling */
button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}

/* Fix for Firefox button styling */
button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
} 