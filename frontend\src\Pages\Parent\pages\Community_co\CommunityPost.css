/* ===== PARENT COMMUNITY POST STYLES ===== */
.parent-community-post-container {
  max-width: 100%;
  margin: 0 auto;
  padding: 30px 15px;
  font-family: 'Be Vietnam Pro', sans-serif;
  /* background: linear-gradient(135deg, #015C92 0%, #2D82B5 30%, #428CD4 60%, #88CDF6 100%); */
  min-height: 100vh;
}

/* Navigation */
.parent-post-navigation {
  margin-bottom: 24px;
}

.parent-back-link {
  color: #ffffff;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  font-weight: 500;
  font-family: 'Be Vietnam Pro', sans-serif;
  padding: 12px 20px;
  border-radius: 25px;
  background: linear-gradient(135deg, #88CDF6 0%, #BCE6FF 100%);
  color: #015C92;
  border: 1px solid rgba(136, 205, 246, 0.3);
  box-shadow: 0 4px 15px rgba(136, 205, 246, 0.4);
}

.parent-back-link i {
  margin-right: 8px;
  font-size: 1rem;
}

.parent-back-link:hover {
  background: linear-gradient(135deg, #BCE6FF 0%, #88CDF6 100%);
  transform: translateX(-4px) translateY(-2px);
  box-shadow: 0 6px 20px rgba(136, 205, 246, 0.6);
  color: #015C92;
}

/* Post Content Container */
.parent-post-content-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  margin-bottom: 30px;
  padding: 35px;
  transition: all 0.3s ease;
}

.parent-post-content-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
}

/* Post Header */
.parent-post-header {
  margin-bottom: 30px;
  border-bottom: 2px solid rgba(1, 92, 146, 0.1);
  padding-bottom: 25px;
}

.parent-post-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 25px;
}

.parent-post-category {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 18px;
  border-radius: 25px;
  background: linear-gradient(135deg, #015C92 0%, #2D82B5 100%);
  color: #ffffff;
  font-size: 0.9rem;
  font-weight: 600;
  font-family: 'Be Vietnam Pro', sans-serif;
  box-shadow: 0 4px 15px rgba(1, 92, 146, 0.3);
  transition: all 0.3s ease;
}

.parent-post-category:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(1, 92, 146, 0.4);
}

.parent-post-time {
  font-size: 0.9rem;
  color: #6b7280;
  font-family: 'Be Vietnam Pro', sans-serif;
  background: rgba(107, 114, 128, 0.1);
  padding: 6px 12px;
  border-radius: 15px;
}

.parent-post-title {
  margin: 0 0 28px 0;
  font-size: 2.2rem;
  line-height: 1.3;
  color: #1f2937;
  font-family: 'Be Vietnam Pro', sans-serif;
  font-weight: 700;
  background: linear-gradient(135deg, #015C92 0%, #428CD4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.parent-post-author {
  display: flex;
  align-items: center;
  gap: 15px;
}

.parent-author-info {
  flex: 1;
}

.parent-author-name {
  font-weight: 600;
  font-size: 1.1rem;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 10px;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.parent-author-badge {
  font-size: 0.8rem;
  padding: 4px 12px;
  border-radius: 15px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-family: 'Be Vietnam Pro', sans-serif;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.parent-author-badge.nurse {
  background: linear-gradient(135deg, #00b09b, #96c93d);
  color: #ffffff;
}

.parent-author-badge.parent {
  background: linear-gradient(135deg, #015C92, #428CD4);
  color: #ffffff;
}

/* Post Content */
.parent-post-content {
  margin-bottom: 35px;
  font-size: 1.1rem;
  line-height: 1.8;
  color: #374151;
  font-family: 'Be Vietnam Pro', sans-serif;
  background: rgba(255, 255, 255, 0.7);
  padding: 25px;
  border-radius: 15px;
  border: 1px solid rgba(1, 92, 146, 0.1);
}

.parent-post-content h3 {
  color: #1f2937;
  margin-top: 30px;
  margin-bottom: 15px;
  font-size: 1.5rem;
  font-family: 'Be Vietnam Pro', sans-serif;
  font-weight: 600;
  background: linear-gradient(135deg, #015C92 0%, #428CD4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.parent-post-content p {
  margin-bottom: 18px;
}

.parent-post-content ul, .parent-post-content ol {
  margin-bottom: 18px;
  padding-left: 28px;
}

.parent-post-content li {
  margin-bottom: 10px;
}

.parent-post-content img {
  max-width: 100%;
  height: auto;
  border-radius: 12px;
  margin: 25px 0;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.parent-post-content a {
  color: #015C92;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.parent-post-content a:hover {
  color: #428CD4;
  text-decoration: underline;
}

/* Post Tags */
.parent-post-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 28px;
}

.parent-post-tag {
  background: linear-gradient(135deg, rgba(1, 92, 146, 0.1), rgba(66, 140, 212, 0.1));
  color: #015C92;
  font-size: 0.85rem;
  padding: 8px 16px;
  border-radius: 20px;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
  font-family: 'Be Vietnam Pro', sans-serif;
  border: 1px solid rgba(1, 92, 146, 0.2);
  font-weight: 500;
}

.parent-post-tag:hover {
  background: linear-gradient(135deg, rgba(1, 92, 146, 0.2), rgba(66, 140, 212, 0.2));
  transform: translateY(-3px);
  box-shadow: 0 4px 15px rgba(1, 92, 146, 0.2);
}

/* Post Actions */
.parent-post-actions {
  display: flex;
  border-top: 2px solid rgba(1, 92, 146, 0.1);
  padding-top: 25px;
  gap: 15px;
  justify-content: center;
}

.parent-post-actions button {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(1, 92, 146, 0.2);
  font-size: 0.95rem;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 12px 20px;
  border-radius: 25px;
  transition: all 0.3s ease;
  font-family: 'Be Vietnam Pro', sans-serif;
  font-weight: 500;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.parent-post-actions button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.parent-post-actions button i {
  margin-right: 8px;
  font-size: 1.1rem;
}

.parent-like-button:hover {
  background: linear-gradient(135deg, rgba(244, 67, 54, 0.1), rgba(244, 67, 54, 0.2));
  color: #f44336;
  border-color: #f44336;
}

.parent-share-button:hover {
  background: linear-gradient(135deg, rgba(1, 92, 146, 0.1), rgba(66, 140, 212, 0.2));
  color: #015C92;
  border-color: #015C92;
}

.parent-report-button:hover {
  background: linear-gradient(135deg, rgba(255, 152, 0, 0.1), rgba(255, 152, 0, 0.2));
  color: #ff9800;
  border-color: #ff9800;
}

.parent-like-button.liked {
  background: linear-gradient(135deg, #f44336, #e91e63);
  color: #ffffff;
  border-color: #f44336;
  box-shadow: 0 6px 20px rgba(244, 67, 54, 0.3);
}

.parent-like-button.liked i {
  color: #ffffff;
  animation: parentHeartBeat 0.6s ease-in-out;
}

@keyframes parentHeartBeat {
  0% { transform: scale(1); }
  25% { transform: scale(1.2); }
  50% { transform: scale(1.4); }
  75% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* Comments Section */
.parent-comments-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 30px 35px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.parent-comments-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
}

.parent-comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 28px;
  padding-bottom: 20px;
  border-bottom: 2px solid rgba(1, 92, 146, 0.1);
}

.parent-comments-header h3 {
  font-size: 1.5rem;
  margin: 0;
  color: #1f2937;
  font-family: 'Be Vietnam Pro', sans-serif;
  font-weight: 700;
  background: linear-gradient(135deg, #015C92 0%, #428CD4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.parent-comments-filter {
  display: flex;
  align-items: center;
  gap: 12px;
}

.parent-comments-filter label {
  color: #6b7280;
  font-size: 0.95rem;
  font-family: 'Be Vietnam Pro', sans-serif;
  font-weight: 500;
}

.parent-comments-filter select {
  padding: 10px 16px;
  border: 1px solid rgba(1, 92, 146, 0.2);
  border-radius: 15px;
  font-size: 0.95rem;
  font-family: 'Be Vietnam Pro', sans-serif;
  color: #374151;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.parent-comments-filter select:focus {
  outline: none;
  border-color: #015C92;
  box-shadow: 0 0 0 3px rgba(1, 92, 146, 0.15);
  background: rgba(255, 255, 255, 1);
}

/* Comment Form */
.parent-comment-form {
  margin-bottom: 35px;
  border: 1px solid rgba(1, 92, 146, 0.2);
  border-radius: 20px;
  padding: 25px;
  background: linear-gradient(135deg, rgba(1, 92, 146, 0.05), rgba(66, 140, 212, 0.05));
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.parent-comment-form:focus-within {
  border-color: #015C92;
  box-shadow: 0 0 0 3px rgba(1, 92, 146, 0.15);
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
}

.parent-comment-form textarea {
  width: 100%;
  height: 120px;
  padding: 18px;
  border: 1px solid rgba(1, 92, 146, 0.2);
  border-radius: 15px;
  resize: vertical;
  font-size: 1rem;
  font-family: 'Be Vietnam Pro', sans-serif;
  margin-bottom: 20px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.parent-comment-form textarea:focus {
  outline: none;
  border-color: #015C92;
  box-shadow: 0 0 0 3px rgba(1, 92, 146, 0.1);
  background: rgba(255, 255, 255, 1);
}

.parent-comment-submit-btn {
  float: right;
  padding: 12px 24px;
  background: linear-gradient(135deg, #015C92 0%, #2D82B5 100%);
  border: none;
  border-radius: 25px;
  color: white;
  font-size: 0.95rem;
  font-weight: 600;
  font-family: 'Be Vietnam Pro', sans-serif;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(1, 92, 146, 0.3);
}

.parent-comment-submit-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2D82B5 0%, #428CD4 100%);
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(1, 92, 146, 0.4);
}

.parent-comment-submit-btn:disabled {
  background: linear-gradient(135deg, #a0aec0, #cbd5e0);
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.parent-loading-comments {
  text-align: center;
  padding: 40px;
  color: #6b7280;
  font-size: 1.1rem;
  font-family: 'Be Vietnam Pro', sans-serif;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 15px;
  margin: 20px 0;
}

.parent-loading-comments i {
  margin-right: 12px;
  animation: parentSpin 1s linear infinite;
  color: #015C92;
  font-size: 1.2rem;
}

@keyframes parentSpin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* No Comments State */
.parent-no-comments {
  text-align: center;
  padding: 50px 20px;
  color: #6b7280;
  background: linear-gradient(135deg, rgba(1, 92, 146, 0.05), rgba(66, 140, 212, 0.05));
  border-radius: 20px;
  margin: 20px 0;
}

.parent-no-comments i {
  font-size: 3.5rem;
  margin-bottom: 20px;
  opacity: 0.6;
  color: #015C92;
}

.parent-no-comments p {
  font-size: 1.1rem;
  margin: 0;
  font-family: 'Be Vietnam Pro', sans-serif;
  color: #374151;
  font-weight: 500;
}

/* Comments List */
.parent-comments-list {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.parent-comment-item {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(1, 92, 146, 0.15);
  border-radius: 18px;
  padding: 25px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.parent-comment-item:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(1, 92, 146, 0.3);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.parent-comment-header {
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.parent-comment-author {
  display: flex;
  align-items: center;
  gap: 15px;
}

.parent-comment-author-info {
  flex: 1;
}

.parent-comment-author-name {
  font-weight: 600;
  color: #1f2937;
  font-family: 'Be Vietnam Pro', sans-serif;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.05rem;
}

.parent-comment-time {
  font-size: 0.85rem;
  color: #6b7280;
  margin-top: 4px;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.parent-comment-content {
  font-size: 1rem;
  line-height: 1.7;
  color: #374151;
  margin-bottom: 15px;
  padding: 8px 0;
  font-family: 'Be Vietnam Pro', sans-serif;
}

/* Author Icons */
.parent-author-icon,
.parent-comment-author-icon {
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.parent-author-icon:hover,
.parent-comment-author-icon:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.parent-icon {
  background: linear-gradient(135deg, #015C92 0%, #428CD4 100%);
}

.nurse-icon {
  background: linear-gradient(135deg, #00b09b, #96c93d);
}

.default-icon {
  background: linear-gradient(135deg, #2D82B5 0%, #88CDF6 100%);
}

.comment-actions {
  display: flex;
  gap: 16px;
}

.comment-like-btn, 
.comment-reply-btn {
  background: none;
  border: none;
  color: var(--text-medium);
  font-size: 0.85rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s;
  font-family: 'Be Vietnam Pro', sans-serif;
  padding: 4px 8px;
  border-radius: 4px;
}

.comment-like-btn:hover {
  color: #f44336;
  background-color: rgba(244, 67, 54, 0.08);
}

.comment-reply-btn:hover {
  color: #2196f3;
  background-color: rgba(33, 150, 243, 0.08);
}

.empty-comments {
  text-align: center;
  padding: 40px 0;
  color: var(--text-light);
}

.empty-comments i {
  font-size: 3rem;
  margin-bottom: 16px;
  color: #bdbdbd;
}

.empty-comments p {
  font-size: 1.1rem;
  margin: 0;
  font-family: 'Be Vietnam Pro', sans-serif;
  color: var(--text-medium);
}

/* Comment Pagination */
.comment-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 24px;
  padding: 16px 0;
}

.pagination-btn {
  background: none;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius);
  padding: 8px 16px;
  color: var(--text-dark);
  font-size: 0.95rem;
  font-family: 'Be Vietnam Pro', sans-serif;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #f5f5f5;
  border-color: var(--text-medium);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  margin: 0 16px;
  font-size: 0.95rem;
  color: var(--text-medium);
  font-family: 'Be Vietnam Pro', sans-serif;
}

/* Related Posts */
.related-posts-section {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  padding: 24px 30px;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-light);
}

.related-posts-section h3 {
  margin: 0 0 20px 0;
  font-size: 1.4rem;
  color: var(--text-dark);
  font-family: 'Be Vietnam Pro', sans-serif;
  font-weight: 600;
}

.related-posts {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.related-post-card {
  background-color: #f9f9f9;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius);
  padding: 16px;
  transition: all 0.3s;
}

.related-post-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  background-color: white;
}

.related-post-category {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-size: 0.85rem;
  color: var(--text-medium);
  margin-bottom: 12px;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.related-post-card h4 {
  margin: 0 0 12px 0;
  font-size: 1.05rem;
  line-height: 1.4;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.related-post-card h4 a {
  color: var(--text-dark);
  text-decoration: none;
  transition: color 0.2s;
}

.related-post-card h4 a:hover {
  color: var(--primary-color);
}

.related-post-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.85rem;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.related-post-author {
  display: flex;
  align-items: center;
}

.related-author-icon {
  width: 30px;
  height: 30px;
  font-size: 14px;
  margin-right: 8px;
}

/* Error Container */
.error-container {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  padding: 40px;
  text-align: center;
  box-shadow: var(--shadow);
  margin: 40px auto;
  max-width: 600px;
  border: 1px solid var(--border-light);
}

.error-container h2 {
  margin: 0 0 24px 0;
  font-size: 1.8rem;
  color: #d32f2f;
  font-family: 'Be Vietnam Pro', sans-serif;
  font-weight: 600;
}

.error-container .back-link {
  display: inline-flex;
  margin-top: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .parent-community-post-container {
    padding: 20px 12px;
  }

  .parent-post-content-container,
  .parent-comments-section {
    padding: 25px 20px;
  }

  .parent-post-title {
    font-size: 1.8rem;
  }

  .parent-post-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .parent-comments-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .parent-post-actions {
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 12px;
  }

  .parent-post-actions button {
    padding: 10px 16px;
    font-size: 0.9rem;
  }

  .parent-post-actions button span {
    font-size: 0.85rem;
  }

  .parent-author-icon,
  .parent-comment-author-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .parent-post-content-container,
  .parent-comments-section {
    padding: 20px 16px;
  }

  .parent-post-title {
    font-size: 1.5rem;
  }

  .parent-comment-form textarea {
    height: 100px;
  }

  .parent-comment-submit-btn {
    padding: 10px 18px;
    font-size: 0.9rem;
    width: 100%;
    justify-content: center;
  }

  .parent-comments-filter {
    width: 100%;
    justify-content: space-between;
  }

  .parent-post-actions {
    flex-direction: row;
    justify-content: space-around;
    width: 100%;
  }

  .parent-post-actions button {
    flex: 1;
    justify-content: center;
    padding: 12px 8px;
  }

  .parent-post-actions button span {
    display: none;
  }

  .parent-author-icon,
  .parent-comment-author-icon {
    width: 35px;
    height: 35px;
    font-size: 16px;
  }

  .parent-comment-item {
    padding: 20px 16px;
  }
}

/* Error Container */
.parent-error-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 50px 40px;
  text-align: center;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  margin: 40px auto;
  max-width: 600px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.parent-error-container h2 {
  margin: 0 0 30px 0;
  font-size: 2rem;
  color: #ef4444;
  font-family: 'Be Vietnam Pro', sans-serif;
  font-weight: 700;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.parent-error-container .parent-back-link {
  display: inline-flex;
  margin-top: 20px;
}

/* Additional Styles for Enhanced UI */
.parent-edited-indicator {
  font-style: italic;
  color: #9ca3af;
  font-size: 12px;
}

/* Smooth transitions for all interactive elements */
.parent-community-post-container * {
  transition: all 0.3s ease;
}

/* Custom scrollbar for better aesthetics */
.parent-comment-form textarea::-webkit-scrollbar,
.parent-comments-section::-webkit-scrollbar {
  width: 8px;
}

.parent-comment-form textarea::-webkit-scrollbar-track,
.parent-comments-section::-webkit-scrollbar-track {
  background: rgba(1, 92, 146, 0.1);
  border-radius: 10px;
}

.parent-comment-form textarea::-webkit-scrollbar-thumb,
.parent-comments-section::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #015C92, #428CD4);
  border-radius: 10px;
}

.parent-comment-form textarea::-webkit-scrollbar-thumb:hover,
.parent-comments-section::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #2D82B5, #88CDF6);
}

/* Focus states for accessibility */
.parent-post-actions button:focus,
.parent-comment-submit-btn:focus,
.parent-comments-filter select:focus {
  outline: 2px solid #015C92;
  outline-offset: 2px;
}

/* Loading animation enhancement */
@keyframes parentPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.parent-loading-comments {
  animation: parentPulse 2s infinite;
}

/* Adjusted styles for comments */
.comment-author {
  display: flex;
  align-items: center;
}

.comment-author-info {
  flex: 1;
}

/* Adjusted styles for related posts */
.related-post-author {
  display: flex;
  align-items: center;
}

/* Comment Actions Menu */
.comment-actions-menu {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.comment-item:hover .comment-actions-menu {
  opacity: 1;
}

.edit-comment-btn,
.delete-comment-btn {
  background: none;
  border: none;
  padding: 6px;
  border-radius: 4px;
  color: #6b7280;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.edit-comment-btn:hover {
  background: #f3f4f6;
  color: #3b82f6;
}

.delete-comment-btn:hover {
  background: #fef2f2;
  color: #ef4444;
}

/* Edit Comment Form */
.edit-comment-form {
  margin-top: 12px;
}

.edit-comment-textarea {
  width: 100%;
  min-height: 80px;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-family: inherit;
  font-size: 14px;
  resize: vertical;
  margin-bottom: 12px;
}

.edit-comment-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.edit-comment-actions {
  display: flex;
  gap: 8px;
}

.save-edit-btn,
.cancel-edit-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.save-edit-btn {
  background: #3b82f6;
  color: white;
}

.save-edit-btn:hover {
  background: #2563eb;
}

.cancel-edit-btn {
  background: #f3f4f6;
  color: #6b7280;
}

.cancel-edit-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

/* Updated Comment Actions */
.comment-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f3f4f6;
}

.comment-like-btn,
.reply-btn,
.show-replies-btn {
  background: none;
  border: none;
  color: #6b7280;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.comment-like-btn:hover,
.reply-btn:hover,
.show-replies-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.comment-like-btn.liked {
  color: #ef4444;
}

.comment-like-btn.liked:hover {
  color: #dc2626;
}

/* Reply Form */
.reply-form {
  margin-top: 16px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.reply-textarea {
  width: 100%;
  min-height: 80px;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-family: inherit;
  font-size: 14px;
  resize: vertical;
  margin-bottom: 12px;
  background: white;
}

.reply-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.reply-actions {
  display: flex;
  gap: 8px;
}

.submit-reply-btn,
.cancel-reply-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submit-reply-btn {
  background: #3b82f6;
  color: white;
}

.submit-reply-btn:hover:not(:disabled) {
  background: #2563eb;
}

.submit-reply-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.cancel-reply-btn {
  background: #f3f4f6;
  color: #6b7280;
}

.cancel-reply-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

/* Replies List */
.replies-list {
  margin-top: 16px;
  margin-left: 24px;
  border-left: 2px solid #e5e7eb;
  padding-left: 16px;
}

.reply-item {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.reply-author {
  display: flex;
  align-items: center;
  gap: 12px;
}

.reply-author-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  flex-shrink: 0;
}

.reply-author-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.reply-author-name {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.reply-time {
  font-size: 12px;
  color: #6b7280;
}

.reply-content {
  margin: 12px 0;
  color: #374151;
  line-height: 1.6;
}

.reply-content p {
  margin: 0;
}

.reply-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e5e7eb;
}

.reply-like-btn {
  background: none;
  border: none;
  color: #6b7280;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.reply-like-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.reply-like-btn.liked {
  color: #ef4444;
}

.reply-like-btn.liked:hover {
  color: #dc2626;
}

/* No Comments State */
.no-comments {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.no-comments i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-comments p {
  font-size: 16px;
  margin: 0;
}

/* Loading Comments */
.loading-comments {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
}

/* Edited Indicator */
.edited-indicator {
  font-style: italic;
  color: #9ca3af;
  font-size: 12px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .comment-actions {
    flex-wrap: wrap;
    gap: 12px;
  }
  
  .replies-list {
    margin-left: 12px;
    padding-left: 12px;
  }
  
  .reply-item {
    padding: 12px;
  }
  
  .comment-actions-menu {
    opacity: 1; /* Always visible on mobile */
  }
}