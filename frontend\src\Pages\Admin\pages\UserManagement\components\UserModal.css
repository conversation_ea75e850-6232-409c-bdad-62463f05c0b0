/* ===== MODERN USER MODAL DESIGN ===== */

/* CSS Reset and Base Styles */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Modern Design Variables */
:root {
  /* Modern Color Palette */
  --modal-white: #ffffff;
  --modal-gray-50: #f8fafc;
  --modal-gray-100: #f1f5f9;
  --modal-gray-200: #e2e8f0;
  --modal-gray-300: #cbd5e1;
  --modal-gray-400: #94a3b8;
  --modal-gray-500: #64748b;
  --modal-gray-600: #475569;
  --modal-gray-700: #334155;
  --modal-gray-800: #1e293b;
  --modal-gray-900: #0f172a;

  /* Blue Theme Colors */
  --modal-blue-50: #eff6ff;
  --modal-blue-100: #dbeafe;
  --modal-blue-500: #3b82f6;
  --modal-blue-600: #2563eb;
  --modal-blue-700: #1d4ed8;

  /* Status Colors */
  --modal-green: #10b981;
  --modal-green-light: #d1fae5;
  --modal-red: #ef4444;
  --modal-red-light: #fee2e2;
  --modal-yellow: #f59e0b;
  --modal-yellow-light: #fef3c7;

  /* Typography */
  --modal-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --modal-font-size-xs: 0.75rem;
  --modal-font-size-sm: 0.875rem;
  --modal-font-size-base: 1rem;
  --modal-font-size-lg: 1.125rem;
  --modal-font-size-xl: 1.25rem;
  --modal-font-size-2xl: 1.5rem;
  --modal-font-size-3xl: 1.875rem;

  /* Spacing */
  --modal-space-1: 0.25rem;
  --modal-space-2: 0.5rem;
  --modal-space-3: 0.75rem;
  --modal-space-4: 1rem;
  --modal-space-5: 1.25rem;
  --modal-space-6: 1.5rem;
  --modal-space-8: 2rem;
  --modal-space-10: 2.5rem;

  /* Borders & Radius */
  --modal-border-width: 1px;
  --modal-border-color: var(--modal-gray-200);
  --modal-radius-sm: 0.375rem;
  --modal-radius: 0.5rem;
  --modal-radius-lg: 0.75rem;
  --modal-radius-xl: 1rem;
  --modal-radius-2xl: 1.5rem;

  /* Shadows */
  --modal-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --modal-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --modal-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --modal-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --modal-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --modal-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* ===== MODERN MODAL OVERLAY ===== */
.admin-user-modal-overlay,
.user-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--modal-space-6);
  animation: modalOverlayFadeIn 0.3s ease-out;
}

@keyframes modalOverlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* ===== MODERN MODAL CONTAINER ===== */
.admin-user-modal,
.user-modal {
  background: var(--modal-white);
  border-radius: var(--modal-radius-2xl);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: var(--modal-shadow-2xl);
  border: none;
  animation: modalSlideIn 0.3s ease-out;
  font-family: var(--modal-font-family);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Large Modal for Complex Forms */
.admin-user-modal-large,
.user-modal-large {
  max-width: 900px;
  max-height: 95vh;
}

/* ===== MODERN MODAL HEADER ===== */
.admin-user-modal-header,
.user-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--modal-space-8);
  background: linear-gradient(135deg, var(--modal-blue-50), var(--modal-white));
  border-bottom: 1px solid var(--modal-gray-200);
  position: relative;
}

.admin-user-modal-header::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--modal-blue-500), var(--modal-blue-600), var(--modal-blue-700));
}

.admin-user-modal-header h2 {
  margin: 0;
  font-size: var(--modal-font-size-2xl);
  font-weight: 700;
  color: var(--modal-gray-900);
  letter-spacing: -0.025em;
  display: flex;
  align-items: center;
  gap: var(--modal-space-3);
}

.admin-user-modal-header h2::before {
  content: '👤';
  font-size: var(--modal-font-size-xl);
}

.close-button {
  background: var(--modal-gray-100);
  border: none;
  color: var(--modal-gray-500);
  cursor: pointer;
  font-size: 1.25rem;
  padding: var(--modal-space-3);
  border-radius: var(--modal-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
}

.close-button:hover {
  background: var(--modal-red);
  color: var(--modal-white);
  transform: scale(1.1);
}

/* ===== MODERN FORM STYLES ===== */
.admin-user-modal form {
  padding: 0;
  max-height: calc(90vh - 200px);
  overflow-y: auto;
}

/* ===== ADMIN FORM HEADER SECTION ===== */
.admin-form-header-section {
  background: linear-gradient(135deg, var(--modal-blue-50), var(--modal-white));
  border: 1px solid var(--modal-blue-200);
  border-radius: var(--modal-radius-xl);
  padding: var(--modal-space-6);
  margin: var(--modal-space-6);
  margin-bottom: var(--modal-space-4);
  box-shadow: var(--modal-shadow-sm);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  animation: adminHeaderSlideIn 0.4s ease-out;
}

@keyframes adminHeaderSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.admin-form-header-section:hover {
  box-shadow: var(--modal-shadow-md);
  border-color: var(--modal-blue-300);
}

.admin-form-header-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--modal-blue-500), var(--modal-blue-600), var(--modal-blue-700));
}

.admin-form-header-section h3 {
  margin: 0 0 var(--modal-space-5) 0;
  font-size: var(--modal-font-size-lg);
  font-weight: 700;
  color: var(--modal-gray-900);
  display: flex;
  align-items: center;
  gap: var(--modal-space-3);
  padding-bottom: var(--modal-space-3);
  border-bottom: 2px solid var(--modal-blue-200);
}

.admin-form-header-section h3 svg {
  color: var(--modal-blue-600);
  font-size: 1.25rem;
}

.admin-form-header-section .admin-form-group {
  margin-bottom: var(--modal-space-4);
}

.admin-form-header-section .admin-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--modal-space-4);
  margin-bottom: var(--modal-space-4);
}

.admin-form-header-section label {
  display: block;
  margin-bottom: var(--modal-space-2);
  font-size: var(--modal-font-size-sm);
  font-weight: 700;
  color: var(--modal-blue-700);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-form-header-section input,
.admin-form-header-section select {
  width: 100%;
  padding: var(--modal-space-4);
  border: 2px solid var(--modal-blue-200);
  border-radius: var(--modal-radius-lg);
  font-size: var(--modal-font-size-base);
  font-family: var(--modal-font-family);
  transition: all 0.3s ease;
  background: var(--modal-white);
  height: 3.5rem;
  display: flex;
  align-items: center;
}

.admin-form-header-section input:focus,
.admin-form-header-section select:focus {
  outline: none;
  border-color: var(--modal-blue-500);
  background: var(--modal-white);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15), var(--modal-shadow-md);
  transform: translateY(-1px);
}

.admin-form-header-section input.disabled-field {
  background: var(--modal-blue-50) !important;
  color: var(--modal-blue-600) !important;
  cursor: not-allowed !important;
  border-color: var(--modal-blue-200) !important;
  font-weight: 600;
}

.admin-form-header-section .admin-field-note {
  font-size: var(--modal-font-size-xs);
  color: var(--modal-blue-600);
  margin-top: var(--modal-space-1);
  font-style: italic;
  font-weight: 500;
}

.admin-form-header-section .admin-required {
  color: var(--modal-red);
  font-weight: 700;
}



.admin-form-header-section select {
  cursor: pointer !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%232563eb' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right var(--modal-space-3) center !important;
  background-size: 1rem !important;
  padding-right: 3rem !important;
}

/* Remove IE/Edge default dropdown arrow */
.admin-form-header-section select::-ms-expand {
  display: none !important;
}

.admin-form-header-section select:focus {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233b82f6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

.admin-form-header-section input.error,
.admin-form-header-section select.error {
  border-color: var(--modal-red);
  background: var(--modal-red-light);
  animation: adminErrorShake 0.4s ease-in-out;
}

@keyframes adminErrorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}

.admin-form-header-section input.error:focus,
.admin-form-header-section select.error:focus {
  border-color: var(--modal-red);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.15), 0 4px 12px rgba(239, 68, 68, 0.1);
}

.admin-form-header-section .admin-error-message {
  color: var(--modal-red);
  font-size: var(--modal-font-size-xs);
  margin-top: var(--modal-space-1);
  display: block;
  font-weight: 600;
  animation: adminErrorFadeIn 0.3s ease-out;
}

@keyframes adminErrorFadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced hover effects for admin header section */
.admin-form-header-section input:hover:not(:disabled),
.admin-form-header-section select:hover:not(:disabled) {
  border-color: var(--modal-blue-400);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.admin-form-header-section input:disabled:hover {
  cursor: not-allowed;
}

/* Enhanced focus states */
.admin-form-header-section input:focus,
.admin-form-header-section select:focus {
  border-color: var(--modal-blue-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15), 0 4px 12px rgba(59, 130, 246, 0.1);
}

/* Improved label styling */
.admin-form-header-section label {
  position: relative;
  padding-left: var(--modal-space-1);
}

.admin-form-header-section label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 60%;
  background: var(--modal-blue-500);
  border-radius: 2px;
}

/* ===== ADMIN SPECIAL FORM SECTIONS ===== */
/* Username field styling */
.admin-form-group input[name="username"] {
  background: var(--modal-blue-50);
  border-color: var(--modal-blue-300);
  color: var(--modal-blue-800);
  font-weight: 600;
}

.admin-form-group input[name="username"]:disabled {
  background: var(--modal-blue-100);
  color: var(--modal-blue-600);
  cursor: not-allowed;
}

/* Password field styling */
.admin-form-group input[type="password"] {
  border-color: var(--modal-blue-200);
  background: var(--modal-white);
}

.admin-form-group input[type="password"]:focus {
  border-color: var(--modal-blue-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15);
}

.admin-form-group input[type="password"]::placeholder {
  color: var(--modal-blue-400);
  font-style: italic;
}

/* Enhanced styling for special admin form groups */
.admin-form-group:has(input[name="username"]),
.admin-form-group:has(input[type="password"]),
.admin-form-group:has(.admin-status-toggle) {
  padding: var(--modal-space-4);
  background: linear-gradient(135deg, var(--modal-blue-50), var(--modal-white));
  border: 1px solid var(--modal-blue-200);
  border-radius: var(--modal-radius-lg);
  margin-bottom: var(--modal-space-4);
  position: relative;
}

.admin-form-group:has(input[name="username"])::before,
.admin-form-group:has(input[type="password"])::before,
.admin-form-group:has(.admin-status-toggle)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--modal-blue-400), var(--modal-blue-500));
  border-radius: var(--modal-radius-lg) var(--modal-radius-lg) 0 0;
}

/* Responsive adjustments for admin header section */
@media (max-width: 768px) {
  .admin-form-header-section .admin-form-row {
    grid-template-columns: 1fr;
    gap: var(--modal-space-3);
  }

  .admin-form-header-section {
    padding: var(--modal-space-4);
    margin: var(--modal-space-4);
  }

  .admin-form-header-section h3 {
    font-size: var(--modal-font-size-base);
  }

  .admin-form-group:has(input[name="username"]),
  .admin-form-group:has(input[type="password"]),
  .admin-form-group:has(.admin-status-toggle) {
    padding: var(--modal-space-3);
  }
}

/* Modern Form Sections */
.form-section {
  margin-bottom: var(--modal-space-6);
  padding: var(--modal-space-6);
  background: var(--modal-white);
  border: 1px solid var(--modal-gray-200);
  border-radius: var(--modal-radius-xl);
  box-shadow: var(--modal-shadow-sm);
  position: relative;
  overflow: hidden;
}

.form-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--modal-blue-500), var(--modal-blue-600));
}

.form-section h3 {
  margin: 0 0 var(--modal-space-6) 0;
  font-size: var(--modal-font-size-lg);
  font-weight: 700;
  color: var(--modal-gray-900);
  display: flex;
  align-items: center;
  gap: var(--modal-space-3);
  padding-bottom: var(--modal-space-4);
  border-bottom: 1px solid var(--modal-gray-200);
}

.form-section h3 svg {
  color: var(--modal-blue-500);
  font-size: 1.25rem;
}

/* Section Header with Actions */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
}

.section-header h3 {
  margin: 0;
}

.form-group {
  margin-bottom: var(--modal-space-5);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--modal-space-4);
  margin-bottom: var(--modal-space-5);
}

/* ===== MODERN LABEL STYLES ===== */
label {
  display: block;
  margin-bottom: var(--modal-space-2);
  font-size: var(--modal-font-size-sm);
  font-weight: 600;
  color: var(--modal-gray-700);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* ===== MODERN INPUT STYLES ===== */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="date"],
select,
textarea {
  width: 100%;
  padding: var(--modal-space-4);
  border: 2px solid var(--modal-gray-200);
  border-radius: var(--modal-radius-lg);
  font-size: var(--modal-font-size-base);
  font-family: var(--modal-font-family);
  transition: all 0.3s ease;
  background: var(--modal-gray-50);
  height: 3.5rem;
  display: flex;
  align-items: center;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="date"]:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: var(--modal-blue-500);
  background: var(--modal-white);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), var(--modal-shadow-md);
  transform: translateY(-1px);
}

textarea {
  height: auto;
  min-height: 100px;
  resize: vertical;
  padding: var(--modal-space-4);
  align-items: flex-start;
  line-height: 1.6;
}

input.error,
textarea.error {
  border-color: var(--modal-red);
  background: var(--modal-red-light);
}

.disabled-field {
  background: var(--modal-gray-100) !important;
  color: var(--modal-gray-500) !important;
  cursor: not-allowed !important;
  border-color: var(--color-gray-200) !important;
}

/* Field Notes */
.field-note {
  display: block;
  margin-top: var(--space-1);
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
  font-style: italic;
  line-height: 1.3;
}

.required {
  color: var(--color-red);
  font-size: var(--font-size-xs);
  font-weight: 500;
  font-style: normal;
}

.optional {
  color: var(--color-gray-500);
  font-size: var(--font-size-xs);
  font-weight: 400;
  font-style: italic;
}

.form-group label .required {
  margin-left: var(--space-1);
}

.form-group label .optional {
  margin-left: var(--space-1);
}

/* Error Messages */
.error-message {
  color: var(--color-red);
  font-size: var(--font-size-xs);
  margin-top: var(--space-1);
  display: block;
}

/* Status Toggle */
.status-toggle {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

input[type="checkbox"] {
  width: 1.25rem;
  height: 1.25rem;
  margin: 0;
  cursor: pointer;
}

.toggle-label {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--color-gray-700);
  cursor: pointer;
}

/* Additional Info Section */
.additional-info {

    margin-top: var(--space-4);
  padding-top: var(--space-4);
  border-top: var(--border-width) solid var(--border-color);
  width: 100%;
  box-sizing: border-box;
}

.info-section {
   
 width: 100%;
  background: var(--color-gray-50);
  padding: var(--space-4);
  border-radius: var(--radius);
  border: var(--border-width) solid var(--border-color);
  box-sizing: border-box;
}

.info-section h4 {
  margin: 0 0 var(--space-3) 0;
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--color-gray-900);
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-3);
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.info-item .info-label {
  font-size: var(--font-size-xs);
  font-weight: 500;
  color: var(--color-gray-600);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-item .info-value {
  font-size: var(--font-size-sm);
  color: var(--color-gray-900);
  font-weight: 500;
}

.info-item .info-value.active {
  color: var(--color-green);
}

.info-item .info-value.inactive {
  color: var(--color-red);
}

/* Admin User Modal Footer */
.admin-user-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-3);
  margin-top: var(--space-6);
  padding-top: var(--space-4);
  border-top: var(--border-width) solid var(--border-color);
}

/* Student Management */
.student-form {
  margin-bottom: var(--space-4);
  padding: var(--space-4);
  background: var(--color-white);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--radius);
  position: relative;
}

/* Existing Students Display */
.existing-student-card {
  margin-bottom: var(--space-2);
  border: var(--border-width) solid var(--color-gray-200);
  border-radius: var(--radius);
  background: var(--color-white);
  overflow: hidden;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.existing-student-card:hover {
  border-color: var(--color-blue);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.student-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2) var(--space-3);
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: var(--border-width) solid var(--color-gray-100);
  transition: all 0.2s ease;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  min-height: 60px;
}

.student-card-header:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
}

.student-basic-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.student-name {
  font-weight: 600;
  color: var(--color-gray-900);
  font-size: var(--font-size-sm);
  margin: 0;
  line-height: 1.4;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.student-class {
  font-size: var(--font-size-xs);
  background: linear-gradient(135deg, var(--color-blue) 0%, #2563eb 100%);
  color: var(--color-white);
  padding: var(--space-1) var(--space-2);
  border-radius: 12px;
  display: inline-block;
  font-weight: 500;
  width: fit-content;
  letter-spacing: 0.025em;
  text-transform: uppercase;
}

.expand-icon {
  color: var(--color-gray-400);
  transition: all 0.2s ease;
  font-size: var(--font-size-sm);
}

.expand-icon:hover {
  color: var(--color-blue);
}

.student-details {
  padding: var(--space-3);
  background: var(--color-white);
  border-top: 1px solid var(--color-gray-50);
  width: 100%;
  box-sizing: border-box;
}

.student-detail-grid {
   display: flex;
  flex-wrap: wrap;
  justify-content: space-between; /* distributes items evenly */
  gap: 16px;
  max-width: 100%;
  padding: 0 3% 0 3%;
}

.detail-item {
   display: flex;
  flex-direction: column; ;
  gap: var(--space-1);
  min-width: 0;
  word-wrap: break-word;
}

.detail-label {
  font-size: var(--font-size-xs);
  font-weight: 600;
  color: var(--color-gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-1);
}

.detail-value {
  font-size: var(--font-size-sm);
  color: var(--color-gray-900);
  font-weight: 500;
  padding: var(--space-2);
  background: var(--color-gray-50);
  border-radius: var(--radius);
  border: var(--border-width) solid var(--color-gray-100);
  transition: all 0.2s ease;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.detail-value:hover {
  background: var(--color-gray-100);
  border-color: var(--color-gray-200);
}

/* Student Edit Form */
.student-edit-form {
  padding: var(--space-3);
  background: var(--color-white);
  border-radius: var(--radius);
}

.student-edit-form .form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-3);
  margin-bottom: var(--space-3);
}

.student-edit-form .form-group {
  margin-bottom: var(--space-4);
}

.student-edit-form label {
  display: block;
  margin-bottom: var(--space-1);
  font-size: var(--font-size-xs);
  font-weight: 600;
  color: var(--color-gray-700);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.student-edit-form input,
.student-edit-form select {
  width: 100%;
  padding: var(--space-2) var(--space-3);
  border: var(--border-width) solid var(--color-gray-200);
  border-radius: var(--radius);
  font-size: var(--font-size-sm);
  font-family: inherit;
  transition: all 0.2s ease;
  background: var(--color-white);
  height: 3rem;
}

.student-edit-form input:focus,
.student-edit-form select:focus {
  outline: none;
  border-color: var(--color-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.student-edit-form input.error {
  border-color: var(--color-red);
}

.student-edit-form .error-message {
  color: var(--color-red);
  font-size: var(--font-size-xs);
  margin-top: var(--space-1);
  display: block;
}

.student-edit-form .field-note {
  display: block;
  margin-top: var(--space-1);
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
  font-style: italic;
}

.student-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-3);
  padding-bottom: var(--space-2);
  border-bottom: var(--border-width) solid var(--border-color);
}

.student-header h4 {
  margin: 0;
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--color-gray-700);
}

.admin-btn-add-student {
  background: var(--color-blue);
  color: var(--color-white);
  border: none;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius);
  font-size: var(--font-size-xs);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.admin-btn-add-student:hover {
  background: #2563eb;
}

.admin-btn-remove-student {
  background: var(--color-red);
  color: var(--color-white);
  border: none;
  padding: var(--space-1);
  border-radius: var(--radius);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.75rem;
  height: 1.75rem;
}

.admin-btn-remove-student:hover {
  background: #dc2626;
}

/* Date Input Styling */
input[type="date"] {
  width: 100%;
  padding: var(--space-3) var(--space-3);
  border: var(--border-width) solid var(--color-gray-200);
  border-radius: var(--radius);
  font-size: var(--font-size-sm);
  font-family: inherit;
  transition: all 0.2s ease;
  background: var(--color-white);
  height: 3rem;
  display: flex;
  align-items: center;
}

input[type="date"]:focus {
  outline: none;
  border-color: var(--color-blue);
  background: var(--color-white);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

input[type="date"].error {
  border-color: var(--color-red);
}

/* ===== MODERN MODAL BUTTONS ===== */
.admin-btn-cancel,
.admin-btn-save {
  padding: var(--modal-space-4) var(--modal-space-6);
  border-radius: var(--modal-radius-lg);
  font-size: var(--modal-font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
  min-width: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--modal-space-2);
}

.admin-btn-cancel {
  background: var(--modal-white);
  color: var(--modal-gray-700);
  border-color: var(--modal-gray-300);
  box-shadow: var(--modal-shadow-sm);
}

.admin-btn-save {
  background: linear-gradient(135deg, var(--modal-blue-500), var(--modal-blue-600));
  color: var(--modal-white);
  border-color: var(--modal-blue-500);
  box-shadow: var(--modal-shadow-md);
}

.admin-btn-cancel:hover {
  background: var(--modal-gray-50);
  border-color: var(--modal-gray-400);
  transform: translateY(-1px);
  box-shadow: var(--modal-shadow-md);
}

.admin-btn-save:hover {
  background: linear-gradient(135deg, var(--modal-blue-600), var(--modal-blue-700));
  transform: translateY(-2px);
  box-shadow: var(--modal-shadow-lg);
}

.admin-btn-save:active,
.admin-btn-cancel:active {
  transform: translateY(0);
}

/* ===== MODERN MODAL FOOTER ===== */
.admin-user-modal-footer,
.user-modal-footer {
  padding: var(--modal-space-6) var(--modal-space-8);
  background: var(--modal-gray-50);
  border-top: 1px solid var(--modal-gray-200);
  display: flex;
  justify-content: flex-end;
  gap: var(--modal-space-4);
  position: sticky;
  bottom: 0;
  z-index: 10;
}

/* ===== FORM ELEMENTS ===== */
.admin-form-group,
.form-group {
  margin-bottom: var(--modal-space-5);
}

.admin-form-row,
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--modal-space-4);
  margin-bottom: var(--modal-space-5);
}

.admin-form-section,
.form-section {
  margin-bottom: var(--modal-space-6);
  padding: var(--modal-space-6);
  background: var(--modal-white);
  border: 1px solid var(--modal-gray-200);
  border-radius: var(--modal-radius-xl);
  box-shadow: var(--modal-shadow-sm);
  position: relative;
  overflow: hidden;
}

.admin-form-section::before,
.form-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--modal-blue-500), var(--modal-blue-600));
}

.admin-form-section h3,
.form-section h3 {
  margin: 0 0 var(--modal-space-6) 0;
  font-size: var(--modal-font-size-lg);
  font-weight: 700;
  color: var(--modal-gray-900);
  display: flex;
  align-items: center;
  gap: var(--modal-space-3);
  padding-bottom: var(--modal-space-4);
  border-bottom: 1px solid var(--modal-gray-200);
}

.admin-form-section h3 svg,
.form-section h3 svg {
  color: var(--modal-blue-500);
  font-size: 1.25rem;
}

/* ===== FIELD STYLING ===== */
.admin-field-note,
.field-note {
  font-size: var(--modal-font-size-xs);
  color: var(--modal-gray-500);
  margin-top: var(--modal-space-1);
  font-style: italic;
}

.admin-error-message,
.error-message {
  color: var(--modal-red);
  font-size: var(--modal-font-size-xs);
  margin-top: var(--modal-space-1);
  display: block;
}

.admin-required,
.required {
  color: var(--modal-red);
}

.admin-optional,
.optional {
  color: var(--modal-gray-500);
  font-weight: normal;
}

/* ===== STATUS TOGGLE ===== */
.admin-status-toggle,
.status-toggle {
  display: flex;
  align-items: center;
  gap: var(--modal-space-2);
}

.admin-toggle-label,
.toggle-label {
  font-size: var(--modal-font-size-base);
  color: var(--modal-gray-700);
  cursor: pointer;
}

/* ===== STUDENT SECTIONS ===== */
.admin-section-header,
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--modal-space-6);
}

.admin-section-header h3,
.section-header h3 {
  margin: 0;
}

.admin-btn-add-student,
.btn-add-student {
  background: linear-gradient(135deg, var(--modal-green), #059669);
  color: var(--modal-white);
  border: none;
  border-radius: var(--modal-radius-lg);
  padding: var(--modal-space-3) var(--modal-space-4);
  font-size: var(--modal-font-size-sm);
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--modal-space-2);
  transition: all 0.3s ease;
}

.admin-btn-add-student:hover,
.btn-add-student:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-1px);
  box-shadow: var(--modal-shadow-md);
}

.admin-btn-remove-student,
.btn-remove-student {
  background: linear-gradient(135deg, var(--modal-red), #dc2626);
  color: var(--modal-white);
  border: none;
  border-radius: var(--modal-radius);
  padding: var(--modal-space-2);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  width: 32px;
  height: 32px;
}

.admin-btn-remove-student:hover,
.btn-remove-student:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: scale(1.1);
}

/* ===== STUDENT CARDS ===== */
.admin-existing-student-card,
.existing-student-card {
  background: var(--modal-gray-50);
  border: 1px solid var(--modal-gray-200);
  border-radius: var(--modal-radius-lg);
  margin-bottom: var(--modal-space-4);
  overflow: hidden;
  transition: all 0.3s ease;
}

.admin-existing-student-card:hover,
.existing-student-card:hover {
  box-shadow: var(--modal-shadow-md);
  border-color: var(--modal-blue-300);
}

.admin-student-card-header,
.student-card-header {
  padding: var(--modal-space-4);
  background: var(--modal-white);
  border-bottom: 1px solid var(--modal-gray-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.admin-student-card-header:hover,
.student-card-header:hover {
  background: var(--modal-blue-50);
}

.admin-student-basic-info,
.student-basic-info {
  display: flex;
  flex-direction: column;
  gap: var(--modal-space-1);
}

.admin-student-name,
.student-name {
  font-weight: 600;
  color: var(--modal-gray-900);
  font-size: var(--modal-font-size-base);
}

.admin-expand-icon,
.expand-icon {
  color: var(--modal-gray-500);
  transition: transform 0.3s ease;
}

.admin-student-details,
.student-details {
  padding: var(--modal-space-6);
  background: var(--modal-white);
  border-top: 1px solid var(--modal-gray-200);
}

.admin-student-form,
.student-form {
  background: var(--modal-white);
  border: 1px solid var(--modal-gray-200);
  border-radius: var(--modal-radius-lg);
  padding: var(--modal-space-6);
  margin-bottom: var(--modal-space-4);
}

.admin-student-header,
.student-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--modal-space-4);
  padding-bottom: var(--modal-space-3);
  border-bottom: 1px solid var(--modal-gray-200);
}

.admin-student-header h4,
.student-header h4 {
  margin: 0;
  color: var(--modal-gray-900);
  font-size: var(--modal-font-size-lg);
}

.admin-student-edit-form,
.student-edit-form {
  background: var(--modal-white);
}

.admin-student-view-container,
.student-view-container {
  background: var(--modal-white);
}

.admin-student-image-section,
.student-image-section {
  margin-bottom: var(--modal-space-4);
}

.admin-student-detail-grid,
.student-detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--modal-space-4);
}

.admin-detail-item,
.detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--modal-space-1);
}

.admin-detail-label,
.detail-label {
  font-size: var(--modal-font-size-xs);
  color: var(--modal-gray-500);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-detail-value,
.detail-value {
  font-size: var(--modal-font-size-base);
  color: var(--modal-gray-900);
  font-weight: 500;
}

/* ===== MODERN PLACEHOLDER STYLING ===== */
input::placeholder,
textarea::placeholder {
  color: var(--color-gray-400);
  font-style: italic;
}

input:focus::placeholder,
textarea:focus::placeholder {
  color: var(--color-gray-300);
}

/* Textarea specific styling */
textarea {
  resize: vertical;
  min-height: 80px;
}

/* Select Styling - Fixed to prevent multiple arrows */
select {
  cursor: pointer;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23525252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right var(--space-3) center !important;
  background-size: 0.875em !important;
  padding-right: 2.5rem !important;
  height: 3rem;
  display: flex;
  align-items: center;
}

/* Specific fix for role selection dropdown */
.admin-form-group select[name="role"] {
  cursor: pointer !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%232563eb' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right 12px center !important;
  background-size: 16px !important;
  padding-right: 40px !important;
  background-color: var(--modal-white) !important;
}

/* Remove any inherited background images */
.admin-form-group select[name="role"]::-ms-expand {
  display: none !important;
}

/* Global fix for all select elements in modal to prevent multiple arrows */
.admin-modal-content select,
.admin-modal select,
.modal-content select {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

/* Remove IE/Edge default arrows for all selects in modal */
.admin-modal-content select::-ms-expand,
.admin-modal select::-ms-expand,
.modal-content select::-ms-expand {
  display: none !important;
}

/* Ensure only one background image per select */
.admin-form-group select {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%232563eb' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right 12px center !important;
  background-size: 16px !important;
  padding-right: 40px !important;
}

select:focus {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233b82f6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

/* Layout Utilities */
.flex-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-2);
}

.flex-column {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.text-truncate {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.full-width {
  width: 100%;
  box-sizing: border-box;
}

/* Prevent layout issues */
.modal-overlay * {
  box-sizing: border-box;
}

.user-modal * {
  box-sizing: border-box;
}

/* Fix for student card layout */
.existing-student-card * {
  box-sizing: border-box;
}

.student-card-header * {
  box-sizing: border-box;
}

.student-details * {
  box-sizing: border-box;
}

/* Responsive Design */
@media (max-width: 900px) {
  .user-modal-large {
    max-width: 90vw;
  }
}

@media (max-width: 768px) {
  .student-detail-grid {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: var(--space-2);
  }

  .detail-value {
    padding: var(--space-2);
    font-size: var(--font-size-sm);
  }

  .detail-label {
    font-size: var(--font-size-xs);
  }
}

@media (max-width: 640px) {
  .modal-overlay {
    padding: var(--space-2);
  }

  .user-modal {
    max-width: none;
    margin: 0;
  }

  .user-modal-large {
    max-width: none;
    margin: 0;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .admin-user-modal-footer {
    flex-direction: column;
  }

  .admin-btn-cancel,
  .admin-btn-save {
    width: 100%;
    justify-content: center;
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-2);
  }

  .admin-btn-add-student {
    align-self: flex-start;
  }

  .form-section {
    padding: var(--space-3);
  }

  .student-form {
    padding: var(--space-3);
  }

  .student-detail-grid {
    grid-template-columns: 1fr;
  }

  .existing-student-card {
    margin-bottom: var(--space-2);
  }

  .student-card-header {
    padding: var(--space-2);
  }

  .student-details {
    padding: var(--space-3);
  }

  .student-edit-form {
    padding: var(--space-2);
  }

  .student-edit-form .form-row {
    grid-template-columns: 1fr;
    gap: var(--space-2);
  }

  .student-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }

  .admin-btn-remove-student {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .admin-user-modal-header,
  .admin-user-modal form {
    padding: var(--space-3);
  }

  .admin-user-modal-header h2 {
    font-size: var(--font-size-base);
  }

  .info-section {
    padding: var(--space-3);
  }

  .form-group {
    margin-bottom: var(--space-3);
  }

  .admin-user-modal-footer {
    margin-top: var(--space-4);
    padding-top: var(--space-3);
  }

  .student-detail-grid {
    grid-template-columns: 1fr;
    gap: var(--space-2);
  }

  .detail-item {
    margin-bottom: var(--space-2);
  }
}

/* Modal Demo Badge */
.modal-demo-badge {
  display: inline-block;
  background-color: #fbbf24;
  color: #92400e;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  margin-left: 0.5rem;
  vertical-align: middle;
}

/* Demo Warning */
.demo-warning {
  background-color: #fef3c7;
  border: 1px solid #fbbf24;
  border-radius: 0.375rem;
  padding: 0.75rem 1rem;
  margin: 0 1.5rem 1rem 1.5rem;
}

.demo-warning p {
  margin: 0;
  color: #92400e;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Demo Text */
.demo-text {
  color: #f59e0b !important;
  font-weight: 600 !important;
  font-style: italic;
}

/* Mock Data Info */
.mock-data-info {
  color: #6b7280;
  font-style: italic;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

/* Enhanced button styling for demo mode */
.admin-btn-save:has-text("Demo") {
  background-color: #f59e0b;
  border-color: #f59e0b;
}

.admin-btn-save:has-text("Demo"):hover {
  background-color: #d97706;
}

/* Mobile adjustments for demo elements */
@media (max-width: 640px) {
  .modal-demo-badge {
    font-size: 0.65rem;
    padding: 0.125rem 0.375rem;
  }
  
  .demo-warning {
    margin: 0 1rem 1rem 1rem;
    padding: 0.5rem 0.75rem;
  }
  
  .demo-warning p {
    font-size: 0.8rem;
  }
}

/* Student Image Section */
.student-image-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: var(--space-4);
  padding: var(--space-3);
  background: var(--color-gray-50);
  border-radius: var(--radius);
  border: var(--border-width) solid var(--color-gray-100);
}

.student-image-section label {
  margin-bottom: var(--space-2);
  text-align: center;
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--color-gray-700);
}

.student-view-container {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  width: 100%;
}

/* Enhanced Student List Section */
.student-list-section {
  background: var(--color-white);
  border: var(--border-width) solid var(--color-gray-100);
  border-radius: var(--radius);
  overflow: hidden;
}

.student-list-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: var(--space-3);
  border-bottom: var(--border-width) solid var(--color-gray-100);
}

.student-list-header h4 {
  margin: 0;
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--color-gray-900);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.student-list-header h4::before {
  content: "👥";
  font-size: var(--font-size-base);
}

.student-list-content {
  padding: var(--space-2);
  max-height: 300px;
  overflow-y: auto;
}

.student-list-content::-webkit-scrollbar {
  width: 6px;
}

.student-list-content::-webkit-scrollbar-track {
  background: var(--color-gray-50);
  border-radius: 3px;
}

.student-list-content::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: 3px;
}

.student-list-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-400);
}

.empty-student-list {
  text-align: center;
  padding: var(--space-6);
  color: var(--color-gray-500);
  font-style: italic;
}

.empty-student-list::before {
  content: "📚";
  display: block;
  font-size: 2rem;
  margin-bottom: var(--space-2);
}

/* Compact Student Cards */
.student-card-compact {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2);
  margin-bottom: var(--space-1);
  background: var(--color-gray-50);
  border-radius: var(--radius);
  transition: all 0.2s ease;
}

.student-card-compact:hover {
  background: var(--color-blue);
  color: var(--color-white);
  transform: translateX(4px);
}

.student-card-compact .student-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--color-blue) 0%, #2563eb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  font-weight: 600;
  font-size: var(--font-size-xs);
  text-transform: uppercase;
}

.student-card-compact .student-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.student-card-compact .student-name-compact {
  font-weight: 600;
  font-size: var(--font-size-sm);
}

.student-card-compact .student-class-compact {
  font-size: var(--font-size-xs);
  opacity: 0.8;
}

.student-card-compact:hover .student-class-compact {
  opacity: 1;
}

/* Image upload styling within student forms */
.student-form .form-group .student-image-upload,
.student-edit-form .form-group .student-image-upload {
  margin-bottom: 16px;
}

.student-form .form-group .student-image-upload .image-container,
.student-edit-form .form-group .student-image-upload .image-container {
  max-width: 200px;
  margin: 0 auto;
}

.student-form .form-group .student-image-upload .student-image,
.student-edit-form .form-group .student-image-upload .student-image {
  border-radius: 8px;
}

.student-form .form-group .student-image-upload .no-image,
.student-edit-form .form-group .student-image-upload .no-image {
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  color: #6c757d;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.student-form .form-group .student-image-upload .no-image svg,
.student-edit-form .form-group .student-image-upload .no-image svg {
  font-size: 24px;
  color: #adb5bd;
}

.student-form .form-group .student-image-upload .upload-image-btn,
.student-edit-form .form-group .student-image-upload .upload-image-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.student-form .form-group .student-image-upload .upload-image-btn:hover,
.student-edit-form .form-group .student-image-upload .upload-image-btn:hover {
  background: #0056b3;
}

/* New student image upload styling */
.new-student-image-upload {
  margin-bottom: 16px;
}

.new-student-image-upload .image-container {
  position: relative;
  max-width: 200px;
  margin: 0 auto;
}

.new-student-image-upload .image-wrapper {
  position: relative;
  display: inline-block;
}

.new-student-image-upload .image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s;
  border-radius: 8px;
}

.new-student-image-upload .image-wrapper:hover .image-overlay {
  opacity: 1;
}

.new-student-image-upload .image-action-btn {
  background: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.new-student-image-upload .image-action-btn:hover {
  transform: scale(1.1);
}

.new-student-image-upload .upload-btn {
  color: #007bff;
}

.new-student-image-upload .delete-btn {
  color: #dc3545;
}