<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Load Test Results - School Medical Management System</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric-card {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .metric-card.success {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
        }
        .metric-card.warning {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
        }
        .metric-card.danger {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
        }
        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 5px solid #74b9ff;
        }
        .section h2 {
            color: #2c3e50;
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        .performance-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 30px;
            margin: 10px 0;
            overflow: hidden;
            position: relative;
        }
        .performance-fill {
            height: 100%;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            transition: width 0.3s ease;
        }
        .performance-fill.excellent {
            background: linear-gradient(90deg, #00b894, #00a085);
        }
        .performance-fill.good {
            background: linear-gradient(90deg, #74b9ff, #0984e3);
        }
        .performance-fill.fair {
            background: linear-gradient(90deg, #fdcb6e, #e17055);
        }
        .performance-fill.poor {
            background: linear-gradient(90deg, #fd79a8, #e84393);
        }
        .recommendations {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
            border-radius: 10px;
            padding: 25px;
            margin-top: 25px;
        }
        .recommendations h2 {
            color: white;
            margin-top: 0;
        }
        .recommendation-item {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid rgba(255,255,255,0.3);
        }
        .status-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            margin: 5px;
        }
        .status-excellent {
            background: #00b894;
            color: white;
        }
        .status-poor {
            background: #e84393;
            color: white;
        }
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .response-time-chart {
            display: flex;
            align-items: end;
            height: 200px;
            gap: 10px;
            margin: 20px 0;
        }
        .chart-bar {
            background: linear-gradient(to top, #74b9ff, #0984e3);
            border-radius: 5px 5px 0 0;
            min-width: 40px;
            display: flex;
            align-items: end;
            justify-content: center;
            color: white;
            font-weight: bold;
            padding: 5px;
        }
        .timestamp {
            color: #6c757d;
            font-size: 0.9em;
            text-align: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Load Test Results</h1>
        <div class="timestamp">
            Test completed: <strong>December 19, 2024 - 19:36</strong>
        </div>

        <div class="summary">
            <div class="metric-card">
                <div class="metric-value">20</div>
                <div class="metric-label">👥 Concurrent Users</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">32s</div>
                <div class="metric-label">⏱️ Test Duration</div>
            </div>
            <div class="metric-card success">
                <div class="metric-value">230</div>
                <div class="metric-label">📈 Total Requests</div>
            </div>
            <div class="metric-card warning">
                <div class="metric-value">52.6%</div>
                <div class="metric-label">📊 Success Rate</div>
            </div>
            <div class="metric-card success">
                <div class="metric-value">3.18ms</div>
                <div class="metric-label">⚡ Avg Response Time</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">7.18</div>
                <div class="metric-label">🚀 Requests/Second</div>
            </div>
        </div>

        <div class="section">
            <h2>📊 Performance Breakdown</h2>
            
            <h3>🌐 Frontend Performance</h3>
            <div class="performance-bar">
                <div class="performance-fill excellent" style="width: 100%;">
                    EXCELLENT - 100% Success Rate
                </div>
            </div>
            <span class="status-badge status-excellent">🟢 Frontend: 1-4ms response time</span>
            
            <h3>🔧 Backend Performance</h3>
            <div class="performance-bar">
                <div class="performance-fill poor" style="width: 47%;">
                    POOR - 47% Success Rate
                </div>
            </div>
            <span class="status-badge status-poor">🔴 Backend: Status 400 errors</span>
        </div>

        <div class="section">
            <h2>📈 Response Time Analysis</h2>
            <div class="chart-container">
                <h3>Response Time Percentiles</h3>
                <div class="response-time-chart">
                    <div class="chart-bar" style="height: 30%;" title="50th percentile">
                        3ms<br><small>50th</small>
                    </div>
                    <div class="chart-bar" style="height: 50%;" title="95th percentile">
                        5ms<br><small>95th</small>
                    </div>
                    <div class="chart-bar" style="height: 60%;" title="99th percentile">
                        6ms<br><small>99th</small>
                    </div>
                    <div class="chart-bar" style="height: 10%;" title="Minimum">
                        1ms<br><small>Min</small>
                    </div>
                    <div class="chart-bar" style="height: 100%;" title="Maximum">
                        31ms<br><small>Max</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎯 Detailed Results</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h3>✅ Successful Requests</h3>
                    <ul>
                        <li><strong>121 requests</strong> completed successfully</li>
                        <li>All <strong>frontend requests</strong> succeeded</li>
                        <li>Response times: <strong>1-4ms</strong></li>
                        <li>Very stable performance</li>
                    </ul>
                </div>
                <div>
                    <h3>❌ Failed Requests</h3>
                    <ul>
                        <li><strong>109 requests</strong> failed</li>
                        <li>All failures from <strong>backend API</strong></li>
                        <li>Status code: <strong>400 (Bad Request)</strong></li>
                        <li>Likely authentication issues</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="recommendations">
            <h2>💡 Recommendations</h2>
            
            <div class="recommendation-item">
                <h3>🔧 Backend Optimization (Priority: HIGH)</h3>
                <p><strong>Issue:</strong> Backend API returning 400 errors</p>
                <p><strong>Action:</strong> Check authentication endpoints and API routes</p>
                <p><strong>Expected Impact:</strong> Increase success rate to 90%+</p>
            </div>

            <div class="recommendation-item">
                <h3>🌐 Frontend Scaling (Priority: LOW)</h3>
                <p><strong>Status:</strong> Frontend performance is excellent</p>
                <p><strong>Action:</strong> Can safely test with 50+ concurrent users</p>
                <p><strong>Expected Impact:</strong> Maintain current performance levels</p>
            </div>

            <div class="recommendation-item">
                <h3>🧪 Next Testing Phase</h3>
                <p><strong>Step 1:</strong> Fix backend API issues</p>
                <p><strong>Step 2:</strong> Test with proper authentication</p>
                <p><strong>Step 3:</strong> Gradually increase to 50, 100, 200 users</p>
                <p><strong>Target:</strong> 95% success rate with <5s response time</p>
            </div>
        </div>

        <div class="section">
            <h2>🚀 Production Readiness Assessment</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h3>✅ Ready for Production</h3>
                    <ul>
                        <li>Frontend static assets</li>
                        <li>UI responsiveness</li>
                        <li>Client-side performance</li>
                        <li>Asset loading speed</li>
                    </ul>
                </div>
                <div>
                    <h3>⚠️ Needs Optimization</h3>
                    <ul>
                        <li>Backend API endpoints</li>
                        <li>Authentication system</li>
                        <li>Database connections</li>
                        <li>Error handling</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📋 Test Configuration</h2>
            <div style="background: #f1f3f4; padding: 15px; border-radius: 8px; font-family: monospace;">
                <strong>Frontend URL:</strong> http://localhost:5173<br>
                <strong>Backend URL:</strong> http://localhost:8080<br>
                <strong>Test Type:</strong> Mixed (Frontend + Backend)<br>
                <strong>Request Interval:</strong> 2000ms ± random<br>
                <strong>Timeout:</strong> 10 seconds<br>
                <strong>Test Method:</strong> HTTP GET requests
            </div>
        </div>
    </div>

    <script>
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Animate metric cards
            const cards = document.querySelectorAll('.metric-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.transform = 'translateY(0)';
                    card.style.opacity = '1';
                }, index * 100);
            });

            // Add click handlers for more details
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                section.addEventListener('click', function() {
                    this.style.transform = 'scale(1.02)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 200);
                });
            });
        });
    </script>
</body>
</html>
