/* Feedback Detail Modal Styles */
.feedback-modal .modal-content {
  border-radius: 12px;
  border: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.feedback-modal .modal-header {
  background: linear-gradient(135deg, #1C3FAA 0%, #2A52C5 100%);
  color: white;
  border-bottom: none;
  padding: 1.25rem 1.5rem;
}

.feedback-modal .modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.feedback-modal .modal-title-icon {
  margin-right: 0.75rem;
}

.feedback-modal .btn-close {
  filter: invert(1) grayscale(100%) brightness(200%);
}

.feedback-modal .modal-body {
  padding: 1.5rem 2rem;
  background-color: #f8f9fa;
}

.feedback-info-section {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border: 1px solid #e9ecef;
}

.feedback-info-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.feedback-student-info p {
  margin: 0;
  line-height: 1.6;
  color: #495057;
}

.feedback-student-info strong {
  color: #212529;
  margin-right: 0.5rem;
}

.feedback-status-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.85rem;
  display: inline-flex;
  align-items: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.feedback-status-badge.approved {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.feedback-status-badge.rejected {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.feedback-status-badge.pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeeba;
}

.feedback-status-badge-icon {
  margin-right: 0.5rem;
  font-size: 1rem;
}

.feedback-details-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #343a40;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
}

.feedback-details-icon {
  margin-right: 0.75rem;
  color: #1C3FAA;
}

.feedback-modal .list-group-item {
  border: none;
  padding: 0.5rem 0;
  background-color: transparent;
}

.feedback-modal .text-muted {
  font-style: italic;
  color: #6c757d !important;
}

.feedback-modal .modal-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding: 1rem;
  justify-content: flex-end;
}

.feedback-modal .btn-close-custom {
  background-color: #6c757d;
  border-color: #6c757d;
  color: white;
  font-weight: 500;
  padding: 0.5rem 1.5rem;
  border-radius: 25px;
  transition: all 0.3s ease;
}
.feedback-modal .btn-close-custom:hover {
  background-color: #5a6268;
  border-color: #545b62;
}

.lukhang-checkuplist-wrapper {
  background: #f8f9fa !important;
  min-height: 100vh !important;
}

.lukhang-checkuplist-header-section {
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%) !important;
  border: 1px solid #e9ecef !important;
  border-radius: 12px !important;
  padding: 2rem !important;
  margin-bottom: 2rem !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08) !important;
}

.lukhang-checkuplist-title-section {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 1.5rem !important;
}

.lukhang-checkuplist-main-title {
  color: #495057 !important;
  font-weight: 700 !important;
  font-size: 1.75rem !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  text-shadow: none !important;
}

.lukhang-checkuplist-main-title i {
  color: #dc3545 !important;
  margin-right: 0.75rem !important;
  font-size: 1.5rem !important;
}

.lukhang-checkuplist-reset-button {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
  border: 2px solid #007bff !important;
  color: white !important;
  font-weight: 600 !important;
  padding: 0.5rem 1.5rem !important;
  border-radius: 25px !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2) !important;
  font-size: 0.9rem !important;
}

.lukhang-checkuplist-reset-button:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%) !important;
  border-color: #0056b3 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3) !important;
  color: white !important;
}

.lukhang-checkuplist-reset-button:focus {
  box-shadow: 0 0 0 4px rgba(0, 123, 255, 0.25) !important;
  color: white !important;
}

.lukhang-checkuplist-filters-row {
  display: flex !important;
  gap: 1rem !important;
  align-items: end !important;
  flex-wrap: wrap !important;
}

.lukhang-checkuplist-status-filter {
  background: white !important;
  border: 2px solid #e9ecef !important;
  border-radius: 8px !important;
  padding: 0.25rem !important;
  min-width: 200px !important;
  transition: all 0.3s ease !important;
}

.lukhang-checkuplist-status-filter:focus-within {
  border-color: #007bff !important;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1) !important;
}

.lukhang-checkuplist-status-select {
  border: none !important;
  outline: none !important;
  background: transparent !important;
  color: #495057 !important;
  font-weight: 500 !important;
}

.lukhang-checkuplist-search-container {
  flex: 1 !important;
  min-width: 250px !important;
}

.lukhang-checkuplist-search-input {
  border: 2px solid #e9ecef !important;
  border-radius: 8px !important;
  padding: 0.75rem 1rem !important;
  font-size: 1rem !important;
  transition: all 0.3s ease !important;
  background: white !important;
}

.lukhang-checkuplist-search-input:focus {
  border-color: #007bff !important;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1) !important;
  outline: none !important;
}

.campaign-card .card-header h5 {
  color: white !important;
}

.campaign-card .card-header .id-badge {
  background-color: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

@media (max-width: 768px) {
  .lukhang-checkuplist-header-section {
    padding: 1.5rem !important;
  }
  
  .lukhang-checkuplist-title-section {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 1rem !important;
  }
  
  .lukhang-checkuplist-main-title {
    font-size: 1.5rem !important;
  }
  
  .lukhang-checkuplist-filters-row {
    flex-direction: column !important;
    gap: 1rem !important;
  }
  
  .lukhang-checkuplist-status-filter,
  .lukhang-checkuplist-search-container {
    min-width: 100% !important;
  }
}

/* CheckupList.css - Full width layout */
.checkup-list-container {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
  width: 100%;
  /* Không giới hạn max-width để full width */
}

.page-header {
  margin-bottom: 30px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.header-filters {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.filter-actions {
  display: flex;
  align-items: center;
  background: none;
  border: none;
}

.search-container {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-input {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  font-size: 0.875rem;
  height: 38px;
}

.search-container {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-input {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  font-size: 0.875rem;
  height: 38px;
}

.search-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.campaign-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin-bottom: 32px;
  width: 100%;
}

@media (max-width: 1200px) {
  .campaign-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .campaign-grid {
    grid-template-columns: 1fr;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .search-container {
    width: 100%;
  }
}

.campaign-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 320px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.campaign-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.campaign-card .card-header {
  background: linear-gradient(135deg, #015C92 0%, #2D82B5 100%);
  color: white;
  border: none;
  padding: 16px 20px;
  font-size: 1.1rem;
  font-weight: 600;
}

.campaign-card .card-body {
  padding: 20px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.campaign-card .card-text {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 16px;
}

.campaign-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin: 16px 0;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 6px;
  border-left: 3px solid #3b82f6;
}

.stat-item svg {
  flex-shrink: 0;
}

.stat-item div {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-item span {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

.stat-item strong {
  font-size: 1.125rem;
  color: #1f2937;
  font-weight: 700;
}

.progress-container-custom {
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.progress-container-custom .progress-bar {
  height: 100%;
  transition: width 0.3s ease;
}

.campaign-card .card-footer {
  background: #f8fafc;
  border-top: 1px solid #e5e7eb;
  padding: 16px 20px;
  font-size: 0.875rem;
  color: #6b7280;
}

.campaign-card .card-footer .btn {
  background: linear-gradient(to right, #38bdf8, #3b82f6);
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 0.875rem;
  font-weight: 500;
  transition: opacity 0.3s ease;
}

.campaign-card .card-footer .btn:hover {
  opacity: 0.85;
}

.records-summary{
  gap: 20px;
  margin-bottom: 30px;
  margin-top: 20px;
}

.summary-card {
  flex: 1;
  min-width: 200px;
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.summary-card.total {
  background-color: #f0f7ff;
  border-left: 4px solid #3498db;
}

.summary-card.completed {
  background-color: #f0fff4;
  border-left: 4px solid #2ecc71;
}

.summary-card.rejected {
  background-color: #fff0f0;
  border-left: 4px solid #e74c3c;
}

.summary-card.pending {
  background-color: #fffbf0;
  border-left: 4px solid #f39c12;
}

.summary-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
}

.summary-card.total .summary-icon {
  background-color: rgba(52, 152, 219, 0.15);
  color: #3498db;
}

.summary-card.completed .summary-icon {
  background-color: rgba(46, 204, 113, 0.15);
  color: #2ecc71;
}

.summary-card.rejected .summary-icon {
  background-color: rgba(231, 76, 60, 0.15);
  color: #e74c3c;
}

.summary-card.pending .summary-icon {
  background-color: rgba(243, 156, 18, 0.15);
  color: #f39c12;
}

.summary-info {
  flex: 1;
}

.summary-info p {
  margin: 0;
  font-size: 14px;
  color: #7f8c8d;
}

.summary-info h3 {
  margin: 5px 0 0;
  font-size: 28px;
  font-weight: 600;
}

.summary-info .percentage {
  font-size: 14px;
  color: #7f8c8d;
  margin-left: 5px;
}

.summary-card.total .summary-info h3 {
  color: #3498db;
}

.summary-card.completed .summary-info h3 {
  color: #2ecc71;
}

.summary-card.rejected .summary-info h3 {
  color: #e74c3c;
}

.summary-card.pending .summary-info h3 {
  color: #f39c12;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .records-summary {
    flex-wrap: wrap;
  }
  
  .summary-card {
    min-width: calc(50% - 20px);
    margin-bottom: 20px;
  }
}

@media (max-width: 576px) {
  .summary-card {
    min-width: 100%;
  }
}

/* Header styles */
.checkup-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

/* Button styles */
.create-btn {
  padding: 8px 16px;
  background-color: var(--primary-color);
  border: none;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.create-btn:hover {
  background-color: var(--primary-color-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.create-btn i {
  font-size: 14px;
}

/* Search and filter controls */
.controls {
  display: flex;
  gap: 15px;
}

.search-filter-container {
  display: flex;
  gap: 10px;
}

.search-input {
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  min-width: 240px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f5f5f5;
  font-size: 14px;
  cursor: pointer;
}

/* Table styles */
.table-container {
  overflow-x: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  margin-bottom: 10px; /* Space for pagination */
}

.checkup-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-top: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  overflow: hidden;
  flex: 1;
}

.checkup-table thead th {
  background-color: #f8f9fa;
  position: sticky;
  top: 0;
  z-index: 10;
}

.checkup-table tbody tr:hover {
  background-color: rgba(236, 245, 255, 0.7);
}

.checkup-table th {
  background-color: #f5f5f5;
  padding: 12px;
  text-align: left;
  font-weight: 600;
  color: #555;
  border-bottom: 2px solid #ddd;
}

.checkup-table td {
  padding: 12px;
  border-bottom: 1px solid #eaeaea;
  vertical-align: middle; /* Center content vertically */
}

.checkup-table th:last-child,
.checkup-table td:last-child {
  min-width: 160px; /* Ensure the action column is wide enough */
  text-align: center; /* Center the action buttons */
  vertical-align: top;
}

.checkup-table tr:hover {
  background-color: #f9f9f9;
}

.campaign-name {
  cursor: pointer;
  position: relative;
  transition: color 0.2s;
  font-weight: 500;
}

.campaign-name:hover {
  color: #2980b9;
}

.view-details {
  font-size: 12px;
  color: #3498db;
  margin-left: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.campaign-name:hover .view-details {
  opacity: 1;
}

.campaign-description {
  font-size: 13px;
  color: #666;
  margin-top: 4px;
}

.progress {
  height: 10px;
  background-color: #f5f5f5;
  border-radius: 5px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: #4CAF50;
}

.progress-text {
  font-size: 12px;
  margin-top: 4px;
  text-align: center;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-active {
  background-color: rgba(46, 204, 113, 0.15);
  color: #27ae60;
}

.status-upcoming {
  background-color: rgba(52, 152, 219, 0.15);
  color: #2980b9;
}

.status-completed {
  background-color: rgba(149, 165, 166, 0.15);
  color: #7f8c8d;
}

.status-cancelled {
  background-color: rgba(231, 76, 60, 0.15);
  color: #c0392b;
}

.action-buttons {
  display: flex;
  gap: 10px;
  justify-content: center; /* Center the buttons in the container */
  min-width: 150px; /* Increase minimum width to fit all buttons */
  flex-wrap: nowrap; /* Prevent wrapping of buttons */
}

.action-btn {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  border: none;
  margin: 0 2px;
  background-color: #f8f9fa;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  position: relative; /* Ensure proper positioning */
  z-index: 5; /* Higher z-index to prevent being hidden */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0; /* Prevent button from shrinking */
}

.view-btn {
  color: #27ae60;
  background-color: rgba(46, 204, 113, 0.1);
}

.view-btn:hover {
  background-color: rgba(46, 204, 113, 0.2);
  color: #27ae60;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(46, 204, 113, 0.2);
}

.edit-btn {
  color: #2980b9;
  background-color: rgba(52, 152, 219, 0.1);
}

.edit-btn:hover {
  background-color: rgba(52, 152, 219, 0.2);
  color: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(52, 152, 219, 0.2);
}

.delete-btn {
  color: #c0392b;
  background-color: rgba(231, 76, 60, 0.1);
}

.delete-btn:hover {
  background-color: rgba(231, 76, 60, 0.2);
  color: #c0392b;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(231, 76, 60, 0.2);
}

.status-btn:hover {
  background-color: rgba(149, 165, 166, 0.1);
}

/* Improved dropdown styles */
.status-dropdown {
  position: relative;
  display: inline-block;
}

.status-dropdown-content {
  display: none;
  position: absolute;
  right: 0;
  background-color: white;
  min-width: 160px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  z-index: 100;
  border-radius: 4px;
}

.status-dropdown:hover .status-dropdown-content {
  display: block;
}

.status-dropdown-content button {
  display: block;
  width: 100%;
  text-align: left;
  padding: 8px 12px;
  border: none;
  background-color: transparent;
  cursor: pointer;
  font-size: 14px;
}

/* Ensure icons in action buttons are visible */
.action-btn i.fas {
  font-size: 16px;
  line-height: 1;
  display: block;
}

.status-dropdown-content button:hover {
  background-color: #f5f5f5;
}

/* Checkup Form styles - NEW */
.checkup-form-container {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  margin: 20px 0;
  position: relative;
  animation: slideDown 0.3s ease-out;
  border: 1px solid #e0e0e0;
}

.checkup-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eaeaea;
  border-radius: 8px 8px 0 0;
}

.checkup-form-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: #777;
  cursor: pointer;
  font-size: 18px;
  padding: 5px;
  transition: all 0.2s;
}

.close-btn:hover {
  color: #e74c3c;
  transform: scale(1.1);
}

.checkup-form {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 25px;
  padding-top: 15px;
  border-top: 1px solid #eaeaea;
}

/* Form animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Checkup items container */
.checkup-items-container {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  margin-top: 5px;
  background-color: #f8f9fa;
}

.checkup-items-container .row {
  margin-bottom: 10px;
}

.checkup-items-container .row:last-child {
  margin-bottom: 0;
}

.form-check {
  display: flex;
  align-items: center;
}

.form-check-input {
  margin-right: 8px;
}

/* Progress bar styles */
.progress {
  height: 8px;
  border-radius: 4px;
  background-color: #f1f1f1;
  overflow: hidden;
  margin-bottom: 4px;
}

.progress-bar {
  background-color: #27ae60;
  height: 100%;
  border-radius: 4px;
}

.progress-text {
  font-size: 12px;
  color: #666;
}

/* Empty message styles */
.empty-message {
  text-align: center;
  padding: 30px;
  color: #7f8c8d;
  font-style: italic;
}

/* Loading overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(0, 123, 255, 0.2);
  border-radius: 50%;
  border-top-color: #007bff;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Pagination styles */
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 32px 0;
  gap: 16px;
}

.pagination-info {
  color: #6c757d;
  font-size: 0.9rem;
}

.pagination-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.pagination-button {
  padding: 8px 16px;
  border: 1px solid #dee2e6;
  background: white;
  color: #495057;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.pagination-button:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.pagination-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.pagination-button.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.pagination-button.active:hover {
  background: #0056b3;
  border-color: #0056b3;
}

@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column;
    gap: 12px;
  }
  
  .pagination-controls {
    flex-wrap: wrap;
    justify-content: center;
  }
}

/* Required field indicator */
.required-field {
  color: #e74c3c;
  font-weight: bold;
  margin-left: 3px;
}

/* Form validation and error styling */
.form-control.is-invalid, 
.was-validated .form-control:invalid {
  border-color: #e74c3c;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23e74c3c' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23e74c3c' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
  padding-right: calc(1.5em + 0.75rem);
}

.form-control.is-invalid:focus, 
.was-validated .form-control:invalid:focus {
  border-color: #e74c3c;
  box-shadow: 0 0 0 0.25rem rgba(231, 76, 60, 0.25);
}

.invalid-feedback,
.text-danger {
  color: #e74c3c;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Alert styling */
.alert {
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

.alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

/* Thêm vào file CheckupList.css */

/* Xóa những class liên quan đến add/create button */
.add-btn {
  background-color: #27ae60;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: background-color 0.2s;
}

.add-btn:hover {
  background-color: #219653;
}

/* Xóa những class cho create checkup modal */
.add-checkup-btn {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.create-checkup-modal {
  max-width: 90% !important;
  margin: 30px auto !important;
}

.create-checkup-modal .modal-content {
  background-color: #ffffff !important;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0,0,0,.5);
  border: 1px solid rgba(0,0,0,.2);
}

.create-checkup-modal-body {
  padding: 20px !important;
  background-color: #ffffff !important;
}

/* Xóa class cho form thêm mới */
.checkup-form-container {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  margin: 20px 0;
  position: relative;
  animation: slideDown 0.3s ease-out;
  border: 1px solid #e0e0e0;
}

.checkup-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eaeaea;
  border-radius: 8px 8px 0 0;
}

.checkup-form-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: #777;
  cursor: pointer;
  font-size: 18px;
  padding: 5px;
  transition: all 0.2s;
}

.close-btn:hover {
  color: #e74c3c;
  transform: scale(1.1);
}

.checkup-form {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

/* Stats section - Chia thành 2 phần riêng biệt */

/* Phần thống kê số liệu - 2 hàng x 2 cột */
.campaign-stats-numbers {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin: 12px 0 8px 0;
}

/* Phần thông tin thời gian - 1 hàng x 2 cột */
.campaign-stats-dates {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin: 8px 0 16px 0;
}

.stat-box {
  padding: 10px 8px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  background: #f8f9fa;
  text-align: center;
  border-left: 3px solid #007bff;
  height: 70px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 70px;
  max-height: 70px;
  overflow: hidden;
}

.stat-box .stat-number {
  display: block;
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
  line-height: 1.1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.stat-box .stat-label {
  font-size: 0.72rem;
  color: #6c757d;
  font-weight: 500;
  line-height: 1.1;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.stat-box.success {
  border-left-color: #28a745;
}

.stat-box.info {
  border-left-color: #17a2b8;
}

.stat-box.warning {
  border-left-color: #ffc107;
}

.stat-box.follow-up {
  border-left-color: #6f42c1;
}

.stat-box.date {
  border-left-color: #17a2b8;
}

.stat-box.deadline {
  border-left-color: #fd7e14;
}

/* Campaign status and action section - Fixed position at bottom */
.campaign-bottom {
  margin-top: auto;
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.campaign-status-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  margin-bottom: 12px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #6c757d;
  min-height: 35px;
}

.campaign-status-info .status-icon {
  color: #6c757d;
  font-size: 0.9rem;
}

.campaign-status-info .status-label {
  color: #6c757d;
  font-weight: 500;
  font-size: 0.8rem;
}

.campaign-status-info .status-value {
  color: #2c3e50;
  font-weight: 600;
  font-size: 0.8rem;
}

.action-button {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border: none;
  color: white;
  padding: 12px 16px;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.action-button:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

/* Responsive adjustments for stats */
@media (max-width: 768px) {
  .campaign-stats-numbers {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
    margin: 10px 0 6px 0;
  }
  
  .campaign-stats-dates {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
    margin: 6px 0 12px 0;
  }
  
  .stat-box {
    height: 65px;
    min-height: 65px;
    max-height: 65px;
  }
  
  .stat-box .stat-number {
    font-size: 1.1rem;
  }
  
  .stat-box .stat-label {
    font-size: 0.7rem;
  }
  
  .campaign-bottom {
    min-height: 75px;
  }
  
  .campaign-status-info {
    min-height: 32px;
    padding: 8px;
  }
  
  .action-button {
    height: 38px;
    font-size: 0.8rem;
  }
}

/* Campaign Details Section - hiển thị trực tiếp trên trang */
.campaign-details-section {
  margin-top: 32px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e5e7eb;
}

.details-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.details-header .btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.details-header .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.details-content {
  min-height: 200px;
}

.students-table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.students-table-container .table {
  margin-bottom: 0;
  border-radius: 8px;
  overflow: hidden;
}

.students-table-container .table thead th {
  background: #f8f9fa;
  color: #495057;
  font-weight: 600;
  border-bottom: 2px solid #dee2e6;
  padding: 12px;
  font-size: 0.9rem;
}

.students-table-container .table tbody tr {
  transition: background-color 0.2s ease;
}

.students-table-container .table tbody tr:hover {
  background-color: #f8f9fa;
}

.students-table-container .table tbody td {
  padding: 12px;
  vertical-align: middle;
  border-bottom: 1px solid #dee2e6;
}

.students-table-container .table tbody td:last-child {
  text-align: center;
}

.students-table-container .btn {
  padding: 6px 12px;
  font-size: 0.8rem;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.students-table-container .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive cho details section */
@media (max-width: 768px) {
  .campaign-details-section {
    margin-top: 24px;
    padding: 16px;
  }
  
  .details-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .details-header h3 {
    font-size: 1.25rem;
  }
  
  .details-header .btn {
    align-self: flex-end;
  }
  
  .students-table-container {
    overflow-x: auto;
  }
}

/* CSS cho trạng thái checkup */
.checkup-status-badge {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

.checkup-status-badge .me-1 {
  margin-right: 4px !important;
}

/* Responsive cho các badge */
@media (max-width: 768px) {
  .checkup-status-badge {
    font-size: 0.7rem;
    padding: 4px 8px;
  }
}

/* Student Filter Section */
.student-filters {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
  margin-bottom: 20px;
}

.student-filters .form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
}

.student-filters .form-control {
  border: 1px solid #ced4da;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.student-filters .form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.student-filters .form-control::placeholder {
  color: #6c757d;
  font-style: italic;
}

/* Responsive cho student filters */
@media (max-width: 768px) {
  .student-filters {
    padding: 15px;
  }
  
  .student-filters .form-control {
    margin-bottom: 10px;
  }
}

/* Header controls container */
.header-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.status-filter {
  min-width: 250px;
}

.status-filter-select {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  font-size: 0.875rem;
  height: 38px;
  background-color: transparent;
  padding: 6px 12px;
}

.status-filter-select:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
  background-color: transparent;
}

/* Remove any white background or borders from filter container */
.status-filter,
.status-filter > *,
.status-filter-select,
.filter-actions,
.filter-actions > * {
  background: none !important;
  border: none;
  box-shadow: none !important;
}

/* Re-apply necessary styles for status filter select */
.status-filter-select {
  border: 1px solid #d1d5db !important;
  border-radius: 8px;
  font-size: 0.875rem;
  height: 38px;
  background-color: transparent !important;
  padding: 6px 12px;
  min-width: 250px;
}

.status-filter-select:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25) !important;
  background-color: transparent !important;
}

/* Re-apply necessary styles for reset button */
.filter-actions .btn {
  border: none !important;
  box-shadow: none !important;
  padding: 6px 16px;
  font-size: 0.875rem;
  background: #007bff !important;
  color: white !important;
  border-radius: 6px;
}

.filter-actions .btn:focus {
  box-shadow: none !important;
  background: #0056b3 !important;
}

.filter-actions .btn:hover {
  background: #0056b3 !important;
  border: none !important;
  box-shadow: none !important;
}

/* Header title section with reset button */
.header-title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.header-title-section h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.875rem;
  font-weight: 700;
}

.header-reset-action {
  display: flex;
  align-items: center;
}

.header-reset-action .btn {
  border: none;
  box-shadow: none;
  padding: 8px 16px;
  font-size: 0.875rem;
  background: #007bff;
  color: white;
  border-radius: 6px;
  font-weight: 500;
}

.header-reset-action .btn:hover {
  background: #0056b3;
  border: none;
  box-shadow: none;
}

.header-reset-action .btn:focus {
  box-shadow: none;
  background: #0056b3;
}

/* Status filter with white background */
.status-filter {
  min-width: 250px;
  background-color: #fff !important;
  border: 1px solid #ced4da !important;
  border-radius: 8px;
  padding: 2px;
}

.status-filter-select {
  border: none !important;
  border-radius: 6px;
  font-size: 0.875rem;
  height: 36px;
  background-color: #fff !important;
  padding: 6px 12px;
}

.status-filter-select:focus {
  border: none !important;
  box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25) !important;
  background-color: #fff !important;
}

/* Fix dropdown arrow for Form.Select */
.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right 0.75rem center !important;
  background-size: 16px 12px !important;
  padding-right: 2.25rem !important;
}

.form-select:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23007bff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
}

/* Ensure dropdown arrow is visible on all form selects */
.form-select,
.form-control[type="select"],
select.form-control {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right 0.75rem center !important;
  background-size: 16px 12px !important;
  padding-right: 2.25rem !important;
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
}

/* Responsive design for header */
@media (max-width: 768px) {
  .header-title-section {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .header-reset-action {
    align-self: flex-end;
  }
  
  .filter-row {
    flex-direction: column;
    gap: 10px;
  }
  
  .status-filter,
  .search-container {
    width: 100%;
    max-width: none;
  }
}
