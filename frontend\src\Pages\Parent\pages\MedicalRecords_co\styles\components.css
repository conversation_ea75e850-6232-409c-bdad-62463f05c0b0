/* Keyframes Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ================================================
   COMPONENTS - MINIMALIST MODERN
   Simple Cards • Clean Buttons • Minimal Effects
   Updated: Tab Navigation Auto-Width Design
   ================================================ */

/* Tab Navigation - Auto Width Design */
.tab-navigation {
  display: flex !important;
  justify-content: center !important;
  width: 100% !important;
  max-width: 770px !important;
  margin: 0 auto !important;
  padding: 0 var(--space-4) !important;
}

.tab-nav-list {
  display: inline-flex !important;
  gap: var(--space-1) !important;
  padding: var(--space-2) !important;
  background-color: var(--color-gray-100) !important;
  border-radius: var(--radius-lg) !important;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  width: auto !important;
  max-width: 100% !important;
  min-width: fit-content !important;
  flex-wrap: nowrap !important;
}

.tab-nav-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background-color: transparent;
  border: none;
  border-radius: var(--radius-md);
  color: var(--color-gray-600);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition);
  white-space: nowrap;
  min-width: 0;
  min-height: 44px;
}

.tab-nav-button:hover {
  background-color: var(--color-white);
  color: var(--color-gray-700);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-nav-button.active {
  background-color: var(--color-primary);
  color: var(--color-white);
  box-shadow: 0 4px 12px rgba(1, 92, 146, 0.3);
  transform: translateY(-1px);
}

.tab-nav-button.active:hover {
  background-color: var(--color-primary-hover);
  color: var(--color-white);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(1, 92, 146, 0.4);
}

.tab-nav-icon {
  font-size: var(--text-lg);
  flex-shrink: 0;
}

.tab-nav-text {
  font-weight: inherit;
  font-size: var(--text-sm);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Fixed Action Buttons - Back Button & Refresh Button */

.back-btn:hover {
  background-color: var(--color-gray-50);
  border-color: var(--color-gray-400);
  color: var(--color-gray-800);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.back-btn svg {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}



/* Stats Grid & Cards */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.stat-card {
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-4);
  transition: all var(--transition);
  box-shadow: var(--shadow-sm);
}

.stat-card:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  font-size: var(--text-lg);
  flex-shrink: 0;
}

.stat-icon.height { 
  background-color: var(--color-success);
}
.stat-icon.weight { 
  background-color: var(--color-primary);
}
.stat-icon.bmi { 
  background-color: var(--color-warning);
}
.stat-icon.blood { 
  background-color: var(--color-error);
}

.stat-content {
  flex: 1;
}

.stat-content h3 {
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-1);
}

.stat-content .value {
  font-size: var(--text-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: var(--space-1);
  line-height: 1;
}

.stat-content .unit {
  font-size: var(--text-sm);
  color: var(--color-gray-500);
}

/* Info Grid & Cards */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
}

.info-card {
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  transition: all var(--transition);
  box-shadow: var(--shadow-sm);
}

.info-card:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
}

.info-card-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
  border-bottom: 1px solid var(--color-gray-200);
  padding-bottom: var(--space-3);
}

.info-card-icon {
  width: 20px;
  height: 20px;
  color: var(--color-primary);
  flex-shrink: 0;
}

.info-card-title {
  font-size: var(--text-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin: 0;
}

.info-card-content {
  color: var(--color-gray-600);
  font-size: var(--text-sm);
  line-height: 1.6;
  margin-top: var(--space-4);
  display: block;
  width: 100%;
}

/* Special info card variations */
.info-card.emergency-info {
  border-color: var(--color-error);
  background-color: #fef2f2;
}

.info-card.emergency-info .info-card-icon {
  color: var(--color-error);
}

/* Vision specific styling */
.vision-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.vision-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3);
  background-color: var(--color-gray-50);
  border-radius: var(--radius-md);
}

.vision-label {
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
}

.vision-value {
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
}

/* Vaccination Cards - Compact Design */
.vaccinations-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.vaccination-card {
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  cursor: pointer;
  transition: all var(--transition);
  box-shadow: var(--shadow-sm);
}

.vaccination-card:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.vaccination-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.vaccination-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex: 1;
}

.vaccination-title svg {
  color: var(--color-primary);
  font-size: var(--text-lg);
  flex-shrink: 0;
}

.vaccination-title h4 {
  font-size: var(--text-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin: 0;
  line-height: 1.4;
}

.vaccination-date {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--color-gray-500);
  font-size: var(--text-sm);
  white-space: nowrap;
}

.vaccination-date svg {
  font-size: var(--text-xs);
}

/* Hide content and footer for compact design */
.vaccination-content {
  display: none;
}

.vaccination-footer {
  display: none;
}

/* Incidents Cards - Enhanced Design */
.incidents-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.incident-card {
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  cursor: pointer;
  transition: all var(--transition);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.incident-card::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--color-gray-300);
  transition: all var(--transition);
}

.incident-card.high::before {
  background-color: var(--color-error);
}

.incident-card.medium::before {
  background-color: var(--color-warning);
}

.incident-card.low::before {
  background-color: var(--color-success);
}

.incident-card:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.incident-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.incident-type {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex: 1;
}

.incident-type h4 {
  font-size: var(--text-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin: 0;
  line-height: 1.4;
}

.incident-date {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--color-gray-500);
  font-size: var(--text-sm);
  white-space: nowrap;
}

.incident-date svg {
  font-size: var(--text-xs);
}

/* Enhanced Severity Tags */
.severity-tag {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  white-space: nowrap;
}

.severity-tag.high {
  background-color: #fef2f2;
  color: var(--color-error);
  border: 1px solid #fecaca;
}

.severity-tag.medium {
  background-color: #fef3c7;
  color: var(--color-warning);
  border: 1px solid #fed7aa;
}

.severity-tag.low {
  background-color: #f0f9ff;
  color: #0284c7;
  border: 1px solid #bae6fd;
}

.severity-tag.unknown {
  background-color: var(--color-gray-100);
  color: var(--color-gray-600);
  border: 1px solid var(--color-gray-200);
}

/* No Data States */
.no-incidents {
  text-align: center;
  padding: var(--space-12);
  background-color: var(--color-gray-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-gray-200);
}

.no-incidents svg {
  font-size: var(--text-3xl);
  color: var(--color-gray-400);
  margin-bottom: var(--space-4);
}

.no-incidents h4 {
  color: var(--color-gray-700);
  margin-bottom: var(--space-2);
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
}

.no-incidents .positive-note {
  color: var(--color-green-600);
  font-weight: var(--font-weight-medium);
  font-style: italic;
  margin-top: var(--space-2);
}

.no-incidents p {
  color: var(--color-gray-500);
  margin: 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-complete {
  background-color: #dcfce7;
  color: #166534;
}

.status-updating {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-incomplete {
  background-color: #fed7d7;
  color: #c53030;
}

.status-unknown {
  background-color: var(--color-gray-100);
  color: var(--color-gray-600);
}

/* Buttons */
.btn-primary {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  background-color: var(--color-primary);
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition);
  text-decoration: none;
}

.btn-primary:hover {
  background-color: var(--color-primary-hover);
  color: var(--color-white);
}

.refresh-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  background-color: var(--color-white);
  color: var(--color-gray-600);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--transition);
}

.refresh-btn:hover {
  background-color: var(--color-gray-50);
  border-color: var(--color-gray-400);
}

/* Loading & Error States */
.data-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  text-align: center;
  background-color: #ffffff;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.loading-spinner.small {
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.data-loading p {
  color: #6b7280;
  margin: 0;
  font-size: 14px;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  font-size: 14px;
  margin-bottom: 24px;
}

.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  text-align: center;
  background-color: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.no-data-message h4 {
  color: #374151;
  margin-bottom: 8px;
  font-size: 18px;
  font-weight: 600;
}

.no-data-message p {
  color: #6b7280;
  margin-bottom: 4px;
  font-size: 14px;
  line-height: 1.5;
  max-width: 400px;
}

/* Panels */
.vaccinations-panel,
.checkups-panel,
.incidents-panel {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.vaccinations-panel h3,
.checkups-panel h3,
.incidents-panel h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 24px;
  text-align: center;
  letter-spacing: -0.01em;
}

/* Update Info */
.last-update-info {
  background-color: var(--color-gray-50);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  margin-bottom: var(--space-6);
  text-align: center;
}

.last-update-info p {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--color-gray-600);
}

.last-update-info p:last-child {
  margin-top: var(--space-2);
}

/* Merge indicator styles */
.merge-indicator {
  color: var(--color-primary);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
  font-style: italic;
}

.last-update-info .merge-indicator {
  background-color: var(--color-primary-light);
  color: var(--color-primary-dark);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-left: var(--space-2);
}

/* Checkups Lists - Enhanced Design */
.checkups-list-simple {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.checkup-row {
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  cursor: pointer;
  transition: all var(--transition);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
}

.checkup-row::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--color-primary);
  transition: all var(--transition);
}

.checkup-row:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.checkup-row:hover::before {
  background-color: var(--color-primary-hover);
}

.checkup-row-content {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  flex: 1;
}

.checkup-date {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--color-gray-500);
  font-size: var(--text-sm);
  white-space: nowrap;
  min-width: 120px;
}

.checkup-date .date-icon {
  font-size: var(--text-xs);
  color: var(--color-primary);
  flex-shrink: 0;
}

.checkup-type {
  flex: 1;
}

.checkup-type span {
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-900);
  font-size: var(--text-base);
  line-height: 1.4;
}

.arrow-icon {
  color: var(--color-gray-400);
  transition: all var(--transition);
  margin-left: var(--space-2);
  font-size: var(--text-sm);
  flex-shrink: 0;
}

.checkup-row:hover .arrow-icon {
  transform: translateX(4px);
  color: var(--color-primary);
}

/* No Checkups Data State */
.checkups-panel .no-data-message {
  text-align: center;
  padding: var(--space-12);
  background-color: var(--color-gray-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-gray-200);
}

.checkups-panel .no-data-message svg {
  font-size: var(--text-3xl);
  color: var(--color-primary);
  margin-bottom: var(--space-4);
}

.checkups-panel .no-data-message h4 {
  color: var(--color-gray-700);
  margin-bottom: var(--space-2);
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
}

.checkups-panel .no-data-message p {
  color: var(--color-gray-500);
  margin-bottom: var(--space-2);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.checkups-panel .no-data-message p:last-child {
  margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .tab-nav-button {
    padding: var(--space-3) var(--space-3);
    font-size: var(--text-sm);
    gap: var(--space-1);
  }
  
  .tab-nav-icon {
    font-size: var(--text-base);
  }
  
  .tab-nav-text {
    font-size: var(--text-sm);
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
  }
  
  .info-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .tab-nav-list {
    padding: var(--space-2);
    gap: var(--space-1);
    width: auto;
    max-width: 100%;
  }
  
  .tab-nav-button {
    padding: var(--space-2) var(--space-2);
    font-size: var(--text-xs);
    gap: var(--space-1);
    min-height: 40px;
    flex: 1;
  }
  
  .tab-nav-icon {
    font-size: var(--text-base);
  }
  
  .tab-nav-text {
    font-size: var(--text-xs);
  }
  
  .vaccination-header,
  .incident-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
  
  .vaccination-title,
  .incident-type {
    width: 100%;
  }
  
  .vaccination-date,
  .incident-date {
    align-self: flex-end;
  }
  
  .checkup-row {
    padding: var(--space-3);
  }
  
  .checkup-row-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
  
  .checkup-date {
    align-self: flex-start;
    min-width: auto;
    order: 2;
  }
  
  .checkup-type {
    order: 1;
    width: 100%;
  }
  
  .arrow-icon {
    position: absolute;
    right: var(--space-3);
    top: 50%;
    transform: translateY(-50%);
  }
  
  .checkup-row:hover .arrow-icon {
    transform: translateY(-50%) translateX(4px);
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }
  
  .stat-card {
    padding: var(--space-4);
    flex-direction: column;
    text-align: center;
    gap: var(--space-3);
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: var(--text-base);
  }
  
  .tab-nav-list {
    padding: var(--space-1);
    gap: 2px;
    width: 100%;
  }
  
  .tab-nav-button {
    padding: var(--space-2) 1px;
    font-size: 9px;
    gap: 2px;
    flex-direction: column;
    min-height: 52px;
    flex: 1;
  }
  
  .tab-nav-icon {
    font-size: var(--text-sm);
  }
  
  .tab-nav-text {
    font-size: 9px;
    line-height: 1;
    text-align: center;
    max-width: 100%;
    word-break: break-word;
    hyphens: auto;
  }
}

/* ================================================
   MODERN MODAL SYSTEM - SIMPLE & ELEGANT
   Clean Design • Responsive • Consistent
   ================================================ */

/* Modal Overlay */
.modern-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.2s ease-out;
}

/* Modal Content */
.modern-modal-content {
  background: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 600px;
  max-height: 90vh;
  width: 100%;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* Modal Header */
.modern-modal-header {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-hover) 100%);
  color: var(--color-white);
  padding: var(--space-4) var(--space-6);
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.modern-modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
  opacity: 0.5;
}

.modal-header-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-header-left {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.modal-header-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xl);
  flex-shrink: 0;
}

.modal-header-text h2 {
  font-size: var(--text-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-white);
  margin: 0 0 var(--space-1) 0;
  line-height: 1.3;
}

.modal-header-text p {
  font-size: var(--text-sm);
  color: var(--color-white);
  opacity: 0.9;
  margin: 0;
  line-height: 1.4;
}

.modal-close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: var(--color-white);
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition);
  font-size: var(--text-base);
}

.modal-close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

/* Modal Body */
.modern-modal-body {
  padding: var(--space-4) var(--space-6);
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--color-gray-300) transparent;
  flex: 1;
  min-height: 0;
}

.modern-modal-body::-webkit-scrollbar {
  width: 6px;
}

.modern-modal-body::-webkit-scrollbar-track {
  background: transparent;
}

.modern-modal-body::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: var(--radius-sm);
}

.modern-modal-body::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-400);
}

/* Content Sections */
.modal-section {
  margin-bottom: var(--space-6);
}

.modal-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin-bottom: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.section-title svg {
  color: var(--color-primary);
  font-size: var(--text-base);
}

/* Info Cards */
.info-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.info-card-simple {
  background: var(--color-gray-50);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  transition: all var(--transition);
}

.info-card-simple:hover {
  border-color: var(--color-primary);
  background: var(--color-white);
  box-shadow: var(--shadow-sm);
}

.info-card-header {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-2);
}

.info-card-icon {
  color: var(--color-primary);
  font-size: var(--text-sm);
}

.info-card-label {
  font-size: var(--text-xs);
  color: var(--color-gray-500);
  text-transform: uppercase;
  font-weight: var(--font-weight-medium);
  letter-spacing: 0.05em;
}

.info-card-value {
  font-size: var(--text-base);
  color: var(--color-gray-900);
  font-weight: var(--font-weight-medium);
  line-height: 1.4;
}

/* Content Card */
.content-card {
  background: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
}

.content-card-title {
  font-size: var(--text-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin-bottom: var(--space-3);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.content-card-title svg {
  color: var(--color-primary);
  font-size: var(--text-sm);
}

.content-card-text {
  color: var(--color-gray-600);
  line-height: 1.6;
  margin: 0;
}

/* Status Badge */
.status-badge-simple {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge-simple.success {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.status-badge-simple.warning {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fed7aa;
}

.status-badge-simple.error {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.status-badge-simple.info {
  background: #dbeafe;
  color: #1e40af;
  border: 1px solid #bfdbfe;
}

/* Loading State */
.modal-loading {
  text-align: center;
  padding: var(--space-12);
}

.modal-loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-gray-200);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--space-4);
}

.modal-loading h3 {
  color: var(--color-gray-700);
  margin-bottom: var(--space-2);
  font-size: var(--text-lg);
}

.modal-loading p {
  color: var(--color-gray-500);
  margin: 0;
}

/* Error State */
.modal-error {
  text-align: center;
  padding: var(--space-8);
}

.modal-error-icon {
  font-size: var(--text-3xl);
  color: var(--color-error);
  margin-bottom: var(--space-4);
}

.modal-error h3 {
  color: var(--color-gray-700);
  margin-bottom: var(--space-2);
}

.modal-error p {
  color: var(--color-gray-500);
  margin-bottom: var(--space-4);
}

.modal-error-btn {
  background: var(--color-primary);
  color: var(--color-white);
  border: none;
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: background var(--transition);
}

.modal-error-btn:hover {
  background: var(--color-primary-hover);
  color: var(--color-white);
}

/* Image Display */
.modal-image-container {
  margin: var(--space-4) 0;
}

.modal-image {
  width: 100%;
  max-height: 300px;
  object-fit: cover;
  border-radius: var(--radius-md);
  border: 1px solid var(--color-gray-200);
}

/* ================================================
   GROWTH TAB - CHART COMPONENTS
   Beautiful Growth Charts with Bitcoin-style Lines
   ================================================ */

.growth-panel {
  padding: var(--space-6) var(--space-4);
  background: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.growth-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-6);
  flex-wrap: wrap;
  gap: var(--space-4);
}

.growth-title-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.growth-title-section h3 {
  font-size: var(--text-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin: 0;
}

.last-updated {
  font-size: var(--text-xs);
  color: var(--color-gray-500);
  font-weight: var(--font-weight-medium);
}

.growth-controls {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex-wrap: wrap;
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: linear-gradient(135deg, var(--color-primary), #4f46e5);
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition);
  box-shadow: 0 1px 3px rgba(99, 102, 241, 0.2);
}

.refresh-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #4f46e5, #4338ca);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.refresh-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.refresh-btn .rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Chart Controls */
.chart-controls {
  display: flex;
  gap: var(--space-2);
  background: var(--color-gray-100);
  padding: var(--space-1);
  border-radius: var(--radius-md);
}

.chart-control-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: transparent;
  border: none;
  border-radius: var(--radius-sm);
  color: var(--color-gray-600);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition);
  white-space: nowrap;
}

.chart-control-btn:hover {
  background: var(--color-white);
  color: var(--color-gray-900);
  box-shadow: var(--shadow-sm);
}

.chart-control-btn.active {
  background: var(--color-white);
  color: var(--color-primary);
  box-shadow: var(--shadow-sm);
  font-weight: var(--font-weight-semibold);
}

.growth-chart-container {
  margin-bottom: var(--space-6);
  background: var(--color-white);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
}

.growth-chart-svg {
  width: 100%;
  height: auto;
  font-family: var(--font-mono);
}

.chart-dot {
  cursor: pointer;
  transition: all 0.2s ease;
}

.chart-dot:hover {
  r: 7;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* Bar Chart Styles */
.chart-bar {
  cursor: pointer;
  transition: all 0.3s ease;
}

.chart-bar:hover {
  filter: brightness(1.1) drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
  transform: translateY(-1px);
}

/* Value labels on bars */
.growth-chart-svg text {
  font-family: var(--font-mono);
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

/* Responsive chart adjustments */
@media (max-width: 768px) {
  .growth-chart-svg text {
    font-size: 10px;
  }

  .chart-bar {
    stroke-width: 0.5;
  }

  .chart-dot {
    r: 3;
  }

  .chart-dot:hover {
    r: 5;
  }
}

/* Growth Statistics */
.growth-stats {
  margin-top: var(--space-6);
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-4);
}

.stat-card {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-4);
  background: linear-gradient(135deg, var(--color-white) 0%, #f8fafc 100%);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-gray-200);
  transition: all var(--transition);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
  color: var(--color-white);
  flex-shrink: 0;
}

.stat-icon.height {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
}

.stat-icon.weight {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
}

.stat-icon.checkups {
  background: linear-gradient(135deg, #059669, #047857);
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--color-gray-600);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-1);
}

.stat-value {
  font-size: var(--text-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: var(--space-1);
}

.stat-change {
  font-size: var(--text-xs);
  color: var(--color-gray-500);
  font-weight: var(--font-weight-medium);
}

/* Checkups Tab Styles */
.checkups-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-6);
  flex-wrap: wrap;
  gap: var(--space-4);
}

.checkups-title-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.checkups-title-section h3 {
  font-size: var(--text-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin: 0;
}

.checkups-controls {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex-wrap: wrap;
}

.checkups-controls .refresh-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background-color: var(--color-primary);
  color: var(--color-white);
  border: 1px solid var(--color-primary);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition);
}

.checkups-controls .refresh-btn:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.checkups-controls .refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Vaccinations Tab Styles */
.vaccinations-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-6);
  flex-wrap: wrap;
  gap: var(--space-4);
}

.vaccinations-title-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.vaccinations-title-section h3 {
  font-size: var(--text-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin: 0;
}

.vaccinations-controls {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex-wrap: wrap;
}

.vaccinations-controls .refresh-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background-color: var(--color-primary);
  color: var(--color-white);
  border: 1px solid var(--color-primary);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition);
}

.vaccinations-controls .refresh-btn:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.vaccinations-controls .refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.vaccinations-controls .refresh-btn.refreshing {
  background-color: var(--color-gray-400);
  border-color: var(--color-gray-400);
}

.vaccinations-controls .refresh-btn .spin {
  animation: spin 1s linear infinite;
}

/* Incidents Tab Styles */
.incidents-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-6);
  flex-wrap: wrap;
  gap: var(--space-4);
}

.incidents-title-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.incidents-title-section h3 {
  font-size: var(--text-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin: 0;
}

.incidents-controls {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex-wrap: wrap;
}

.incidents-controls .refresh-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background-color: var(--color-primary);
  color: var(--color-white);
  border: 1px solid var(--color-primary);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition);
}

.incidents-controls .refresh-btn:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.incidents-controls .refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Responsive Styles for Tab Headers */
@media (max-width: 768px) {
  .growth-header,
  .checkups-header,
  .vaccinations-header,
  .incidents-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
  }

  .growth-controls,
  .checkups-controls,
  .vaccinations-controls,
  .incidents-controls {
    justify-content: flex-end;
    margin-top: var(--space-2);
  }
}

/* ================================================
   SORT BUTTON STYLING
   ================================================ */

.sort-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background-color: var(--color-white);
  color: var(--color-gray-600);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition);
  white-space: nowrap;
}

.sort-btn:hover {
  background-color: var(--color-gray-50);
  border-color: var(--color-gray-400);
  color: var(--color-gray-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.sort-btn:active {
  transform: translateY(0);
  background-color: var(--color-blue-50);
  border-color: var(--color-blue-300);
  color: var(--color-blue-700);
}

.sort-btn.active {
  background-color: var(--color-blue-100);
  border-color: var(--color-blue-400);
  color: var(--color-blue-800);
  font-weight: 600;
}

.sort-btn svg {
  font-size: var(--text-base);
  flex-shrink: 0;
}

/* Sort indicator styling */
.sort-indicator {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 20px;
  padding: 2px 6px;
  background-color: var(--color-blue-100);
  color: var(--color-blue-700);
  font-size: var(--text-xs);
  font-weight: 600;
  border-radius: var(--radius-full);
  margin-left: var(--space-2);
  border: 1px solid var(--color-blue-200);
  animation: sortIndicatorFadeIn 0.3s ease-in-out;
}

/* Animation for sort changes */
@keyframes sortIndicatorFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Add animation to list items when sorting */
.incident-card,
.vaccination-plan-card,
.vaccination-row {
  animation: listItemSlideIn 0.4s ease-out;
}

@keyframes listItemSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-10px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Sort notification styling */
.sort-notification {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background-color: var(--color-green-50);
  color: var(--color-green-700);
  border: 1px solid var(--color-green-200);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: 500;
  margin-bottom: var(--space-3);
  animation: notificationSlideDown 0.3s ease-out;
}

.sort-notification .notification-icon {
  font-size: var(--text-base);
  animation: spin 1s linear infinite;
}

@keyframes notificationSlideDown {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Section controls styling for VaccinationsTab */
.section-controls {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.section-title {
  flex: 1;
}

/* History header styling for VaccinationsTab */
.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
  gap: var(--space-3);
  flex-wrap: wrap;
}

.history-header h4 {
  margin: 0;
  flex: 1;
}

/* Responsive design for sort buttons */
@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
  }

  .section-controls {
    justify-content: flex-end;
  }

  .history-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-2);
  }

  .sort-btn {
    align-self: flex-end;
    width: auto;
  }
}

/* ================================================
   VACCINATION SUB-TABS STYLING
   ================================================ */

/* Sub-tab navigation */
.vaccination-sub-tabs {
  display: flex;
  gap: var(--space-1);
  margin-bottom: var(--space-6);
  padding: var(--space-2);
  background-color: var(--color-gray-100);
  border-radius: var(--radius-lg);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.sub-tab-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background-color: transparent;
  border: none;
  border-radius: var(--radius-md);
  color: var(--color-gray-600);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition);
  white-space: nowrap;
  flex: 1;
  justify-content: center;
}

.sub-tab-btn:hover {
  background-color: var(--color-white);
  color: var(--color-gray-800);
  box-shadow: var(--shadow-sm);
}

.sub-tab-btn.active {
  background-color: var(--color-primary);
  color: var(--color-white);
  box-shadow: var(--shadow-md);
}

.sub-tab-btn svg {
  font-size: var(--text-base);
}

/* Section headers */
.section-header {
  text-align: center;
  margin-bottom: 24px;
  padding: 20px;
  background: #f9fafb;
  border-radius: 10px;
  border: 1px solid #e5e7eb;
}

.section-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  letter-spacing: -0.01em;
}

.section-header p {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
  line-height: 1.4;
}

/* ================================================
   VACCINATION PLANS STYLING - MODERN CLEAN DESIGN
   ================================================ */

.vaccination-plans-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.vaccination-plan-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;
  transition: box-shadow 0.2s ease, border-color 0.2s ease;
}

.vaccination-plan-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6 0%, #1e40af 100%);
}

.vaccination-plan-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  border-color: #d1d5db;
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 18px;
  flex-wrap: wrap;
  gap: 16px;
}

.plan-title {
  display: flex;
  align-items: flex-start;
  gap: 14px;
  flex: 1;
}

.plan-icon {
  color: #3b82f6;
  font-size: 20px;
  margin-top: 2px;
  flex-shrink: 0;
  background: #b0bedf !important;
  padding: 10px;
  border-radius: 10px;
}

.plan-title h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 6px 0;
  line-height: 1.3;
  letter-spacing: -0.01em;
}

.plan-description {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
  line-height: 1.4;
  font-weight: 400;
}

.plan-date {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  font-size: 13px;
  white-space: nowrap;
  background: #f9fafb;
  padding: 8px 12px;
  border-radius: 8px;
  font-weight: 500;
  border: 1px solid #e5e7eb;
}

.plan-date svg {
  color: #3b82f6;
  font-size: 13px;
}

.plan-status {
  margin-bottom: 18px;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 14px;
  border-radius: 16px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid transparent;
}

.status-badge.completed {
  background: #d1fae5;
  color: #065f46;
  border-color: #a7f3d0;
}

.status-badge.pending {
  background: #fef3c7;
  color: #92400e;
  border-color: #fde68a;
}

.status-badge.waiting_parent {
  background: #dbeafe;
  color: #1e40af;
  border-color: #93c5fd;
}

.status-badge.accepted {
  background: #d1fae5;
  color: #065f46;
  border-color: #a7f3d0;
}

.status-badge.rejected {
  background: #fee2e2;
  color: #991b1b;
  border-color: #fecaca;
}

.status-badge.in_progress {
  background: #e0f2fe;
  color: #0277bd;
  border-color: #81d4fa;
}

.status-badge.canceled {
  background: #f3e5f5;
  color: #7b1fa2;
  border-color: #ce93d8;
}

/* ================================================
   VACCINES LIST STYLING - MODERN CLEAN DESIGN
   ================================================ */

.vaccines-list h5 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 14px 0;
  letter-spacing: -0.01em;
}

.vaccine-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 10px;
  margin-bottom: 10px;
  gap: 14px;
  transition: border-color 0.2s ease;
}

.vaccine-item:last-child {
  margin-bottom: 0;
}

.vaccine-item:hover {
  border-color: #d1d5db;
}

.vaccine-info {
  flex: 1;
}

.vaccine-info strong {
  display: block;
  font-size: 15px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
  letter-spacing: -0.01em;
}

.vaccine-info p {
  color: #64748b;
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
  font-weight: 400;
}

.vaccine-actions {
  display: flex;
  gap: var(--space-2);
  flex-shrink: 0;
}

.confirm-btn {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-2) var(--space-3);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition);
  min-width: 90px;
  justify-content: center;
}

.confirm-btn.accept {
  background-color: var(--color-green-600);
  color: var(--color-white);
}

.confirm-btn.accept:hover {
  background-color: var(--color-green-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.confirm-btn.reject {
  background-color: var(--color-red-600);
  color: var(--color-white);
}

.confirm-btn.reject:hover {
  background-color: var(--color-red-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.confirm-btn.open-modal {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.confirm-btn.open-modal:hover {
  background-color: var(--color-primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.confirm-btn svg {
  font-size: var(--text-xs);
}

/* ================================================
   RESPONSIVE DESIGN FOR VACCINATION TABS
   ================================================ */

@media (max-width: 768px) {
  .vaccination-sub-tabs {
    flex-direction: column;
    gap: 8px;
  }
  
  .sub-tab-btn {
    flex: none;
    justify-content: flex-start;
  }
  
  .plan-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .plan-date {
    align-self: flex-start;
  }
  
  .vaccine-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .vaccine-actions {
    justify-content: flex-end;
  }
  
  .confirm-btn {
    flex: 1;
    max-width: 120px;
  }
  
  .vaccine-confirm-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .vaccine-confirm-actions label {
    min-width: auto;
  }
  
  .section-header {
    padding: 16px;
  }
  
  .vaccination-plan-card {
    padding: 16px;
  }
  
  .plan-title {
    gap: 12px;
  }
  
  .plan-icon {
    padding: 8px;
  }
  
  /* Vaccination History Responsive */
  .profile-info-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .vaccination-history-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .vaccination-history-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .detail-item {
    flex-direction: column;
    gap: 4px;
  }
  
  .detail-item label {
    min-width: auto;
  }
  
  .vaccination-status-indicator {
    align-self: center;
  }
}

/* ================================================
   VACCINE CONFIRMATION FORM STYLING
   ================================================ */

.vaccine-confirmation-form {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 10px;
  padding: 20px;
  margin-top: 18px;
}

.vaccine-confirmation-form h5 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.confirmation-instruction {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #eff6ff;
  border-radius: 8px;
  border-left: 3px solid #3b82f6;
}

.confirmation-instruction small {
  color: #1e40af;
  font-size: 12px;
  line-height: 1.4;
}

.vaccine-confirm-item {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
}

.vaccine-confirm-item:last-child {
  margin-bottom: 16px;
}

.vaccine-confirm-info {
  margin-bottom: 12px;
}

.vaccine-confirm-info strong {
  display: block;
  font-size: 15px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.vaccine-confirm-info p {
  color: #6b7280;
  font-size: 13px;
  margin: 0;
  line-height: 1.4;
}

.vaccine-confirm-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.vaccine-confirm-actions label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 10px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background-color: #ffffff;
  transition: all 0.2s ease;
  min-width: 90px;
  justify-content: center;
}

.vaccine-confirm-actions label:hover {
  border-color: #3b82f6;
  background-color: #f0f9ff;
}

.vaccine-confirm-actions label.selected {
  border-color: #3b82f6;
  background-color: #dbeafe;
  color: #1e40af;
}

.vaccine-confirm-actions input[type="radio"] {
  margin: 0;
  width: 14px;
  height: 14px;
  accent-color: #3b82f6;
}

.radio-label {
  font-size: 13px;
  font-weight: 500;
}

.vaccine-notes {
  margin-top: 8px;
}

.vaccine-notes input {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 13px;
  transition: border-color 0.2s ease;
}

.vaccine-notes input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px #dbeafe;
}

/* Progress indicator styles */
.confirmation-progress {
  margin: 16px 0;
  padding: 12px;
  background-color: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
}

.progress-text {
  font-size: 13px;
  font-weight: 500;
  color: #1e40af;
  margin-bottom: 8px;
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background-color: #e0e7ff;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #3b82f6;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.loading-spinner.small {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Response badges for vaccine confirmation status */
.vaccine-response {
  margin-top: 8px;
}

.response-badge {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.response-badge.accepted {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.response-badge.rejected {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

.response-badge.pending {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

/* Completed vaccine confirmation items */
.vaccine-confirm-item.completed {
  background-color: #f0f9ff;
  border-left: 3px solid #10b981;
  opacity: 0.9;
}

.vaccine-confirm-item.completed .vaccine-confirm-info {
  position: relative;
}

/* Submit button styling */
.submit-confirmation-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  padding: 12px 20px;
  background-color: #3b82f6;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submit-confirmation-btn:hover {
  background-color: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.submit-confirmation-btn:active {
  transform: translateY(0);
}

.submit-confirmation-btn:disabled {
  background-color: #9ca3af;
  color: #ffffff;
  cursor: not-allowed;
  transform: none;
}

.submit-confirmation-btn:disabled:hover {
  background-color: #9ca3af;
  transform: none;
  box-shadow: none;
}

.submit-confirmation-btn.loading {
  background-color: #6b7280;
}

/* All confirmed message */
.all-confirmed-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px 20px;
  background-color: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  color: #1e40af;
  font-weight: 500;
  text-align: center;
}

.all-confirmed-message .success-icon {
  color: #10b981;
  font-size: 16px;
}

.all-confirmed-message p {
  margin: 0;
  font-size: 14px;
}

/* ================================================
   VACCINATION HISTORY STYLING
   ================================================ */

.history-section {
  margin-top: 24px;
}

.vaccination-history-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Health Profile Summary */
.health-profile-summary {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 10px;
  padding: 20px;
}

.health-profile-summary h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
  letter-spacing: -0.01em;
}

.profile-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.profile-info-item {
  display: flex;
  flex-direction: row;
  gap: 4px;
}

.profile-info-item label {
  font-size: 13px;
  font-weight: 500;
  color: #6b7280;
}

.profile-info-item span {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
}

.immunization-status.complete {
  color: #059669;
  background: #d1fae5;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  display: inline-block;
  max-width: 70px;
  text-align: center;
}

.immunization-status.incomplete {
  color: #dc2626;
  background: #fee2e2;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  display: inline-block;
}

/* Vaccination History List */
.vaccination-history-list h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
  letter-spacing: -0.01em;
}

.history-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.vaccination-history-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 10px;
  padding: 18px;
  transition: border-color 0.2s ease;
}

.vaccination-history-item:hover {
  border-color: #d1d5db;
}

.vaccination-history-item.pending {
  background: #fffbeb;
  border-color: #fbbf24;
}

.vaccination-history-item.completed {
  background: #ffffff;
  border-color: #e5e7eb;
}

.vaccination-history-info {
  flex: 1;
}

.vaccination-history-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  flex-wrap: wrap;
  gap: 8px;
}

.vaccination-history-header h5 {
  font-size: 15px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  letter-spacing: -0.01em;
}

.vaccination-type-badge {
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 13px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.vaccination-type-badge.school_plan {
  background: #dbeafe;
  color: #1e40af;
  border: 1px solid #93c5fd;
}

.vaccination-type-badge.parent_declared {
  background: #fef9c3;
  color: #b45309;
  border: 1px solid #fcd34d;
}

.vaccination-type-badge.individual {
  background: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.vaccination-type-badge.campaign {
  background: #f3e8ff;
  color: #7c3aed;
  border: 1px solid #d8b4fe;
}

.vaccination-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  gap: 8px;
  align-items: flex-start;
}

.detail-item label {
  font-size: 13px;
  font-weight: 500;
  color: #6b7280;
  min-width: 140px;
  flex-shrink: 0;
}

.detail-item span {
  font-size: 13px;
  color: #1f2937;
  line-height: 1.4;
}

.detail-item span.pending-text {
  color: #f59e0b;
  font-style: italic;
}

.next-dose-date {
  color: #059669;
  font-weight: 500;
}

.vaccination-status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  flex-shrink: 0;
}

.vaccination-status-indicator .completed-icon {
  color: #059669;
  background: #d1fae5;
  padding: 12px;
  border-radius: 50%;
  font-size: 16px;
}

.vaccination-status-indicator .pending-icon {
  color: #f59e0b;
  background: #fef3c7;
  padding: 12px;
  border-radius: 50%;
  font-size: 16px;
}

/* ================================================
   VACCINATION HISTORY ROWS - SIMPLE LIST DESIGN
   ================================================ */

.vaccination-history-rows {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  margin-top: var(--space-4);
}

.vaccination-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  background-color: white;
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.vaccination-row:hover {
  border-color: var(--color-blue-300);
  background-color: var(--color-blue-50);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.vaccination-row-content {
  display: flex;
  align-items: center;
  gap: var(--space-6);
  flex: 1;
}

.vaccination-row-main {
  display: flex;
  align-items: center;
  gap: var(--space-6);
  flex: 1;
}

.vaccination-dose {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  min-width: 100px;
}

.dose-icon {
  color: var(--color-blue-500);
  font-size: var(--text-sm);
}

.dose-text {
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-700);
  font-size: var(--text-sm);
}

.vaccination-name {
  flex: 1;
  min-width: 200px;
}

.vaccine-name {
  color: var(--color-gray-800);
  font-weight: var(--font-weight-medium);
  font-size: var(--text-sm);
  line-height: 1.4;
}

.vaccination-date {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  min-width: 140px;
}

.date-icon {
  color: var(--color-gray-400);
  font-size: var(--text-xs);
}

.date-text {
  color: var(--color-gray-600);
  font-size: var(--text-sm);
}

.vaccination-type {
  min-width: 120px;
}

.vaccination-type-badge-small {
  display: inline-block;
  padding: var(--space-1) var(--space-2);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.vaccination-type-badge-small.parent_declared {
  background-color: #FFF9C4; /* Vàng nhạt */
  color: #FBC02D;            /* Vàng đậm */
}

.vaccination-type-badge-small.school_plan {
  background-color: #BBDEFB; /* Xanh dương nhạt */
  color: #1976D2;            /* Xanh dương đậm */
}

.vaccination-type-badge-small.individual {
  background-color: #C8E6C9; /* Xanh lá nhạt */
  color: #388E3C;            /* Xanh lá đậm */
}

.vaccination-type-badge-small.campaign {
  background-color: #E1BEE7; /* Tím nhạt */
  color: #8E24AA;            /* Tím đậm */
}


.vaccination-row-status {
  display: flex;
  align-items: center;
  margin-right: var(--space-2);
}

.status-icon {
  font-size: var(--text-base);
}

.status-icon.completed {
  color: var(--color-green-500);
  color: #10b981 !important;
}

.status-icon.pending {
  color: var(--color-yellow-500);
}

.vaccination-row .arrow-icon {
  color: var(--color-gray-400);
  font-size: var(--text-sm);
  transition: all var(--transition);
}

.vaccination-row:hover .arrow-icon {
  color: var(--color-blue-500);
  transform: translateX(2px);
}

/* ================================================
   VACCINATION MODAL STYLES
   ================================================ */

.vaccination-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.vaccination-modal-content {
  background-color: white;
  border-radius: var(--radius-lg);
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Notes Section for Modal */
.notes-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.note-item {
  background-color: var(--color-gray-50);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  border-left: 4px solid var(--color-blue-500);
}

.note-header {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-2);
}

.note-icon {
  color: var(--color-blue-500);
  font-size: var(--text-sm);
}

.note-label {
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-700);
  font-size: var(--text-sm);
}

.note-content {
  color: var(--color-gray-600);
  font-size: var(--text-sm);
  line-height: 1.5;
}

.medical-note {
  background-color: var(--color-blue-50);
  padding: var(--space-3);
  border-radius: var(--radius-sm);
  border-left: 3px solid var(--color-blue-400);
}

.parent-note {
  background-color: var(--color-green-50);
  padding: var(--space-3);
  border-radius: var(--radius-sm);
  border-left: 3px solid var(--color-green-400);
}

.no-notes {
  background-color: var(--color-gray-50);
  padding: var(--space-4);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-gray-200);
  text-align: center;
}

.no-notes-text {
  color: var(--color-gray-500);
  font-style: italic;
  font-size: var(--text-sm);
}

/* Next dose date styling */
.next-dose-date {
  color: var(--color-orange-600);
  font-weight: var(--font-weight-medium);
}

.pending-text {
  color: var(--color-yellow-600);
  font-style: italic;
}

/* Status badges for modal */
.status-badge-simple {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-semibold);
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge-simple.success {
  background-color: var(--color-green-100);
  color: var(--color-green-700);
  background-color: rgb(96, 206, 18);
  color: #fff;
}

.status-badge-simple.warning {
  background-color: var(--color-yellow-100);
  color: var(--color-yellow-700);
  background-color: rgb(255, 211, 54);
  color: #484b03;
}

.status-badge-simple.error {
  background-color: var(--color-red-100);
  color: var(--color-red-700);
  background-color: rgb(217, 83, 38);
  color: ffff;
}

.status-badge-simple.info {
  background-color: var(--color-blue-100);
  color: var(--color-blue-700);
}

/* Responsive Design for Vaccination Rows */
@media (max-width: 768px) {
  .vaccination-row-main {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }

  .vaccination-dose,
  .vaccination-name,
  .vaccination-date,
  .vaccination-type {
    min-width: auto;
    width: 100%;
  }

  .vaccination-row-content {
    gap: var(--space-3);
  }
}

@media (max-width: 480px) {
  .vaccination-row {
    padding: var(--space-3);
  }

  .vaccination-modal-content {
    width: 95%;
    margin: var(--space-2);
  }
}

/* ================================================
   MEDICAL IMAGES GRID FOR INCIDENT MODAL
   ================================================ */

.medical-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-4);
  margin-top: var(--space-4);
}

.medical-image-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.image-label {
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-700);
  font-size: var(--text-sm);
  padding: var(--space-2) var(--space-3);
  background-color: var(--color-gray-100);
  border-radius: var(--radius-md);
  text-align: center;
}

.modal-image-container {
  position: relative;
  margin-top: 10px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.modal-image {
  width: 100%;
  max-height: 400px;
  object-fit: contain;
  cursor: zoom-in;
  background-color: #f8f9fa;
  transition: transform 0.3s ease;
  display: block;
  border-radius: 8px;
}

.modal-image:hover {
  transform: scale(1.05);
}

/* Incident Image Zoom Hint */
.incident-image-zoom-hint {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Incident Modal Zoom Overlay - Namespaced to avoid conflicts */
.incident-zoom-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999999999; /* Higher than modal to appear on top */
  padding: 20px;
  animation: incidentFadeIn 0.3s ease-in-out;
}

@keyframes incidentFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.incident-zoomed-image {
  width: 650px;
  height: 600px;
  object-fit: contain;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
  animation: incidentZoomIn 0.3s ease-in-out;
}

@keyframes incidentZoomIn {
  from { 
    opacity: 0;
    transform: scale(0.8);
  }
  to { 
    opacity: 1;
    transform: scale(1);
  }
}

.incident-zoom-close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: white;
  color: #333;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  font-size: 20px;
  transition: all 0.2s ease;
}

.incident-zoom-close-btn:hover {
  background-color: #f8f9fa;
  transform: scale(1.05);
}

/* Responsive adjustments for medical images */
@media (max-width: 768px) {
  .medical-images-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }
  
  .modal-image {
    max-height: 250px;
  }
}

/* Override handled by global.css */

/* ================================================
   MODAL Z-INDEX FIXES FOR HEADER HIDING
   ================================================ */

/* Image Modal - High z-index to ensure it's above header */
.image-modal {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-color: rgba(0, 0, 0, 0.9) !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  z-index: 999999999 !important; /* Extremely high z-index to stay above header */
  padding: 20px !important;
  cursor: pointer !important;
  animation: fadeIn 0.3s ease-out;
}

.image-modal .modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: transparent;
}

.image-modal .modal-content {
  position: relative;
  max-width: 90%;
  max-height: 90%;
  z-index: 999999999;
  pointer-events: none;
}

.image-modal .modal-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: zoomIn 0.4s ease-out;
  pointer-events: none; /* Prevent clicking on image */
}

.image-modal .modal-close {
  position: absolute;
  top: -50px;
  right: -50px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  font-size: 18px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  pointer-events: auto;
}

.image-modal .modal-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.image-modal .image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: white;
  padding: 20px;
}

/* Force hide header when modal is open */
body:has(.image-modal) .parent-header,
body:has(.image-modal) .fix-parent-header,
body:has(.image-modal) header,
.simple-parent-layout:has(.image-modal) .parent-header {
  z-index: -999999 !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
  transform: translateY(-200px) !important;
}

/* Animations */
@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Fallback class-based header hiding */
body.modal-open .parent-header,
body.modal-open .fix-parent-header,
body.modal-open header,
.modal-open .parent-header,
.modal-open .fix-parent-header,
.modal-open header {
  z-index: -999999 !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
  transform: translateY(-200px) !important;
}

/* ================================================
   GENERAL TAB VACCINATION SECTION STYLES
   ================================================ */

.vaccination-section {
  margin-top: var(--space-6);
  padding: var(--space-6);
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.vaccination-section h3 {
  font-size: var(--text-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin: 0 0 var(--space-4) 0;
  text-align: center;
  border-bottom: 2px solid var(--color-primary);
  padding-bottom: var(--space-3);
}

.vaccination-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.vaccination-item {
  background: var(--color-gray-50);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-md);
  padding: var(--space-5);
  transition: all var(--transition);
  position: relative;
  overflow: hidden;
}

.vaccination-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
}

.vaccination-item:hover {
  background: var(--color-white);
  border-color: var(--color-primary-light);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.vaccination-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-4);
  flex-wrap: wrap;
  gap: var(--space-3);
}

.vaccine-name-primary {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  line-height: 1.3;
  flex: 1;
  min-width: 200px;
}

.dose-info {
  background: var(--color-primary);
  color: var(--color-white);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  white-space: nowrap;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.vaccination-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.vaccination-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.status-completed {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  background: var(--color-green-100);
  color: var(--color-green-700);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  border: 1px solid var(--color-green-200);
}

.status-pending {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  background: var(--color-yellow-100);
  color: var(--color-yellow-700);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  border: 1px solid var(--color-yellow-200);
}

.vaccination-date {
  color: var(--color-gray-600);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
}

.next-dose-date {
  color: var(--color-orange-600);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  font-style: italic;
}

.vaccination-location {
  color: var(--color-gray-700);
  font-size: var(--text-sm);
  margin-bottom: var(--space-2);
}

.vaccination-location strong {
  color: var(--color-gray-900);
  font-weight: var(--font-weight-semibold);
}

.vaccination-notes {
  color: var(--color-gray-600);
  font-size: var(--text-sm);
  line-height: 1.5;
  background: var(--color-blue-50);
  padding: var(--space-3);
  border-radius: var(--radius-sm);
  border-left: 3px solid var(--color-blue-400);
}

.vaccination-notes strong {
  color: var(--color-blue-700);
  font-weight: var(--font-weight-semibold);
}

.next-dose {
  color: var(--color-purple-700);
  font-size: var(--text-sm);
  background: var(--color-purple-50);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-sm);
  border-left: 3px solid var(--color-purple-400);
}

.next-dose strong {
  color: var(--color-purple-800);
  font-weight: var(--font-weight-semibold);
}

.vaccination-type-badge {
  margin-top: var(--space-2);
}

.vaccination-type-badge span {
  display: inline-block;
  padding: var(--space-1) var(--space-2);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.vaccination-type-badge .type-school_plan {
  background-color: #BBDEFB; /* Xanh dương nhạt */
  color: #1976D2;            /* Xanh dương đậm */
}

.vaccination-type-badge .type-parent_declared {
  background-color: #FFF9C4; /* Vàng nhạt */
  color: #FBC02D;            /* Vàng đậm */
}

.vaccination-type-badge .type-individual {
  background-color: #C8E6C9; /* Xanh lá nhạt */
  color: #388E3C;            /* Xanh lá đậm */
}

.vaccination-type-badge .type-campaign {
  background-color: #E1BEE7; /* Tím nhạt */
  color: #8E24AA;            /* Tím đậm */
}

/* Responsive Design for Vaccination Section */
@media (max-width: 768px) {
  .vaccination-section {
    padding: var(--space-4);
  }

  .vaccination-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-2);
  }

  .vaccine-name-primary {
    font-size: var(--text-base);
    min-width: auto;
  }

  .dose-info {
    align-self: flex-start;
    padding: var(--space-1) var(--space-2);
    font-size: var(--text-xs);
  }

  .vaccination-status {
    flex-direction: column;
    align-items: flex-start;
  }

  .vaccination-location,
  .vaccination-notes,
  .next-dose {
    font-size: var(--text-xs);
    padding: var(--space-2);
  }
}

@media (max-width: 480px) {
  .vaccination-item {
    padding: var(--space-3);
  }

  .vaccine-name-primary {
    font-size: var(--text-sm);
    line-height: 1.4;
  }

  .vaccination-details {
    gap: var(--space-2);
  }
}

/* ================================================
   PAGINATION CONTROLS FOR VACCINATION SECTION
   ================================================ */

.pagination-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-3);
  margin-top: var(--space-4);
  padding: var(--space-3);
  background: var(--color-gray-50);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-gray-200);
}

.pagination-info {
  color: var(--color-gray-600);
  font-size: 0.875rem;
  text-align: center;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid var(--color-gray-300);
  background: white;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-700);
}

.pagination-btn:hover:not(:disabled) {
  background: var(--color-primary-light);
  border-color: var(--color-primary);
  color: var(--color-primary);
  transform: translateY(-1px);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--color-gray-100);
  color: var(--color-gray-400);
}

.pagination-pages {
  display: flex;
  gap: var(--space-1);
}

.pagination-page {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid var(--color-gray-300);
  background: white;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-700);
}

.pagination-page:hover {
  background: var(--color-primary-light);
  border-color: var(--color-primary);
  color: var(--color-primary);
  transform: translateY(-1px);
}

.pagination-page.active {
  background: var(--color-primary);
  border-color: var(--color-primary);
  color: white;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(var(--color-primary-rgb), 0.3);
}

.pagination-page.active:hover {
  background: var(--color-primary-dark);
  transform: translateY(-1px);
}

/* Responsive pagination */
@media (max-width: 768px) {
  .pagination-container {
    padding: var(--space-2);
  }
  
  .pagination-controls {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .pagination-pages {
    max-width: 100%;
    overflow-x: auto;
    padding: var(--space-1);
  }
  
  .pagination-btn,
  .pagination-page {
    width: 36px;
    height: 36px;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .pagination-info {
    font-size: 0.8rem;
    margin-bottom: var(--space-2);
  }
  
  .pagination-controls {
    flex-direction: column;
    gap: var(--space-2);
  }
  
  .pagination-pages {
    justify-content: center;
    gap: 6px;
  }
}

/* Vaccination Notification Modal */
.vaccination-notification-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.15);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.vaccination-notification-modal {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 32px rgba(0,0,0,0.18);
  padding: 32px 28px 24px 28px;
  min-width: 320px;
  max-width: 90vw;
  text-align: center;
  position: relative;
  animation: fadeIn 0.2s;
}

.vaccination-notification-success {
  border-left: 6px solid #4caf50;
}

.vaccination-notification-error {
  border-left: 6px solid #f44336;
}

.vaccination-notification-icon {
  margin-bottom: 12px;
}

.vaccination-notification-content h3 {
  font-size: 1.25rem;
  margin-bottom: 8px;
  color: #222;
}

.vaccination-notification-content p {
  font-size: 1rem;
  color: #444;
  margin-bottom: 0;
}

.vaccination-notification-close {
  position: absolute;
  top: 12px;
  right: 12px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: background 0.2s;
}

.vaccination-notification-close:hover {
  background: #f2f2f2;
}

.vaccination-notification-progress {
  height: 3px;
  width: 100%;
  background: linear-gradient(90deg, #4caf50 0%, #f44336 100%);
  border-radius: 2px;
  margin: 16px 0 0 0;
  animation: notificationProgress 3s linear forwards;
}

@keyframes notificationProgress {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}