/* Admin Student Image Upload Component Styles */

.admin-student-image-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

/* Image Container */
.admin-image-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.admin-image-wrapper {
  position: relative;
  width: 150px;
  height: 150px;
  border-radius: 16px;
  overflow: hidden;
  border: 3px solid #e5e7eb;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.admin-image-wrapper:hover {
  border-color: #3b82f6;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.admin-student-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: all 0.3s ease;
}

.admin-image-wrapper:hover .admin-student-image {
  transform: scale(1.05);
}

/* Image Overlay */
.admin-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.8) 0%, rgba(37, 99, 235, 0.9) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  opacity: 0;
  transition: all 0.3s ease;
  backdrop-filter: blur(2px);
}

.admin-image-wrapper:hover .admin-image-overlay {
  opacity: 1;
}

.admin-image-action-btn {
  width: 44px;
  height: 44px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
  color: white;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.admin-image-action-btn:disabled {
  cursor: not-allowed;
  opacity: 0.4;
}

.admin-upload-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.6);
  transform: scale(1.1);
}

.admin-delete-btn:hover:not(:disabled) {
  background: rgba(239, 68, 68, 0.8);
  border-color: rgba(255, 255, 255, 0.6);
  transform: scale(1.1);
}

/* No Image State */
.admin-no-image {
  width: 150px;
  height: 150px;
  border: 3px dashed #d1d5db;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  color: #6b7280;
  transition: all 0.3s ease;
}

.admin-no-image:hover {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1e40af;
  transform: translateY(-2px);
}

.admin-no-image svg {
  font-size: 2rem;
  opacity: 0.6;
}

.admin-no-image span {
  font-size: 0.9rem;
  font-weight: 500;
}

.admin-upload-image-btn {
  margin-top: 0.5rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.admin-upload-image-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.admin-upload-image-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Error Message */
.admin-upload-error {
  color: #ef4444;
  font-size: 0.85rem;
  text-align: center;
  padding: 0.5rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  margin-top: 0.5rem;
}

/* Modal Styles */
.admin-image-upload-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(4px);
  animation: adminFadeIn 0.3s ease-out;
}

@keyframes adminFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.admin-image-upload-modal-dialog {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  animation: adminSlideUp 0.3s ease-out;
}

@keyframes adminSlideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.admin-modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.admin-modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.admin-modal-close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(107, 114, 128, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s ease;
}

.admin-modal-close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  transform: scale(1.1);
}

.admin-modal-body {
  padding: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
}

.admin-image-preview {
  text-align: center;
  margin-bottom: 1.5rem;
}

.admin-image-preview img {
  max-width: 100%;
  max-height: 300px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.admin-file-info {
  background: #f8fafc;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border: 1px solid #e2e8f0;
}

.admin-file-info p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
  color: #4b5563;
}

.admin-upload-note {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #93c5fd;
}

.admin-upload-note p {
  margin: 0 0 0.5rem 0;
  font-weight: 600;
  color: #1e40af;
  font-size: 0.9rem;
}

.admin-upload-note ul {
  margin: 0;
  padding-left: 1.25rem;
  color: #1e40af;
}

.admin-upload-note li {
  font-size: 0.85rem;
  margin-bottom: 0.25rem;
}

.admin-modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
  background: #f9fafb;
}

.admin-btn-cancel {
  padding: 0.75rem 1.5rem;
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.admin-btn-cancel:hover:not(:disabled) {
  background: #e5e7eb;
  border-color: #9ca3af;
  transform: translateY(-1px);
}

.admin-btn-upload {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.admin-btn-upload:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.admin-btn-upload:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Spinning animation */
.admin-spinning {
  animation: adminSpin 1s linear infinite;
}

@keyframes adminSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
