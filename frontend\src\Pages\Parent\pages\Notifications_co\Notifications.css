/* ================================================
   PARENT NOTIFICATIONS - ISOLATED CSS STYLES
   Prefix: pn- (parent-notifications)
   ================================================ */

/* CSS Variables - Isolated to avoid conflicts */
.pn-root {
  /* Colors */
  --pn-primary: #015C92;
  --pn-primary-light: #2D82B5;
  --pn-primary-lighter: #428CD4;
  --pn-primary-dark: #01456E;
  --pn-gradient: linear-gradient(135deg, #015C92 0%, #2D82B5 50%, #428CD4 100%);
  
  /* Status Colors */
  --pn-success: #10b981;
  --pn-warning: #f59e0b;
  --pn-danger: #ef4444;
  --pn-info: #3b82f6;
  
  /* Neutrals */
  --pn-white: #ffffff;
  --pn-gray-50: #f9fafb;
  --pn-gray-100: #f3f4f6;
  --pn-gray-200: #e5e7eb;
  --pn-gray-300: #d1d5db;
  --pn-gray-400: #9ca3af;
  --pn-gray-500: #6b7280;
  --pn-gray-600: #4b5563;
  --pn-gray-700: #374151;
  --pn-gray-800: #1f2937;
  --pn-gray-900: #111827;
  
  /* Shadows */
  --pn-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --pn-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --pn-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --pn-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --pn-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Typography */
  --pn-font: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --pn-text-xs: 0.75rem;
  --pn-text-sm: 0.875rem;
  --pn-text-base: 1rem;
  --pn-text-lg: 1.125rem;
  --pn-text-xl: 1.25rem;
  --pn-text-2xl: 1.5rem;
  --pn-text-3xl: 1.875rem;
  --pn-text-4xl: 2.25rem;
  
  /* Spacing */
  --pn-space-1: 0.25rem;
  --pn-space-2: 0.5rem;
  --pn-space-3: 0.75rem;
  --pn-space-4: 1rem;
  --pn-space-5: 1.25rem;
  --pn-space-6: 1.5rem;
  --pn-space-8: 2rem;
  --pn-space-10: 2.5rem;
  --pn-space-12: 3rem;
  --pn-space-16: 4rem;
  
  /* Border Radius */
  --pn-radius-sm: 0.375rem;
  --pn-radius: 0.5rem;
  --pn-radius-md: 0.75rem;
  --pn-radius-lg: 1rem;
  --pn-radius-xl: 1.5rem;
  
  /* Transitions */
  --pn-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --pn-transition-slow: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reset for isolated scope */
.pn-root *,
.pn-root *::before,
.pn-root *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* ================================================
   MAIN CONTAINER
   ================================================ */

.pn-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--pn-gray-50) 0%, var(--pn-white) 50%, var(--pn-gray-100) 100%);
  font-family: var(--pn-font);
  padding: var(--pn-space-8);
  position: relative;
  width: 100%;
  max-width: none;
  margin: 0;
}

.pn-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: var(--pn-gradient);
  opacity: 0.03;
  z-index: 0;
}

/* ================================================
   HEADER SECTION
   ================================================ */
   .pn-header-title {
    color: #01456E;
   }

.pn-header {
  text-align: center;
  margin-bottom: var(--pn-space-10);
  position: relative;
  z-index: 1;
}

.pn-title {
  font-size: var(--pn-text-4xl);
  font-weight: 700;
  background: var(--pn-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--pn-space-4);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--pn-space-5);
}

.pn-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: var(--pn-gradient);
  border-radius: var(--pn-radius-sm);
}

.pn-title-icon {
  font-size: var(--pn-text-3xl);
  background: var(--pn-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: pn-pulse 2s infinite;
}

.pn-subtitle {
  color: var(--pn-gray-600);
  font-size: var(--pn-text-xl);
  font-weight: 500;
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
  opacity: 0.9;
}

@keyframes pn-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* ================================================
   TABS NAVIGATION - ENHANCED
   ================================================ */

.pn-tabs {
  display: flex;
  gap: var(--pn-space-3);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: var(--pn-space-3);
  border-radius: var(--pn-radius-xl);
  margin-bottom: var(--pn-space-10);
  box-shadow: var(--pn-shadow-lg);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: var(--pn-space-10);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.pn-tab {
  flex: 1;
  background: transparent;
  border: none;
  padding: var(--pn-space-5) var(--pn-space-6);
  border-radius: var(--pn-radius-lg);
  font-size: var(--pn-text-lg);
  font-weight: 600;
  color: var(--pn-gray-600);
  cursor: pointer;
  transition: var(--pn-transition-slow);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--pn-space-3);
  position: relative;
  min-height: 64px;
  overflow: hidden;
  font-family: var(--pn-font);
}

.pn-tab:hover:not(.pn-tab--active) {
  background: rgba(255, 255, 255, 0.8);
  color: var(--pn-primary);
  transform: translateY(-2px);
  box-shadow: var(--pn-shadow-md);
}

/* Tab Active State - Simplified and Clear */
.pn-tab--active {
  background: var(--pn-gradient) !important;
  color: #ffffff !important;
  transform: translateY(-3px);
  box-shadow: var(--pn-shadow-xl);
}

/* Force all children to be white */
.pn-tab--active *,
.pn-tab--active i,
.pn-tab--active span {
  color: #ffffff !important;
}

/* Specific targeting for text nodes */
.pn-tab--active {
  text-shadow: none !important;
}

.pn-tab-icon {
  font-size: var(--pn-text-xl);
  transition: var(--pn-transition);
}

.pn-tab--active .pn-tab-icon {
  animation: pn-bounce 0.6s ease-out;
  color: #ffffff !important;
}

@keyframes pn-bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-5px); }
  60% { transform: translateY(-3px); }
}

/* ================================================
   CONTENT CONTAINER
   ================================================ */

.pn-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: var(--pn-radius-xl);
  box-shadow: var(--pn-shadow-xl);
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
}

.pn-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--pn-gradient);
}

.pn-tab-content {
  min-height: 600px;
  padding: var(--pn-space-8);
}

/* ================================================
   HEALTH CHECKUP CONTENT
   ================================================ */

.pn-health-content {
  display: flex;
  flex-direction: column;
  gap: var(--pn-space-6);
}

/* Filter Controls */
.pn-filters,
.parentfix-filters {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 2px solid var(--pn-gray-200);
  border-radius: var(--pn-radius-xl);
  padding: var(--pn-space-8);
  margin-bottom: var(--pn-space-8);
  box-shadow: var(--pn-shadow-lg);
}

.pn-filters-header,
.parentfix-filters-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--pn-space-6);
}

.pn-filters-title,
.parentfix-filters-title {
  display: flex;
  align-items: center;
  gap: var(--pn-space-3);
  font-size: var(--pn-text-xl);
  font-weight: 600;
  color: var(--pn-gray-800);
}

.pn-filter-count,
.parentfix-filter-count {
  background: var(--pn-primary);
  color: var(--pn-white);
  padding: var(--pn-space-1) var(--pn-space-3);
  border-radius: var(--pn-radius);
  font-size: var(--pn-text-sm);
  font-weight: 600;
}

.pn-clear-filters,
.parentfix-clear-filters {
  background: var(--pn-danger);
  color: var(--pn-white);
  border: none;
  padding: var(--pn-space-2) var(--pn-space-4);
  border-radius: var(--pn-radius);
  font-size: var(--pn-text-sm);
  font-weight: 600;
  cursor: pointer;
  transition: var(--pn-transition);
  display: flex;
  align-items: center;
  gap: var(--pn-space-2);
}

.pn-clear-filters:hover,
.parentfix-clear-filters:hover {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: var(--pn-shadow-md);
}

.pn-filters-row,
.parentfix-filters-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--pn-space-6);
  margin-bottom: var(--pn-space-6);
  align-items: end;
}

.pn-filter-group,
.parentfix-filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--pn-space-2);
  min-height: 72px;
  justify-content: flex-end;
}

.pn-filter-label,
.parentfix-filter-label {
  display: flex;
  align-items: center;
  gap: var(--pn-space-2);
  font-weight: 500;
  color: var(--pn-gray-700);
  font-size: var(--pn-text-sm);
}

.pn-filter-select,
.pn-filter-group .selectstudentfix,
.parentfix-filter-select {
  padding: var(--pn-space-3) var(--pn-space-4);
  border: 2px solid var(--pn-gray-300);
  border-radius: var(--pn-radius);
  font-family: var(--pn-font);
  font-size: var(--pn-text-base);
  color: var(--pn-gray-700);
  background: var(--pn-white);
  cursor: pointer;
  transition: var(--pn-transition);
  height: 48px;
  min-width: 180px;
  box-sizing: border-box;
  /* Remove default arrow completely */
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

.pn-filter-select:focus,
.pn-filter-group .selectstudentfix:focus,
.parentfix-filter-select:focus {
  border-color: var(--pn-primary);
  outline: none;
  box-shadow: 0 0 0 3px rgba(1, 92, 146, 0.1);
}

/* Option elements styling */
.pn-filter-select option,
.pn-filter-group .selectstudentfix option,
.parentfix-filter-select option {
  font-family: var(--pn-font);
  font-size: var(--pn-text-base);
  color: var(--pn-gray-700) !important;
  background: var(--pn-white) !important;
  padding: 8px 12px;
  line-height: 1.5;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Selected option styling */
.pn-filter-select option:checked,
.pn-filter-group .selectstudentfix option:checked,
.parentfix-filter-select option:checked {
  background: var(--pn-primary-light) !important;
  color: var(--pn-white) !important;
}

/* Option hover styling */
.pn-filter-select option:hover,
.pn-filter-group .selectstudentfix option:hover,
.parentfix-filter-select option:hover {
  background: var(--pn-gray-100) !important;
  color: var(--pn-gray-800) !important;
}

/* Ensure proper text rendering and remove default arrows */
.pn-filter-select,
.pn-filter-group .selectstudentfix,
.parentfix-filter-select {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23374151' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right 12px center !important;
  background-size: 16px !important;
  padding-right: 40px !important;
  line-height: 1.5;
  font-weight: 400;
}

/* Remove IE default arrow */
.pn-filter-select::-ms-expand,
.pn-filter-group .selectstudentfix::-ms-expand,
.parentfix-filter-select::-ms-expand {
  display: none;
}

/* Remove Firefox default arrow */
.pn-filter-select:-moz-focusring,
.pn-filter-group .selectstudentfix:-moz-focusring,
.parentfix-filter-select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 var(--pn-gray-700);
}

/* Additional browser compatibility fixes */
select.pn-filter-select,
select.selectstudentfix,
select.parentfix-filter-select {
  background-color: var(--pn-white) !important;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23374151' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right 12px center !important;
  background-size: 16px !important;
}

/* Force remove all default select styling */
.pn-filter-container select,
.pn-filter-group select,
.parentfix-filter-container select {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23374151' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right 12px center !important;
  background-size: 16px !important;
  padding-right: 40px !important;
}

/* Hover and active states for select */
.pn-filter-select:hover,
.pn-filter-group .selectstudentfix:hover,
.parentfix-filter-select:hover {
  border-color: var(--pn-primary-light);
}

.pn-filter-select:active,
.pn-filter-group .selectstudentfix:active,
.parentfix-filter-select:active {
  border-color: var(--pn-primary);
}

/* Sort Toggle Button */
.pn-sort-toggle,
.parentfix-sort-toggle {
  display: flex;
  align-items: center;
  gap: var(--pn-space-2);
  padding: var(--pn-space-3) var(--pn-space-4);
  border: 2px solid var(--pn-gray-300);
  border-radius: var(--pn-radius);
  background: var(--pn-white);
  color: var(--pn-gray-700);
  font-size: var(--pn-text-base);
  font-weight: 500;
  cursor: pointer;
  transition: var(--pn-transition);
  height: 48px;
  min-width: 180px;
  justify-content: center;
  box-sizing: border-box;
}

.pn-sort-toggle:hover,
.parentfix-sort-toggle:hover {
  border-color: var(--pn-primary-light);
  background: var(--pn-gray-50);
  transform: translateY(-1px);
}

.pn-sort-toggle.active,
.parentfix-sort-toggle.parentfix-active {
  border-color: var(--pn-primary);
  background: var(--pn-primary);
  color: var(--pn-white);
  box-shadow: 0 0 0 3px rgba(1, 92, 146, 0.1);
}

.pn-sort-toggle.active:hover,
.parentfix-sort-toggle.parentfix-active:hover {
  background: var(--pn-primary-dark);
  border-color: var(--pn-primary-dark);
}

.pn-sort-toggle i,
.parentfix-sort-toggle i {
  font-size: var(--pn-text-sm);
}

.pn-filters-results {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: var(--pn-space-4);
  border-top: 1px solid var(--pn-gray-200);
}

.pn-result-count {
  display: flex;
  align-items: center;
  gap: var(--pn-space-2);
  color: var(--pn-gray-600);
  font-size: var(--pn-text-sm);
}

/* Consent List */
.pn-consent-list {
  display: flex;
  flex-direction: column;
  gap: var(--pn-space-5);
}

.pn-consent-item {
  background: var(--pn-white);
  border: 2px solid var(--pn-gray-200);
  border-radius: var(--pn-radius-lg);
  padding: var(--pn-space-6);
  cursor: pointer;
  transition: var(--pn-transition-slow);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;
  box-shadow: var(--pn-shadow-sm);
}

.pn-consent-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--pn-gradient);
  transition: var(--pn-transition);
}

.pn-consent-item:hover {
  transform: translateY(-4px);
  box-shadow: var(--pn-shadow-xl);
  border-color: var(--pn-primary-light);
}

.pn-consent-item:hover::before {
  width: 8px;
}

.pn-consent-content {
  flex: 1;
  position: relative;
  z-index: 1;
}

.pn-consent-title {
  font-size: var(--pn-text-lg);
  font-weight: 600;
  color: var(--pn-gray-900);
  margin-bottom: var(--pn-space-2);
  line-height: 1.4;
}

.pn-consent-meta {
  color: var(--pn-gray-600);
  font-size: var(--pn-text-base);
  line-height: 1.6;
  font-weight: 500;
}

/* Status Badges */
.pn-status-badge {
  display: flex;
  align-items: center;
  gap: var(--pn-space-2);
  padding: var(--pn-space-3) var(--pn-space-5);
  border-radius: var(--pn-radius-xl);
  font-size: var(--pn-text-sm);
  font-weight: 700;
  white-space: nowrap;
  position: relative;
  z-index: 1;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--pn-shadow-sm);
}

.pn-status-badge--pending {
  background: linear-gradient(135deg, var(--pn-warning) 0%, #fbbf24 100%);
  color: var(--pn-white);
  animation: pn-shimmer 2s infinite;
}

.pn-status-badge--confirmed {
  background: linear-gradient(135deg, var(--pn-success) 0%, #34d399 100%);
  color: var(--pn-white);
}

.pn-status-badge--rejected {
  background: linear-gradient(135deg, var(--pn-danger) 0%, #f87171 100%);
  color: var(--pn-white);
}

@keyframes pn-shimmer {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

/* ================================================
   VACCINATION CONTENT
   ================================================ */

.pn-vaccination-content {
  display: flex;
  flex-direction: column;
  gap: var(--pn-space-6);
  min-height: 500px;
}

.pn-vaccination-list {
  display: flex;
  flex-direction: column;
  gap: var(--pn-space-5);
}

.pn-vaccination-item {
  background: var(--pn-white);
  border: 2px solid var(--pn-gray-200);
  border-radius: var(--pn-radius-lg);
  padding: var(--pn-space-6);
  cursor: pointer;
  transition: var(--pn-transition-slow);
  position: relative;
  overflow: hidden;
  box-shadow: var(--pn-shadow-sm);
}

.pn-vaccination-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--pn-gradient);
  transition: var(--pn-transition);
}

.pn-vaccination-item:hover {
  transform: translateY(-3px);
  box-shadow: var(--pn-shadow-xl);
  border-color: var(--pn-primary-light);
}

.pn-vaccination-item:hover::before {
  width: 8px;
}

.pn-vaccination-content-inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.pn-vaccination-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--pn-space-2);
}

.pn-vaccination-title {
  font-size: var(--pn-text-lg);
  font-weight: 600;
  color: var(--pn-gray-900);
  margin: 0;
}

.pn-vaccination-date {
  font-size: var(--pn-text-sm);
  color: var(--pn-gray-600);
  margin: 0;
}

.pn-vaccination-arrow {
  font-size: var(--pn-text-xl);
  color: var(--pn-gray-400);
}

/* ================================================
   NO DATA STATES
   ================================================ */

.pn-no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--pn-space-16) var(--pn-space-8);
  background: linear-gradient(135deg, var(--pn-white) 0%, var(--pn-gray-50) 100%);
  border-radius: var(--pn-radius-xl);
  box-shadow: var(--pn-shadow-md);
  text-align: center;
  border: 2px dashed var(--pn-gray-300);
  position: relative;
  overflow: hidden;
}

.pn-no-data::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--pn-gradient);
  opacity: 0.02;
}

.pn-no-data-icon {
  font-size: var(--pn-text-4xl);
  color: var(--pn-gray-400);
  margin-bottom: var(--pn-space-5);
  animation: pn-pulse 2s infinite;
}

.pn-no-data-text {
  color: var(--pn-gray-600);
  font-size: var(--pn-text-xl);
  font-weight: 500;
  position: relative;
  z-index: 1;
}

/* ================================================
   PAGINATION CONTROLS
   ================================================ */

.pn-pagination {
  display: flex;
  flex-direction: column;
  gap: var(--pn-space-4);
  margin-top: var(--pn-space-8);
  padding: var(--pn-space-6);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: var(--pn-radius-lg);
  border: 1px solid var(--pn-gray-200);
  box-shadow: var(--pn-shadow-sm);
}

.pn-pagination-info {
  text-align: center;
  color: var(--pn-gray-600);
  font-size: var(--pn-text-sm);
  font-weight: 500;
}

.pn-pagination-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--pn-space-2);
  flex-wrap: wrap;
}

.pn-pagination-btn {
  display: flex;
  align-items: center;
  gap: var(--pn-space-2);
  padding: var(--pn-space-3) var(--pn-space-4);
  background: var(--pn-white);
  border: 2px solid var(--pn-gray-300);
  border-radius: var(--pn-radius);
  color: var(--pn-gray-700);
  font-size: var(--pn-text-sm);
  font-weight: 600;
  cursor: pointer;
  transition: var(--pn-transition);
  min-width: 44px;
  justify-content: center;
  box-shadow: var(--pn-shadow-sm);
  font-family: var(--pn-font);
}

.pn-pagination-btn:hover:not(:disabled) {
  background: var(--pn-gradient);
  color: var(--pn-white);
  border-color: transparent;
  transform: translateY(-1px);
  box-shadow: var(--pn-shadow-md);
}

.pn-pagination-btn:disabled {
  background: var(--pn-gray-100);
  color: var(--pn-gray-400);
  border-color: var(--pn-gray-200);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.pn-pagination-numbers {
  display: flex;
  gap: var(--pn-space-1);
  align-items: center;
}

.pn-pagination-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: var(--pn-white);
  border: 2px solid var(--pn-gray-300);
  border-radius: var(--pn-radius);
  color: var(--pn-gray-700);
  font-size: var(--pn-text-sm);
  font-weight: 600;
  cursor: pointer;
  transition: var(--pn-transition);
  box-shadow: var(--pn-shadow-sm);
  font-family: var(--pn-font);
}

.pn-pagination-number:hover {
  background: var(--pn-primary-lighter);
  color: var(--pn-white);
  border-color: var(--pn-primary-lighter);
  transform: translateY(-1px);
  box-shadow: var(--pn-shadow-md);
}

.pn-pagination-number--active {
  background: var(--pn-gradient);
  color: var(--pn-white);
  border-color: transparent;
  box-shadow: var(--pn-shadow-lg);
  transform: translateY(-1px);
}

/* ================================================
   MODAL OVERLAY (for ConsentDetailModal)
   ================================================ */

.pn-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: var(--pn-space-4);
}

.pn-modal-content {
  background: var(--pn-white);
  border-radius: var(--pn-radius-xl);
  box-shadow: var(--pn-shadow-xl);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.pn-modal-header {
  padding: var(--pn-space-6);
  border-bottom: 1px solid var(--pn-gray-200);
  background: var(--pn-gradient);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.pn-modal-title {
  font-size: var(--pn-text-xl);
  font-weight: 600;
  color: #ffffff;
}

.pn-modal-close {
  background: none;
  border: none;
  font-size: var(--pn-text-xl);
  color: var(--pn-gray-500);
  cursor: pointer;
  transition: var(--pn-transition);
  width: 40px;
  height: 40px;
  border-radius: var(--pn-radius);
  display: flex;
  align-items: center;
  justify-content: center;
}

.pn-modal-close:hover {
  background: var(--pn-gray-100);
  color: var(--pn-gray-700);
}

.pn-modal-body {
  padding: var(--pn-space-6);
}

/* ================================================
   RESPONSIVE DESIGN
   ================================================ */

@media (max-width: 768px) {
  .pn-container {
    padding: var(--pn-space-5);
  }
  
  .pn-title {
    font-size: var(--pn-text-3xl);
    flex-direction: column;
    gap: var(--pn-space-4);
  }
  
  .pn-subtitle {
    font-size: var(--pn-text-lg);
  }
  
  .pn-tabs {
    flex-direction: column;
    gap: var(--pn-space-3);
    max-width: none;
    padding: var(--pn-space-4);
  }
  
  .pn-tab {
    justify-content: flex-start;
    padding: var(--pn-space-4) var(--pn-space-6);
    min-height: 56px;
  }
  
  .pn-tab-content {
    padding: var(--pn-space-6);
  }
  
  .pn-filters {
    padding: var(--pn-space-6);
  }
  
  .pn-filters-row {
    grid-template-columns: 1fr;
    gap: var(--pn-space-4);
  }
  
  .pn-consent-item {
    padding: var(--pn-space-5);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--pn-space-4);
  }
  
  .pn-status-badge {
    align-self: flex-end;
    padding: var(--pn-space-2) var(--pn-space-4);
    font-size: var(--pn-text-xs);
  }
  
  .pn-pagination {
    padding: var(--pn-space-4);
    gap: var(--pn-space-3);
  }
  
  .pn-pagination-controls {
    flex-direction: column;
    gap: var(--pn-space-3);
  }
  
  .pn-pagination-numbers {
    order: 2;
  }
  
  .pn-pagination-btn {
    min-width: 120px;
    padding: var(--pn-space-3) var(--pn-space-5);
  }
  
  .pn-pagination-number {
    width: 40px;
    height: 40px;
    font-size: var(--pn-text-xs);
  }
}

@media (max-width: 480px) {
  .pn-title {
    font-size: var(--pn-text-2xl);
  }
  
  .pn-consent-title {
    font-size: var(--pn-text-base);
  }
  
  .pn-tab-content {
    padding: var(--pn-space-4);
  }
  
  .pn-pagination-numbers {
    gap: 1px;
  }
  
  .pn-pagination-number {
    width: 36px;
    height: 36px;
  }
  
  .pn-pagination-btn {
    font-size: var(--pn-text-xs);
    min-width: 100px;
    padding: var(--pn-space-2) var(--pn-space-4);
  }
}

/* ================================================
   LOADING SPINNER
   ================================================ */

.pn-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--pn-space-16);
}

.pn-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--pn-gray-200);
  border-left-color: var(--pn-primary);
  border-radius: 50%;
  animation: pn-spin 1s linear infinite;
}

@keyframes pn-spin {
  to { transform: rotate(360deg); }
}

/* ================================================
   DEBUG CONTROLS
   ================================================ */

.pn-debug {
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid #6366f1;
  border-radius: var(--pn-radius);
  padding: var(--pn-space-4);
  margin-bottom: var(--pn-space-6);
  font-size: var(--pn-text-sm);
}

.pn-debug-status {
  display: inline-block;
  padding: var(--pn-space-2) var(--pn-space-3);
  border-radius: var(--pn-radius-sm);
  color: var(--pn-white);
  font-weight: 600;
  margin-right: var(--pn-space-3);
  margin-bottom: var(--pn-space-2);
}

.pn-debug-status--success {
  background: var(--pn-success);
}

.pn-debug-status--error {
  background: var(--pn-danger);
}

.pn-debug-btn {
  background: #6366f1;
  color: var(--pn-white);
  border: none;
  padding: var(--pn-space-2) var(--pn-space-3);
  border-radius: var(--pn-radius-sm);
  font-size: var(--pn-text-xs);
  cursor: pointer;
  margin-right: var(--pn-space-2);
  margin-bottom: var(--pn-space-2);
  transition: var(--pn-transition);
}

.pn-debug-btn:hover {
  background: #4f46e5;
}

/* ================================================
   ANIMATIONS
   ================================================ */

@keyframes pn-fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pn-slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.pn-consent-item,
.pn-vaccination-item {
  animation: pn-fadeInUp 0.6s ease-out;
}

.pn-status-badge {
  animation: pn-slideInRight 0.8s ease-out;
} 

/* ================================================
   CRITICAL OVERRIDES - Highest Specificity for Tab Active
   ================================================ */

/* Ultimate override for active tab - maximum specificity */
.pn-root .pn-tabs .pn-tab.pn-tab--active {
  background: linear-gradient(135deg, #015C92 0%, #2D82B5 50%, #428CD4 100%) !important;
  color: #ffffff !important;
  font-weight: 600 !important;
}

.pn-root .pn-tabs .pn-tab.pn-tab--active * {
  color: #ffffff !important;
}

.pn-root .pn-tabs .pn-tab.pn-tab--active i {
  color: #ffffff !important;
}

.pn-root .pn-tabs .pn-tab.pn-tab--active .pn-tab-icon {
  color: #ffffff !important;
}

/* Ensure text content is visible */
.pn-root .pn-tabs .pn-tab.pn-tab--active {
  -webkit-text-fill-color: #ffffff !important;
  color: #ffffff !important;
}

/* ================================================
   END CRITICAL OVERRIDES
   ================================================ */ 

/* ================================================
   VACCINATION MODAL STYLES
   ================================================ */

.pn-vaccination-detail-info {
  display: flex;
  flex-direction: column;
  gap: var(--pn-space-4);
  margin-bottom: var(--pn-space-6);
  padding: var(--pn-space-6);
  background: var(--pn-gray-50);
  border-radius: var(--pn-radius-lg);
  border: 1px solid var(--pn-gray-200);
}

.pn-info-row {
  display: flex;
  align-items: center;
  gap: var(--pn-space-4);
}

.pn-info-label {
  display: flex;
  align-items: center;
  gap: var(--pn-space-2);
  min-width: 150px;
  font-weight: 600;
  color: var(--pn-gray-700);
  font-size: var(--pn-text-sm);
}

.pn-info-label i {
  color: var(--pn-primary);
  font-size: var(--pn-text-base);
}

.pn-info-value {
  flex: 1;
  color: var(--pn-gray-900);
  font-size: var(--pn-text-base);
}

.pn-vaccination-message {
  margin-bottom: var(--pn-space-6);
}

.pn-vaccination-message h4 {
  font-size: var(--pn-text-lg);
  font-weight: 600;
  color: var(--pn-gray-800);
  margin-bottom: var(--pn-space-3);
}

.pn-message-content {
  padding: var(--pn-space-5);
  background: var(--pn-white);
  border: 1px solid var(--pn-gray-200);
  border-radius: var(--pn-radius);
  line-height: 1.6;
  color: var(--pn-gray-700);
  white-space: pre-wrap;
}

.pn-vaccination-response-buttons {
  display: flex;
  gap: var(--pn-space-4);
  justify-content: center;
  padding-top: var(--pn-space-4);
  border-top: 1px solid var(--pn-gray-200);
}

.pn-vaccination-response-buttons button {
  padding: var(--pn-space-3) var(--pn-space-6);
  border: none;
  border-radius: var(--pn-radius);
  font-size: var(--pn-text-base);
  font-weight: 600;
  cursor: pointer;
  transition: var(--pn-transition);
  display: flex;
  align-items: center;
  gap: var(--pn-space-2);
  font-family: var(--pn-font);
}

.pn-vaccination-response-buttons .accept-btn {
  background: var(--pn-success);
  color: var(--pn-white);
}

.pn-vaccination-response-buttons .accept-btn:hover {
  background: #059669;
  transform: translateY(-2px);
  box-shadow: var(--pn-shadow-md);
}

.pn-vaccination-response-buttons .reject-btn {
  background: var(--pn-danger);
  color: var(--pn-white);
}

.pn-vaccination-response-buttons .reject-btn:hover {
  background: #dc2626;
  transform: translateY(-2px);
  box-shadow: var(--pn-shadow-md);
}

.pn-vaccination-response-buttons button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.pn-vaccination-response-info {
  text-align: center;
  padding: var(--pn-space-6);
}

.pn-response-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--pn-space-2);
  padding: var(--pn-space-3) var(--pn-space-5);
  border-radius: var(--pn-radius-xl);
  font-size: var(--pn-text-base);
  font-weight: 600;
  margin-bottom: var(--pn-space-3);
}

.pn-response-badge.accepted {
  background: linear-gradient(135deg, var(--pn-success) 0%, #34d399 100%);
  color: var(--pn-white);
}

.pn-response-badge.rejected {
  background: linear-gradient(135deg, var(--pn-danger) 0%, #f87171 100%);
  color: var(--pn-white);
}

.pn-response-time {
  color: var(--pn-gray-600);
  font-size: var(--pn-text-sm);
}

.pn-vaccination-info-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--pn-space-2);
  padding: var(--pn-space-4);
  background: var(--pn-gray-50);
  border-radius: var(--pn-radius);
  color: var(--pn-gray-600);
  font-size: var(--pn-text-sm);
  margin-top: var(--pn-space-4);
}

.pn-vaccination-info-message i {
  color: var(--pn-info);
  font-size: var(--pn-text-base);
}

/* ================================================
   MODAL FORM ELEMENTS
   ================================================ */

.pn-form-title {
  font-size: var(--pn-text-lg);
  font-weight: 600;
  color: var(--pn-gray-800);
  margin-bottom: var(--pn-space-3);
}

.pn-form-description {
  color: var(--pn-gray-600);
  font-size: var(--pn-text-sm);
  margin-bottom: var(--pn-space-4);
}

.pn-checkup-items {
  display: flex;
  flex-direction: column;
  gap: var(--pn-space-3);
  margin-bottom: var(--pn-space-6);
}

.pn-checkup-item {
  display: flex;
  align-items: center;
  padding: var(--pn-space-3) var(--pn-space-4);
  background: var(--pn-gray-50);
  border: 1px solid var(--pn-gray-200);
  border-radius: var(--pn-radius);
  cursor: pointer;
  transition: var(--pn-transition);
  position: relative;
}

.pn-checkup-item:hover {
  background: var(--pn-gray-100);
  border-color: var(--pn-primary-light);
}

.pn-checkbox {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.pn-checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--pn-gray-400);
  border-radius: var(--pn-radius-sm);
  margin-right: var(--pn-space-3);
  position: relative;
  transition: var(--pn-transition);
}

.pn-checkbox:checked ~ .pn-checkmark {
  background: var(--pn-primary);
  border-color: var(--pn-primary);
}

.pn-checkbox:checked ~ .pn-checkmark::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.pn-item-text {
  color: var(--pn-gray-700);
  font-size: var(--pn-text-base);
}

.pn-parent-notes {
  margin-bottom: var(--pn-space-6);
}

.pn-notes-label {
  display: block;
  font-weight: 500;
  color: var(--pn-gray-700);
  margin-bottom: var(--pn-space-2);
  font-size: var(--pn-text-sm);
}

.pn-notes-textarea {
  width: 100%;
  padding: var(--pn-space-3);
  border: 2px solid var(--pn-gray-300);
  border-radius: var(--pn-radius);
  font-size: var(--pn-text-base);
  font-family: var(--pn-font);
  resize: vertical;
  transition: var(--pn-transition);
}

.pn-notes-textarea:focus {
  outline: none;
  border-color: var(--pn-primary);
  box-shadow: 0 0 0 3px rgba(1, 92, 146, 0.1);
}

/* Modal footer buttons */
.modal-footer {
  display: flex;
  gap: var(--pn-space-3);
  justify-content: flex-end;
  padding: var(--pn-space-5);
  border-top: 1px solid var(--pn-gray-200);
  background: var(--pn-gray-50);
}

.modal-footer button {
  padding: var(--pn-space-3) var(--pn-space-5);
  border: none;
  border-radius: var(--pn-radius);
  font-size: var(--pn-text-base);
  font-weight: 600;
  cursor: pointer;
  transition: var(--pn-transition);
  display: flex;
  align-items: center;
  gap: var(--pn-space-2);
  font-family: var(--pn-font);
}

.btn-cancel {
  background: var(--pn-gray-200);
  color: var(--pn-gray-700);
}

.btn-cancel:hover {
  background: var(--pn-gray-300);
}

.btn-reject {
  background: var(--pn-danger);
  color: var(--pn-white);
}

.btn-reject:hover {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: var(--pn-shadow-md);
}

.btn-confirm {
  background: var(--pn-primary);
  color: var(--pn-white);
}

.btn-confirm:hover {
  background: var(--pn-primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--pn-shadow-md);
}

/* Modal info sections */
.campaign-info-modal {
  padding: var(--pn-space-5);
  background: var(--pn-gray-50);
  border-radius: var(--pn-radius-lg);
  margin-bottom: var(--pn-space-5);
}

.campaign-info-modal h3 {
  font-size: var(--pn-text-xl);
  color: var(--pn-gray-900);
  margin-bottom: var(--pn-space-3);
}

.student-info-modal {
  display: flex;
  align-items: center;
  gap: var(--pn-space-2);
  color: var(--pn-gray-600);
  margin-bottom: var(--pn-space-3);
}

.campaign-description-modal {
  color: var(--pn-gray-700);
  line-height: 1.6;
  margin-bottom: var(--pn-space-4);
}

.consent-status-modal {
  display: inline-flex;
  align-items: center;
  gap: var(--pn-space-2);
  padding: var(--pn-space-2) var(--pn-space-4);
  border-radius: var(--pn-radius-xl);
  font-size: var(--pn-text-sm);
  font-weight: 600;
}

.consent-status-modal.pending {
  background: rgba(245, 158, 11, 0.1);
  color: var(--pn-warning);
}

.consent-status-modal.confirmed {
  background: rgba(16, 185, 129, 0.1);
  color: var(--pn-success);
}

.consent-status-modal.rejected {
  background: rgba(239, 68, 68, 0.1);
  color: var(--pn-danger);
}

.confirmed-info-modal,
.rejected-info-modal {
  padding: var(--pn-space-5);
  border-radius: var(--pn-radius-lg);
  margin-top: var(--pn-space-5);
}

.selected-items-modal {
  list-style: none;
  padding: 0;
  margin: var(--pn-space-3) 0;
}

.selected-items-modal li {
  display: flex;
  align-items: center;
  gap: var(--pn-space-2);
  padding: var(--pn-space-2) 0;
  color: var(--pn-gray-700);
}

.selected-items-modal li i {
  color: var(--pn-success);
}

/* ================================================
   MODAL OPEN STATE
   ================================================ */

body.modal-open {
  overflow: hidden;
}

/* Ensure modal overlay has highest z-index */
.pn-modal-overlay {
  z-index: 999999 !important;
}

/* ================================================
   END CRITICAL OVERRIDES
   ================================================ */ 