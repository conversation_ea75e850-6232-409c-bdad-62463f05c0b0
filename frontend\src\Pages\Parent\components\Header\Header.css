/* Modern Header Design - Dark Blue Theme for Parent Pages */

.parent-header {
  padding: 0;
  height: auto;
  min-height: 70px;
  background: linear-gradient(135deg, #015C92 0%, #2D82B5 30%, #428CD4 60%, #88CDF6 85%, #BCE6FF 100%);
  color: #fff;
  box-shadow: 0 4px 20px rgba(1, 92, 146, 0.35);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.21, 0.6, 0.35, 1);
  border-bottom: none;
  display: flex;
  flex-direction: column;
  will-change: transform, opacity; /* Optimize for animation */
}

.parent-header.scrolled {
  min-height: 70px;
  background: linear-gradient(135deg, rgba(1, 92, 146, 0.95) 0%, rgba(45, 130, 181, 0.95) 30%, rgba(66, 140, 212, 0.95) 60%, rgba(136, 205, 246, 0.95) 85%, rgba(188, 230, 255, 0.95) 100%);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  box-shadow: 0 6px 24px rgba(1, 92, 146, 0.4);
  border-bottom: none;
}

.parent-header.scrolled .parent-header-navigation {
  display: none;
  height: 0;
  overflow: hidden;
}

.parent-header.scrolled .parent-header-top {
  height: 70px;
}

/* Top Header Row - Logo and Actions */
.parent-header-top {
  height: 70px;
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;
}

.parent-header .parent-container {
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  height: 100% !important;
}

/* Bottom Header Row - Navigation */
.parent-header-navigation {
  width: 100%;
  height: 60px;
  padding: 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05); /* Subtle overlay để tạo depth */
  transition: all 0.3s ease;
}

.parent-nav-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1400px;
  height: 100%;
  margin: 0 auto;
  padding: 0 2rem;
}

.parent-main-nav-list {
  display: flex;
  list-style: none;
  gap: 0;
  align-items: center;
  margin: 0;
  padding: 0;
  flex: 1;
  justify-content: flex-start;
  height: 100%;
}

.parent-main-nav-item {
  flex: 0 0 auto;
  max-width: 280px;
  height: 100%;
  display: flex;
  align-items: center;
}

.parent-main-nav-link {
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  padding: 0 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  border-radius: 0;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.parent-main-nav-link-ripple {
  position: absolute;
  width: 120px;
  height: 120px;
  background: rgba(255, 255, 255, 0.35);
  border-radius: 50%;
  pointer-events: none;
  transform: scale(0);
  animation: parent-main-nav-ripple 0.6s linear;
  z-index: 2;
}

@keyframes parent-main-nav-ripple {
  to {
    transform: scale(2.5);
    opacity: 0;
  }
}

.parent-main-nav-link:hover {
  background: rgba(255, 255, 255, 0.18);
  color: white;
  transform: translateY(-1px);
  border-radius: 16px;
  padding: 0.5rem 1.1rem;
  height: 38px;
  font-size: 0.93rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.2s cubic-bezier(0.21, 0.6, 0.35, 1);
}

.parent-main-nav-link.active {
  background: rgba(255, 255, 255, 0.25);
  color: white;
  font-weight: 600;
  position: relative;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.10);
  border-radius: 16px;
  padding: 0.5rem 1.1rem;
  height: 38px;
  font-size: 0.93rem;
  transition: all 0.2s cubic-bezier(0.21, 0.6, 0.35, 1);
}

.parent-main-nav-link.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #FBBF24, #F59E0B);
  animation: slideInFromLeft 0.3s ease-out;
}

/* Enhanced active state animation */
@keyframes slideInFromLeft {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

/* Quick Info Panel - Right side */
.parent-nav-quick-info {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 0.75rem 0;
}

.parent-quick-info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 20px;
  color: rgba(7, 18, 117, 0.9);
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.parent-quick-info-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
  color: white;
}

.parent-quick-info-item i {
  font-size: 0.9rem;
  opacity: 0.8;
}

.parent-quick-info-item.health-status {
  background: rgba(34, 197, 94, 0.15);
  border-color: rgba(34, 197, 94, 0.3);
  color: #86efac;
}

.parent-quick-info-item.health-status:hover {
  background: rgba(34, 197, 94, 0.25);
  color: #bbf7d0;
}

.parent-quick-info-item.health-status i {
  color: #22c55e;
  animation: parent-heartbeat 2s ease-in-out infinite;
}

@keyframes parent-heartbeat {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.parent-header-nav {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  width: 100% !important;
  height: 100% !important;
}

/* Logo - Modern fintech style */
.parent-header-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  text-decoration: none;
  margin-right: 3rem;
  flex: 0 0 auto;
  height: auto;
  transition: transform 0.3s ease;
}

.parent-header-logo:hover {
  transform: scale(1.02);
}

.parent-header-logo img {
  height: 35px;
  width: 35px;
  border-radius: 8px;
  transition: transform 0.3s ease;
  object-fit: cover;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.parent-logo-text {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-size: 1.5rem;
  font-weight: 700;
  color: #fff;
  letter-spacing: -0.5px;
  white-space: nowrap;
}

.parent-logo-text span {
  color: #BFDBFE;
  font-weight: 600;
}

/* Navigation Links - Enhanced with better active states */
.parent-nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 1rem;
  height: 100%;
  align-items: center;
}

.parent-nav-link {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
}

.parent-nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.parent-nav-link.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: 600;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
}

.parent-nav-link.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-radius: 8px;
  animation: activeGlow 0.3s ease-out;
}

@keyframes activeGlow {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.parent-header .parent-header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 0 0 auto;
  height: 100%;
}

.parent-header .parent-user-menu {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.parent-header .parent-header-actions .parent-notification-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  text-decoration: none;
  overflow: hidden;
  animation: parent-notification-float 3s ease-in-out infinite;
}

.parent-header .parent-header-actions .parent-user-greeting {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
  backdrop-filter: blur(10px);
}

.parent-header .parent-header-actions .parent-login-btn, 
.parent-header .parent-header-actions .parent-logout-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20px;
  color: white;
  font-size: 0.9rem;
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
  text-decoration: none;
  backdrop-filter: blur(10px);
}

.parent-notification-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-3px) scale(1.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  animation: parent-notification-glow 1.5s ease-in-out infinite;
}

.parent-notification-btn:active {
  transform: translateY(-1px) scale(1.05);
  transition: all 0.1s ease;
}

/* Ripple effect on click */
.parent-notification-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
  z-index: 1;
}

.parent-notification-btn:active::before {
  width: 60px;
  height: 60px;
  opacity: 0;
}

.parent-notification-btn i {
  font-size: 1rem;
  color: white;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.parent-notification-btn:hover i {
  transform: scale(1.1);
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.parent-notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 14px;
  height: 14px;
  background: #FF6B7C;
  border-radius: 50%;
  border: 2px solid #015C92;
  animation: parent-pulse-badge 2s infinite;
}

@keyframes parent-pulse-badge {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 107, 124, 0.7);
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 5px rgba(255, 107, 124, 0);
  }
  
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 107, 124, 0);
  }
}

/* Enhanced Notification Button Animations */
@keyframes parent-notification-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-2px);
  }
}

@keyframes parent-notification-glow {
  0%, 100% {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25), 0 0 20px rgba(255, 255, 255, 0.1);
  }
  50% {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25), 0 0 30px rgba(255, 255, 255, 0.3);
  }
}

@keyframes parent-notification-shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

/* Enhanced badge animation */
.parent-notification-btn:hover .parent-notification-badge {
  animation: parent-notification-shake 0.5s ease-in-out infinite;
}

/* Focus state for accessibility */
.parent-notification-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
  transform: translateY(-2px) scale(1.05);
}

/* Enhanced pulse animation for badge when hovered */
.parent-notification-btn:hover .parent-notification-badge {
  animation: parent-pulse-badge-enhanced 1s infinite, parent-notification-shake 0.5s ease-in-out infinite;
}

@keyframes parent-pulse-badge-enhanced {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 107, 124, 0.7);
  }
  
  50% {
    transform: scale(1.2);
    box-shadow: 0 0 0 8px rgba(255, 107, 124, 0);
  }
  
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 107, 124, 0);
  }
}

.parent-header .parent-header-actions .parent-user-greeting i {
  font-size: 1.2rem;
  color: #BFDBFE;
}

.parent-header .parent-header-actions .parent-login-btn {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.parent-header .parent-header-actions .parent-logout-btn {
  background: rgba(255, 107, 124, 0.2);
  border-color: rgba(255, 107, 124, 0.4);
}

/* Mobile Menu Toggle - Hidden by default */
.parent-mobile-menu-toggle {
  display: none;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
}

.parent-mobile-menu-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
}

@media (max-width: 1200px) {
  .parent-header .parent-container {
    padding: 0 1.5rem;
  }
  
  .parent-nav-wrapper {
    padding: 0 1.5rem;
  }
  
  .parent-main-nav-link {
    padding: 0 1rem;
  }
  
  .parent-nav-list {
    gap: 0.75rem;
  }
  
  .parent-nav-link {
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
  }
  
  .parent-nav-quick-info {
    gap: 1rem;
  }
  
  .parent-quick-info-item {
    padding: 0.4rem 0.6rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 1024px) {
  .parent-quick-info-item span {
    display: none;
  }
  
  .parent-quick-info-item {
    width: 36px;
    height: 36px;
    justify-content: center;
    padding: 0;
  }
}

/* Desktop Navigation - Hide mobile menu toggle on desktop */
@media (min-width: 993px) {
  .parent-mobile-menu-toggle {
    display: none !important;
  }
  
  .parent-nav-list {
    display: flex !important;
    position: static !important;
    background: none !important;
    flex-direction: row !important;
    padding: 0 !important;
    transform: none !important;
    visibility: visible !important;
  }
}

/* Mobile and Tablet Navigation */
@media (max-width: 992px) {
  .parent-header {
    min-height: 70px;
  }
  
  .parent-header-navigation {
    display: none;
  }
  
  .parent-header-logo {
    margin-right: 1rem;
  }
  
  .parent-header-logo .parent-logo-text {
    display: none;
  }
  
  .parent-mobile-menu-toggle {
    display: flex !important;
  }
  
  .parent-nav-list {
    position: fixed;
    top: 70px;
    left: 0;
    width: 100%;
    background: linear-gradient(135deg, rgba(1, 92, 146, 0.95) 0%, rgba(45, 130, 181, 0.95) 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    flex-direction: column;
    padding: 1rem 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    transform: translateY(-100%);
    visibility: hidden;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 999;
    gap: 0;
  }
  
  .parent-nav-list.active {
    transform: translateY(0);
    visibility: visible;
    opacity: 1;
  }
  
  .parent-nav-list li {
    width: 100%;
  }
  
  .parent-nav-link {
    display: block;
    width: 100%;
    padding: 1rem 2rem;
    text-align: left;
    border-radius: 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .parent-nav-link.active {
    background: rgba(255, 255, 255, 0.25);
    border-left: 4px solid #FBBF24;
  }
}

@media (max-width: 768px) {
  .parent-header .parent-container {
    padding: 0 1rem;
  }
  
  .parent-nav-wrapper {
    padding: 0 1rem;
  }
  
  .parent-main-nav-link {
    padding: 0 0.75rem;
    font-size: 0.9rem;
  }
  
  .parent-header-logo img {
    height: 30px;
    width: 30px;
  }
  
  .parent-logo-text {
    font-size: 1.2rem;
  }
  
  .parent-logo-text span {
    display: none;
  }
  
  .parent-header .parent-header-actions {
    gap: 0.5rem;
  }
  
  .parent-header .parent-header-actions .parent-login-btn, 
  .parent-header .parent-header-actions .parent-logout-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .parent-header .parent-container {
    padding: 0 0.75rem;
  }
  
  .parent-header .parent-header-actions .parent-notification-btn {
    width: 36px;
    height: 36px;
  }
  
  .parent-login-btn span, .parent-logout-btn span {
    display: none;
  }
  
  .parent-login-btn i, .parent-logout-btn i {
    margin-right: 0;
  }
  
  .parent-header .parent-header-actions .parent-user-greeting span {
    display: none;
  }
}

/* Hover Effects */
.parent-header .parent-header-actions .parent-notification-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-3px) scale(1.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  animation: parent-notification-glow 1.5s ease-in-out infinite;
}

.parent-header .parent-header-actions .parent-user-greeting:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

.parent-header .parent-header-actions .parent-login-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
}

.parent-header .parent-header-actions .parent-logout-btn:hover {
  background: rgba(255, 107, 124, 0.3);
  transform: translateY(-1px);
}