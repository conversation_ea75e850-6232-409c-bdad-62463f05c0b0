/* Vaccine List View - Multi-Theme Support */
.reports-vaccine-list-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Theme-specific backgrounds */
.reports-vaccine-list-container.theme-blue {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.reports-vaccine-list-container.theme-green {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

.reports-vaccine-list-container.theme-purple {
  background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
}

.reports-vaccine-list-container.theme-orange {
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

.reports-vaccine-list-container.theme-teal {
  background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
}

/* Loading Section */
.reports-vaccine-loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 70vh;
  color: #64748b;
  gap: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.08);
  border: 1px solid #e2e8f0;
  padding: 40px;
}

.reports-vaccine-loading-section p {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

/* Header Styles */
.reports-vaccine-header h2{
  color: white;
  font-size: 32px;
  font-weight: 700;
  margin: 0;
}

.reports-vaccine-header p{
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  font-weight: 500;
  margin: 8px 0 0 0;
}

.reports-vaccine-header {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  color: white;
  box-shadow: 0 8px 30px rgba(59, 130, 246, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.reports-vaccine-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
}

.reports-vaccine-header-actions {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 15px;
  position: relative;
  z-index: 2;
}

.reports-vaccine-back-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 10px 15px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  position: relative;
  z-index: 10;
  pointer-events: auto;
  user-select: none;
  outline: none;
}

.reports-vaccine-back-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.reports-vaccine-back-button:focus {
  background: rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

.reports-vaccine-back-button:active {
  transform: translateY(0);
  background: rgba(255, 255, 255, 0.4);
}

/* Custom back button for vaccine header */
.reports-vaccine-back-button-custom {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 12px 18px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 15px;
  font-weight: 500;
  position: relative;
  z-index: 10;
  pointer-events: auto;
  user-select: none;
  outline: none;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.reports-vaccine-back-button-custom:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.reports-vaccine-back-button-custom:focus {
  background: rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

.reports-vaccine-back-button-custom:active {
  transform: translateY(0);
  background: rgba(255, 255, 255, 0.4);
}

.reports-vaccine-back-button-custom i {
  font-size: 14px;
  transition: transform 0.2s ease;
}

.reports-vaccine-back-button-custom:hover i {
  transform: translateX(-2px);
}

.reports-vaccine-header h2 {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
}

.reports-vaccine-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
}

/* Statistics Cards */
.reports-vaccine-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.reports-vaccine-stat-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
  border-left: 4px solid #e9ecef;
}

.reports-vaccine-stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.reports-vaccine-stat-card.reports-vaccine-total {
  border-left-color: #4a90e2;
}

.reports-vaccine-stat-card.reports-vaccine-active {
  border-left-color: #28a745;
}

.reports-vaccine-stat-card.reports-vaccine-multi-dose {
  border-left-color: #ffc107;
}

.reports-vaccine-stat-card.reports-vaccine-age-groups {
  border-left-color: #6f42c1;
}

.reports-vaccine-stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.reports-vaccine-total .reports-vaccine-stat-icon {
  background: linear-gradient(135deg, #4a90e2, #357abd);
}

.reports-vaccine-active .reports-vaccine-stat-icon {
  background: linear-gradient(135deg, #28a745, #20c997);
}

.reports-vaccine-multi-dose .reports-vaccine-stat-icon {
  background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.reports-vaccine-age-groups .reports-vaccine-stat-icon {
  background: linear-gradient(135deg, #6f42c1, #e83e8c);
}

.reports-vaccine-stat-content h3 {
  margin: 0 0 5px 0;
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
}

.reports-vaccine-stat-content p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
}

/* Filters Section */
.reports-vaccine-filters {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
}

.reports-vaccine-filter-group {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 20px;
  align-items: end;
}

.reports-vaccine-search-box {
  position: relative;
}

.reports-vaccine-search-box i {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 16px;
}

.reports-vaccine-search-box input {
  width: 100%;
  padding: 12px 15px 12px 45px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.reports-vaccine-search-box input:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.reports-vaccine-filter-select {
  padding: 12px 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reports-vaccine-filter-select:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.reports-vaccine-results-count {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
}

/* Error Message */
.reports-vaccine-error-message {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 4px 20px rgba(255, 107, 107, 0.3);
}

.reports-vaccine-error-message i {
  font-size: 20px;
}

.reports-vaccine-error-message span {
  flex: 1;
  font-size: 16px;
}

.reports-vaccine-error-message button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reports-vaccine-error-message button:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Table Styles */
.reports-vaccine-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.reports-vaccine-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.reports-vaccine-table thead {
  background: linear-gradient(135deg, #2c3e50, #34495e);
  color: white;
}

.reports-vaccine-table th {
  padding: 18px 15px;
  text-align: left;
  font-weight: 600;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 2px solid #1a252f;
}

.reports-vaccine-table-row {
  transition: all 0.3s ease;
  border-bottom: 1px solid #f8f9fa;
}

.reports-vaccine-table-row:hover {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  transform: scale(1.01);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.reports-vaccine-table td {
  padding: 18px 15px;
  vertical-align: middle;
}

.reports-vaccine-table-stt {
  font-weight: 600;
  color: #6c757d;
  width: 60px;
  text-align: center;
}

.reports-vaccine-table-name {
  min-width: 300px;
}

.reports-vaccine-name-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.reports-vaccine-item-name {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.reports-vaccine-item-description {
  font-size: 13px;
  color: #6c757d;
  line-height: 1.4;
}

.reports-vaccine-table-age-range {
  min-width: 120px;
}

.reports-vaccine-age-badge {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  color: #1565c0;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: inline-block;
  border: 1px solid #90caf9;
}

.reports-vaccine-table-dose-count {
  min-width: 80px;
  text-align: center;
}

.reports-vaccine-dose-badge {
  background: linear-gradient(135deg, #fff3e0, #ffe0b2);
  color: #ef6c00;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: inline-block;
  border: 1px solid #ffcc02;
}

.reports-vaccine-table-interval {
  min-width: 100px;
  text-align: center;
  font-weight: 500;
  color: #495057;
}

.reports-vaccine-table-status {
  min-width: 130px;
}

.reports-vaccine-status-badge {
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  border: 1px solid;
}

.reports-vaccine-status-badge.reports-vaccine-active {
  background: linear-gradient(135deg, #d4edda, #c3e6cb);
  color: #155724;
  border-color: #b8daff;
}

.reports-vaccine-status-badge.reports-vaccine-inactive {
  background: linear-gradient(135deg, #f8d7da, #f5c6cb);
  color: #721c24;
  border-color: #f1b0b7;
}

.reports-vaccine-table-actions {
  width: 100px;
  text-align: center;
}

.reports-vaccine-action-button {
  background: linear-gradient(135deg, #4a90e2, #357abd);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

.reports-vaccine-action-button:hover {
  transform: translateY(-2px) scale(1.1);
  box-shadow: 0 4px 15px rgba(74, 144, 226, 0.4);
  background: linear-gradient(135deg, #357abd, #2968a3);
}

/* No Data State */
.reports-vaccine-no-data {
  background: white;
  border-radius: 12px;
  padding: 60px 30px;
  text-align: center;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
  color: #6c757d;
}

.reports-vaccine-no-data i {
  margin-bottom: 20px;
  color: #dee2e6;
}

.reports-vaccine-no-data h3 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 24px;
}

.reports-vaccine-no-data p {
  margin: 0;
  font-size: 16px;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .reports-vaccine-filter-group {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .reports-vaccine-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Admin History Toolbar */
.admin-history-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  gap: 24px;
  flex-wrap: wrap;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 24px 28px;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* Admin Secondary Toolbar */
.admin-secondary-toolbar {
  margin-bottom: 32px;
  padding: 16px 24px;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.admin-filter-row {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.admin-search-filter-group {
  display: flex;
  gap: 20px;
  flex: 1;
  min-width: 320px;
}

.admin-search-box {
  position: relative;
  flex: 2;
  max-width: 420px;
}

.admin-search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #3b82f6;
  font-size: 1.1rem;
  z-index: 1;
}

.admin-search-box input {
  width: 100%;
  padding: 14px 20px 14px 44px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  background: #ffffff;
  color: #1e293b;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-search-box input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.admin-search-box input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.admin-filter-dropdown {
  position: relative;
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 200px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-filter-dropdown:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.admin-filter-icon {
  position: absolute;
  left: 16px;
  color: #3b82f6;
  font-size: 1.1rem;
  z-index: 1;
}

.admin-filter-dropdown select {
  padding: 14px 20px 14px 44px;
  border: none;
  border-radius: 12px;
  background: transparent;
  font-size: 0.875rem;
  min-width: 200px;
  cursor: pointer;
  color: #1e293b;
  font-weight: 600;
}

.admin-filter-dropdown select:focus {
  outline: none;
}

.admin-toolbar-buttons {
  display: flex;
  gap: 16px;
  align-items: center;
}

.admin-results-count {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.admin-refresh-button {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 14px 24px;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  font-size: 0.875rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.admin-refresh-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
}

.admin-refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

@media (max-width: 768px) {
  .reports-vaccine-list-container {
    padding: 15px;
  }
  
  .reports-vaccine-header {
    padding: 20px;
  }
  
  .reports-vaccine-header-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .reports-vaccine-header h2 {
    font-size: 24px;
  }
  
  .reports-vaccine-stats {
    grid-template-columns: 1fr;
  }
  
  .reports-vaccine-stat-card {
    padding: 20px;
  }
  
  .reports-vaccine-filters {
    padding: 20px;
  }
  
  .reports-vaccine-table-container {
    overflow-x: auto;
  }
  
  .reports-vaccine-table {
    min-width: 800px;
  }
  
  .reports-vaccine-table th,
  .reports-vaccine-table td {
    padding: 12px 10px;
  }
  
  .reports-vaccine-item-name {
    font-size: 14px;
  }
  
  .reports-vaccine-item-description {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .reports-vaccine-header h2 {
    font-size: 20px;
  }
  
  .reports-vaccine-stat-content h3 {
    font-size: 24px;
  }
  
  .reports-vaccine-stat-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }
  
  .reports-vaccine-no-data {
    padding: 40px 20px;
  }
  
  .reports-vaccine-no-data h3 {
    font-size: 20px;
  }
  
  .reports-vaccine-no-data p {
    font-size: 14px;
  }
}

/* Pagination Styles */
.admin-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
  margin-top: 0;
}

.admin-pagination-info {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.admin-pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.admin-pagination-btn {
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  background: white;
  color: #64748b;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
  min-width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.admin-pagination-btn:hover:not(:disabled) {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #475569;
}

.admin-pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.admin-pagination-btn.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.admin-pagination-btn.active:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.admin-pagination-ellipsis {
  padding: 8px 4px;
  color: #9ca3af;
  font-size: 0.875rem;
}

@media (max-width: 768px) {
  .admin-pagination {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
  }

  .admin-pagination-controls {
    flex-wrap: wrap;
    justify-content: center;
  }

  /* Responsive Toolbar */
  .admin-history-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .admin-search-filter-group {
    flex-direction: column;
    gap: 12px;
  }

  .admin-filter-row {
    flex-direction: column;
    gap: 12px;
  }

  .admin-filter-dropdown {
    min-width: auto;
    width: 100%;
  }

  .admin-toolbar-buttons {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .reports-vaccine-container {
    padding: 16px;
  }

  .reports-vaccine-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .reports-vaccine-card {
    padding: 16px;
  }
}
