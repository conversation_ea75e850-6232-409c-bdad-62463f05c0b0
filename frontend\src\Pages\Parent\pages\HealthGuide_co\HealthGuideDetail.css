.article-detail-container {
  max-width: 940px;
  margin: 0 auto;
  padding: 30px 20px;
  font-family: '<PERSON>o', 'Segoe UI', sans-serif;
}

/* Override handled by global.css */

/* Demo banner với animation nhẹ nhàng */
.demo-banner {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
  padding: 14px 18px;
  border-radius: 8px;
  margin-bottom: 25px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.04);
  animation: fadeIn 0.5s ease;
}

.demo-banner i {
  color: #f39c12;
  font-size: 1.2rem;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Loading styles với hiệu ứng đẹp mắt */
.loading-container.article-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  text-align: center;
  background-color: #f8f9fa;
  background-image: linear-gradient(120deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 16px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
  margin: 20px auto;
  max-width: 850px;
  padding: 40px;
  position: relative;
  overflow: hidden;
}

.loading-container.article-loading::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-image: linear-gradient(120deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
  transform: translateX(-100%);
}

@keyframes shimmer {
  100% { transform: translateX(100%); }
}

.spinner-container {
  margin-bottom: 25px;
  position: relative;
}

.spinner-border {
  display: inline-block;
  width: 65px;
  height: 65px;
  border: 6px solid rgba(52, 152, 219, 0.15);
  border-radius: 50%;
  border-top-color: #3498db;
  animation: spin 1s cubic-bezier(0.5, 0.1, 0.5, 1) infinite;
  box-sizing: border-box;
  box-shadow: 0 0 10px rgba(52, 152, 219, 0.1);
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-container p {
  font-size: 1.15rem;
  color: #3d4852;
  margin-top: 18px;
  font-weight: 500;
  letter-spacing: 0.3px;
  position: relative;
}

.loading-container p::after {
  content: "...";
  position: absolute;
  animation: ellipsis 1.5s infinite;
}

@keyframes ellipsis {
  0% { content: "."; }
  33% { content: ".."; }
  66% { content: "..."; }
  100% { content: "."; }
}

/* Article not found - Hiệu ứng mới */
.article-not-found {
  max-width: 600px;
  margin: 70px auto;
  text-align: center;
  background-color: #fff;
  padding: 45px 30px;
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.07);
  border-top: 5px solid #e74c3c;
  transition: all 0.3s ease;
}

.article-not-found:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.article-not-found i {
  font-size: 4rem;
  color: #e74c3c;
  margin-bottom: 20px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.article-not-found h2 {
  font-size: 2rem;
  color: #2d3748;
  margin-bottom: 15px;
  font-weight: 600;
}

.article-not-found p {
  color: #4a5568;
  margin-bottom: 30px;
  font-size: 1.15rem;
  line-height: 1.6;
}

.btn-back {
  display: inline-flex;
  align-items: center;
  padding: 12px 24px;
  background-color: #3498db;
  color: white;
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  letter-spacing: 0.3px;
  box-shadow: 0 4px 6px rgba(52, 152, 219, 0.25);
}

.btn-back:hover {
  background-color: #2980b9;
  transform: translateY(-3px);
  box-shadow: 0 7px 14px rgba(52, 152, 219, 0.3);
}

.btn-back:active {
  transform: translateY(-1px);
}

.btn-back i {
  margin-right: 10px;
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.btn-back:hover i {
  transform: translateX(-4px);
}

/* Article Header - Kiểu dáng mới */
.article-detail-header {
  margin-bottom: 35px;
  padding-bottom: 30px;
  border-bottom: 1px solid #edf2f7;
}

.article-detail-header .btn-back {
  margin-bottom: 25px;
  background-color: #f3f4f6;
  color: #4b5563;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 10px 20px;
}

.article-detail-header .btn-back:hover {
  background-color: #e5e7eb;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
}

.article-meta {
  display: flex;
  flex-wrap: wrap;
  margin: 15px 0;
  gap: 15px;
  align-items: center;
}

.article-category,
.article-date,
.article-reading-time {
  display: inline-flex;
  align-items: center;
  font-size: 0.95rem;
  color: #4b5563;
}

.article-meta i {
  margin-right: 7px;
  color: #64748b;
}

.article-category {
  background: linear-gradient(120deg, #edf2f7 0%, #e2e8f0 100%);
  padding: 5px 14px;
  border-radius: 20px;
  color: #3182ce;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
}

.article-category:hover {
  transform: scale(1.05);
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.05);
}

.article-title {
  font-size: 2.5rem;
  margin: 20px 0;
  line-height: 1.2;
  color: #1a202c;
  font-weight: 700;
  letter-spacing: -0.5px;
}

.article-author {
  display: flex;
  align-items: center;
  margin-top: 15px;
  font-style: italic;
  color: #4a5568;
  background: #f8fafc;
  padding: 8px 15px;
  border-radius: 30px;
  display: inline-flex;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
}

.article-author i {
  margin-right: 8px;
  color: #4a5568;
}

/* Article Content - Cải tiến */
.article-main-image {
  margin: 0 -20px 30px -20px;
  overflow: hidden;
  position: relative;
  height: 400px;
}

.article-main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: transform 0.7s ease;
  filter: brightness(0.9);
}

.article-main-image::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40%;
  background-image: linear-gradient(to top, rgba(0, 0, 0, 0.5), transparent);
}

.article-main-image:hover img {
  transform: scale(1.03);
  filter: brightness(1);
}

.article-summary {
  font-size: 1.25rem;
  font-weight: 500;
  margin: 0 0 35px 0;
  padding: 25px;
  background-color: #f7fafc;
  border-left: 5px solid #3182ce;
  border-radius: 0 12px 12px 0;
  color: #2d3748;
  line-height: 1.7;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 5;
  margin-top: -50px;
  width: 90%;
  margin-left: auto;
  margin-right: auto;
  background-color: white;
}

.article-body {
  line-height: 1.8;
  color: #2d3748;
  font-size: 1.1rem;
  margin-bottom: 40px;
}

.article-body h3 {
  margin-top: 40px;
  margin-bottom: 20px;
  color: #2c5282;
  font-size: 1.7rem;
  font-weight: 600;
  position: relative;
  padding-bottom: 12px;
}

.article-body h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: #3182ce;
  border-radius: 3px;
}

.article-body h4 {
  margin-top: 30px;
  margin-bottom: 15px;
  color: #2c5282;
  font-size: 1.35rem;
  font-weight: 600;
}

.article-body p {
  margin-bottom: 25px;
}

.article-body ul, 
.article-body ol {
  margin-bottom: 25px;
  padding-left: 25px;
}

.article-body li {
  margin-bottom: 12px;
}

.article-body a {
  color: #3182ce;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.3s ease;
  font-weight: 500;
}

.article-body a:hover {
  color: #2c5282;
  border-bottom: 1px solid #2c5282;
}

.article-body img {
  max-width: 100%;
  border-radius: 8px;
  margin: 25px 0;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.article-body blockquote {
  border-left: 4px solid #3182ce;
  padding: 15px 20px;
  margin: 25px 0;
  background-color: #f7fafc;
  border-radius: 0 8px 8px 0;
  font-style: italic;
  color: #4a5568;
}

/* Article Tags - Thiết kế mới */
.article-tags {
  margin-top: 50px;
  padding-top: 30px;
  border-top: 1px solid #e2e8f0;
}

.article-tags h4 {
  font-size: 1.15rem;
  margin-bottom: 18px;
  color: #4a5568;
  font-weight: 600;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.tag {
  font-size: 0.9rem;
  background-color: #edf2f7;
  padding: 7px 15px;
  border-radius: 20px;
  color: #4a5568;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
}

.tag:hover {
  background-color: #e2e8f0;
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06);
  color: #3182ce;
}

/* Related Articles - Thiết kế card mới */
.related-articles {
  margin-top: 60px;
  padding-top: 40px;
  border-top: 1px solid #e2e8f0;
}

.related-articles h3 {
  font-size: 1.6rem;
  margin-bottom: 25px;
  color: #2d3748;
  font-weight: 600;
  position: relative;
  padding-bottom: 15px;
  text-align: center;
}

.related-articles h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: #3182ce;
  border-radius: 3px;
}

.related-articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 25px;
}

.related-article-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  background-color: white;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.related-article-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.12);
}

.related-article-card a {
  color: inherit;
  text-decoration: none;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.related-article-image {
  height: 170px;
  overflow: hidden;
  position: relative;
}

.related-article-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 70%, rgba(0,0,0,0.6) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.related-article-card:hover .related-article-image::after {
  opacity: 1;
}

.related-article-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.related-article-card:hover .related-article-image img {
  transform: scale(1.08);
}

.related-article-content {
  padding: 20px;
  background-color: white;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.related-article-content h4 {
  font-size: 1.1rem;
  margin-bottom: 12px;
  line-height: 1.4;
  color: #2d3748;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 2.8em;
  font-weight: 600;
  transition: color 0.3s ease;
}

.related-article-card:hover h4 {
  color: #3182ce;
}

.related-summary {
  font-size: 0.95rem;
  color: #4a5568;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 2.85em;
  line-height: 1.5;
  flex-grow: 1;
}

.related-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.85rem;
  color: #718096;
  margin-top: auto;
  padding-top: 15px;
  border-top: 1px solid #f1f5f9;
}

.related-article-date {
  display: flex;
  align-items: center;
}

.related-article-date i {
  margin-right: 6px;
}

.related-article-category {
  font-style: italic;
  color: #3182ce;
  font-weight: 500;
}

/* Cải thiện responsive */
@media (max-width: 768px) {
  .article-detail-container {
    padding: 20px 15px;
  }
  
  .article-title {
    font-size: 2rem;
  }
  
  .article-summary {
    font-size: 1.15rem;
    padding: 20px;
    width: 95%;
    margin-top: -40px;
  }
  
  .article-body {
    font-size: 1.05rem;
  }
  
  .article-body h3 {
    font-size: 1.5rem;
  }
  
  .article-main-image {
    height: 350px;
  }
  
  .related-articles-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
  }
  
  .loading-container.article-loading {
    height: 350px;
    padding: 35px;
  }
  
  .spinner-border {
    width: 55px;
    height: 55px;
  }
}

@media (max-width: 576px) {
  .article-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .article-title {
    font-size: 1.7rem;
    margin: 15px 0;
  }
  
  .article-main-image {
    height: 280px;
  }
  
  .article-summary {
    font-size: 1.05rem;
    padding: 18px;
    margin-top: -30px;
  }
  
  .article-body h3 {
    font-size: 1.35rem;
  }
  
  .article-body {
    font-size: 1rem;
  }
  
  .article-not-found {
    padding: 30px 20px;
  }
  
  .loading-container.article-loading {
    height: 280px;
    padding: 25px;
  }
  
  .spinner-border {
    width: 45px;
    height: 45px;
    border-width: 5px;
  }
  
  .loading-container p {
    font-size: 1rem;
  }
  
  .article-tags h4 {
    text-align: center;
  }
  
  .tags-list {
    justify-content: center;
  }
  
  .related-articles-grid {
    grid-template-columns: 1fr;
  }
  
  .related-article-image {
    height: 200px;
  }
}