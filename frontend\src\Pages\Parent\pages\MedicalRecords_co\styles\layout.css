/* ================================================
   LAYOUT - MINIMALIST MODERN - Fixed for Parent Layout
   Clean Structure • Simple Grids • White Space
   ================================================ */

/* Main Container - Compatible with ParentLayout */
.medical-container {
  min-height: 100vh;
  background-color: var(--color-gray-50);
  animation: fadeIn 0.3s ease-out;
  width: 1200px; /* Force exact width */
  max-width: 100%; /* Allow responsive on smaller screens */
  margin: 0 auto; /* Center the container */
  box-sizing: border-box;
  padding-top: 0; /* Remove padding top since ParentLayout handles it */
}

/* Header - Adjust for Parent Layout */
.medical-header {
  background-color: var(--color-white);
  border-bottom: 1px solid var(--color-gray-200);
  position: relative;
  box-shadow: var(--shadow-sm);
  margin: 0;
  width: 100%;
  box-sizing: border-box;
  border-radius: 15%;
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-6) var(--space-4);
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  background-color: var(--color-primary);
  color: var(--color-white);
  border-radius: var(--radius-lg);
  margin-top: var(--space-4);
}

/* Override when inside parent-content-wrapper - Match container width */
.parent-content-wrapper .header-content {
  max-width: 1200px; /* Match medical-container max-width */
  width: 100%; /* Ensure full width */
  margin: var(--space-4) auto 0 auto;
}

.header-left {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.header-icon {
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--color-white);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
  flex-shrink: 0;
  margin-bottom: var(--space-2);
}

.header-info h1 {
  font-size: var(--text-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-white);
  margin-bottom: var(--space-2);
  text-align: center;
}

.header-info p {
  font-size: var(--text-sm);
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  text-align: center;
}

/* Split header actions to prevent overlap */
.header-actions1 {
  position: absolute;
  top: var(--space-4);
  left: var(--space-4);
  display: flex;
  align-items: center;
  z-index: 10;
}

.header-actions2 {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  display: flex;
  align-items: center;
  z-index: 10;
}

.header-actions1 .back-btn,
.header-actions2 .back-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--color-white);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  font-size: var(--text-sm);
  transition: all var(--transition);
  border: 1px solid rgba(255, 255, 255, 0.3);
  white-space: nowrap;
  text-decoration: none;
  flex-shrink: 0;
}

.header-actions1 .back-btn:hover,
.header-actions2 .back-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
  color: var(--color-white);
  transform: translateY(-1px);
}

.header-actions1 .refresh-btn,
.header-actions2 .refresh-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--color-white);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  font-size: var(--text-sm);
  transition: all var(--transition);
  border: 1px solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
  white-space: nowrap;
  flex-shrink: 0;
}

.header-actions1 .refresh-btn:hover:not(:disabled),
.header-actions2 .refresh-btn:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.header-actions1 .refresh-btn:disabled,
.header-actions2 .refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.header-actions1 .refresh-btn.refreshing,
.header-actions2 .refresh-btn.refreshing {
  background-color: var(--color-gray-400);
  border-color: var(--color-gray-400);
}

.header-actions1 .refresh-btn .spin,
.header-actions2 .refresh-btn .spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Main Content - Compatible with ParentLayout */
.medical-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-6) 0; /* Remove side padding to match container */
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
  width: 100%;
  box-sizing: border-box;
}

/* Override when inside parent-content-wrapper - Match container width exactly */
.parent-content-wrapper .medical-main {
  max-width: 1200px; /* Match medical-container max-width */
  width: 1200px; /* Force exact width match */
  margin: 0 auto;
  padding: var(--space-4) 0; /* Remove side padding */
}

/* Student Section */
.student-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
  width: 100%;
  box-sizing: border-box;
}

.chonhocsinhtabparent {
  /* background-color: var(--color-primary-50); */
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-primary-100);
  width: 100%;
  box-sizing: border-box;
  
}

.parent-selector-header {
  margin-bottom: var(--space-6);
  text-align: center;
  justify-content: center;
  align-items: center; /* căn giữa theo chiều ngang */
  display: flex;
  flex-direction: column;
  padding-left: 0; /* bỏ lệnh đẩy sang phải */
}

.parent-selector-header h2 {
  font-size: var(--text-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin-bottom: var(--space-2);
}

.parent-selector-header p {
  font-size: var(--text-sm);
  color: var(--color-gray-500);
  margin: 0;
}


.selector-content {
  width: 100%;
  display: flex;
  justify-content: center;
}

.select-wrapper {
  position: relative;
  width: 100%;
  max-width: 500px;
}

.chonhocsinhtabparent-select,
.selectstudentfix {
  width: 100%;
  padding: var(--space-4);
  padding-right: var(--space-12);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  background-color: var(--color-white);
  font-size: var(--text-base);
  color: var(--color-gray-700);
  appearance: none;
  cursor: pointer;
  transition: all var(--transition);
  box-sizing: border-box;
  
}

.chonhocsinhtabparent-select:focus,
.selectstudentfix:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-light);
  outline: none;
}

.chonhocsinhtabparent-select:disabled,
.selectstudentfix:disabled {
  background-color: var(--color-gray-100);
  cursor: not-allowed;
  opacity: 0.6;
}

.select-arrow {
  position: absolute;
  right: var(--space-4);
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-gray-400);
  pointer-events: none;
  font-size: var(--text-sm);
}

/* Student Info Card - Fixed Layout */
.student-info {
  background: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-4);
  transition: all var(--transition);
  animation: slideUp 0.3s ease-out;
  width: 100%;
  box-sizing: border-box;
  min-height: 200px;
  flex-direction: row;
  justify-content: center;
}

.student-info .student-details {
  order: 1;
}

.student-info .student-avatar {
  order: 2;
}

.student-info:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
}

.student-avatar {
  width: 180px !important;
  height: 180px !important;
  background-color: var(--color-gray-100);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-gray-500);
  font-size: 3rem;
  flex-shrink: 0;
  overflow: hidden;
  position: relative;
}

.student-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--radius-lg);
}

.student-avatar .avatar-fallback {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.student-details {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  min-width: 0; /* Prevent flex item from overflowing */
  justify-content: center;
  max-width: 300px;
  width: 300px;
}

.student-details h3 {
  font-size: var(--text-2xl);
  font-weight: var(--font-weight-bold);
  margin: 0;
  color: var(--color-gray-900);
  text-align: left;
}

.student-meta {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex-wrap: wrap;
  max-width: 300px;
}

.meta-item {
  background-color: var(--color-primary-light);
  color: var(--color-primary);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
}

/* Content Section - Fixed Layout */
.content-section {
  background-color: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
}

.content-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--color-gray-200);
  background-color: var(--color-primary-50);
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  background-color: var(--color-white);
}

.content-header h2 {
  font-size: var(--text-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin: 0;
  text-align: center;
}

.content-body {
  padding: var(--space-6);
  min-height: 400px;
  width: 100%;
  box-sizing: border-box;
  overflow-x: auto; /* Handle overflow content */
}

/* Print Section */
.print-section {
  display: flex;
  justify-content: center;
  margin-top: var(--space-6);
  width: 100%;
  box-sizing: border-box;
}

.print-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4) var(--space-6);
  background-color: var(--color-white);
  color: var(--color-gray-700);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition);
  box-shadow: var(--shadow-sm);
}

.print-btn:hover {
  background-color: var(--color-gray-50);
  border-color: var(--color-gray-400);
  transform: translateY(-1px);
}

/* Loading & Error States */
.medical-loading,
.medical-error {
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8);
  width: 100%;
  box-sizing: border-box;
}

.loading-content,
.error-content {
  text-align: center;
  max-width: 400px;
  background-color: var(--color-white);
  padding: var(--space-8);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
  width: 100%;
  box-sizing: border-box;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-gray-200);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--space-4);
}

.error-icon {
  font-size: var(--text-3xl);
  color: var(--color-error);
  margin-bottom: var(--space-4);
}

.loading-content p,
.error-content p {
  color: var(--color-gray-500);
  margin-bottom: var(--space-6);
}

.error-content h2 {
  color: var(--color-gray-900);
  margin-bottom: var(--space-4);
}


.header-actions1 .back-btn,
.header-actions1 .refresh-btn,
.header-actions2 .back-btn,
.header-actions2 .refresh-btn {
  flex-shrink: 0;
  min-width: auto;
}

/* No additional fixes needed for button overlap - they're now separated */

/* Responsive Design - Improved */
@media (max-width: 1240px) {
  .medical-container {
    width: 100%;
    max-width: 100%;
    padding: 0 var(--space-4);
  }
  
  .medical-main,
  .parent-content-wrapper .medical-main {
    width: 100%;
    max-width: 100%;
    padding: var(--space-4);
  }
}

@media (max-width: 992px) {
  .medical-main {
    padding: var(--space-4);
    gap: var(--space-4);
  }
  
  .header-content {
    margin-top: var(--space-2);
    padding: var(--space-4);
  }
  
  .header-actions1,
  .header-actions2 {
    position: static;
    margin-top: var(--space-4);
    padding: 0;
  }
  
  .header-actions1 {
    margin-bottom: var(--space-2);
  }
  
  .header-left {
    margin-bottom: var(--space-2);
  }
}

@media (max-width: 768px) {
  .medical-main {
    padding: var(--space-4) var(--space-3);
    gap: var(--space-4);
  }
  
  .header-content {
    flex-direction: column;
    gap: var(--space-3);
    text-align: center;
    padding: var(--space-4) var(--space-3);
    margin-top: var(--space-2);
  }
  
  .header-left {
    flex-direction: column;
    gap: var(--space-3);
    margin-bottom: var(--space-2);
  }
  
  .content-header,
  .content-body {
    padding: var(--space-4);
  }
  
  .chonhocsinhtabparent {
    padding: var(--space-4);
  }
  
  .student-details {
    gap: var(--space-2);
  }
  
  .student-meta {
    gap: var(--space-2);
  }
  
  .header-actions1,
  .header-actions2 {
    flex-direction: column;
    gap: var(--space-2);
    align-items: center;
  }
  
  .header-actions1 .back-btn,
  .header-actions1 .refresh-btn,
  .header-actions2 .back-btn,
  .header-actions2 .refresh-btn {
    width: auto;
    min-width: 120px;
  }
}

@media (max-width: 480px) {
  .medical-main {
    padding: var(--space-3) var(--space-2);
    gap: var(--space-3);
  }
  
  .content-header,
  .content-body {
    padding: var(--space-3);
  }
  
  .chonhocsinhtabparent {
    padding: var(--space-3);
  }
  
  .header-info h1 {
    font-size: var(--text-xl);
  }
  
  .content-header h2 {
    font-size: var(--text-lg);
  }
  
  .chonhocsinhtabparent-select,
  .selectstudentfix {
    padding: var(--space-3);
    padding-right: var(--space-10);
    font-size: var(--text-sm);
  }
  
  .print-btn {
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-xs);
  }
  
  .student-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: var(--space-3);
    max-width: 300px !important;
  }
  
  .student-details {
    width: 100%;
    text-align: center;
    align-items: center;
  }
  
  .student-meta {
    justify-content: center;
    flex-wrap: wrap;
  }
}