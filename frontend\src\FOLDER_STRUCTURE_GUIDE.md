# 📁 Cấu trúc thư mục Frontend/src

Tài liệu này mô tả chức năng của từng folder chính trong thư mục `frontend/src` của hệ thống quản lý y tế trường học.

## 📂 **Pages**
**Chức năng:** Chứa tất cả các trang (pages) chính của ứng dụng
- Được chia theo vai trò người dùng: <PERSON><PERSON>, Nurse, Parent
- Mỗi subfolder chứa các component trang tương ứng với từng vai trò
- File `Login.jsx` - Trang đăng nhập chung cho tất cả người dùng

## 🖼️ **assets**
**Chức năng:** Lưu trữ tài nguyên tĩnh (static assets)
- Hình ảnh: logo, background, icons
- File backup và tài liệu liên quan đến hình ảnh
- Các file media khác như SVG, JPG, PNG

## 🧩 **components**
**Chức năng:** Chứ<PERSON> các React component tái sử dụng
- Component chung được dùng ở nhiều nơi trong ứng dụng
- Bao gồm: Modal, Loading Spinner, Search Box, Layout components
- Component bảo mật: ProtectedRoute, ErrorBoundary, OAuth handlers

## ⚙️ **config**
**Chức năng:** Cấu hình ứng dụng
- `apiConfig.js` - Cấu hình API endpoints và base URLs
- Quản lý environment variables
- Cấu hình kết nối backend

## 🔄 **context**
**Chức năng:** React Context providers cho state management
- `AuthContext.jsx` - Quản lý trạng thái xác thực người dùng
- `NotificationContext.jsx` - Quản lý thông báo toàn ứng dụng
- `StudentDataContext.jsx` - Quản lý dữ liệu học sinh
- `NurseContext` - Context riêng cho y tá

## 📊 **mockData**
**Chức năng:** Dữ liệu giả lập cho development và testing
- `students.js` - Dữ liệu học sinh mẫu
- `users.js` - Dữ liệu người dùng mẫu
- Sử dụng khi backend chưa sẵn sàng hoặc để test

## 🛣️ **routes**
**Chức năng:** Định nghĩa routing cho ứng dụng
- `AdminRoutes.jsx` - Routes dành cho Admin
- `NurseRoutes.jsx` - Routes dành cho Y tá
- `ParentRoutes.jsx` - Routes dành cho Phụ huynh
- `index.jsx` - Router chính tổng hợp tất cả routes

## 🌐 **services**
**Chức năng:** Xử lý logic nghiệp vụ và giao tiếp API
- `api.js` - Axios instance chính và interceptors
- `APIAdmin/` - Services riêng cho Admin
- `APINurse/` - Services riêng cho Y tá
- Các service chuyên biệt: authentication, notifications, medical data
- Session management và security wrappers

## 🎨 **styles**
**Chức năng:** Quản lý CSS và styling
- `global.css` - CSS toàn cục
- `reset.css` - CSS reset
- CSS fixes và conflict resolution
- Namespace management cho tránh xung đột CSS

## 🔧 **utils**
**Chức năng:** Utility functions và helper tools
- `sessionUtils.js` - Tiện ích quản lý session
- Compatibility checks và safety tests
- Các function hỗ trợ chung cho ứng dụng

## 📄 **Root Files**
- `App.jsx` - Component gốc của ứng dụng
- `App.css` - CSS cho App component
- `main.jsx` - Entry point chính
- `index.jsx` - File index

---

## 🏗️ **Kiến trúc tổng quan:**
```
src/
├── Pages/          # Giao diện người dùng theo vai trò
├── components/     # Component tái sử dụng
├── services/       # Logic nghiệp vụ & API
├── context/        # State management
├── routes/         # Điều hướng
├── config/         # Cấu hình
├── styles/         # Styling
├── utils/          # Tiện ích
├── assets/         # Tài nguyên tĩnh
└── mockData/       # Dữ liệu test
```

**Nguyên tắc tổ chức:**
- Phân chia theo vai trò người dùng (Admin/Nurse/Parent)
- Tách biệt logic nghiệp vụ và giao diện
- Component tái sử dụng được tập trung
- Cấu hình và utilities được tổ chức riêng biệt
