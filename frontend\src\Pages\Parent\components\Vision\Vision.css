/* School Medical Vision Component - Professional Blue Theme */
/* Prefix ALL selectors with "sm-" to prevent conflicts */

:root {
  --primary-blue: #015C92;
  --secondary-blue: #2D82B5;
  --accent-blue: #428CD4;
  --light-blue: #88CDF6;
  --lightest-blue: #BCE6FF;
  --blue-gradient: linear-gradient(135deg, #015C92 0%, #2D82B5 25%, #428CD4 50%, #88CDF6 75%, #BCE6FF 100%);
  --blue-gradient-secondary: linear-gradient(135deg, #428CD4 0%, #88CDF6 50%, #BCE6FF 100%);
}

/* Reset wrapper để vô hiệu hóa mọi style từ các file parent */
.sm-vision-reset-wrapper {
  display: block;
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box !important;
  position: relative !important;
  overflow-x: hidden !important;
  left: 0 !important;
  right: 0 !important;
}

/* Reset tất cả các thu<PERSON><PERSON> t<PERSON>h có thể gây ra vấn đề */
.sm-vision-reset-wrapper * {
  box-sizing: border-box;
}

/* Main container - full width with proper spacing */
.sm-vision-system {
  width: 100% !important;
  display: block !important;
  position: relative !important;
  margin: 0 !important;
  padding: 0 !important;
  left: 0 !important;
  right: 0 !important;
  box-sizing: border-box !important;
}

/* Outer container for background and positioning */
.sm-vision-outer {
  position: relative !important;
  width: 100% !important;
  padding: 100px 0 80px !important;
  margin: 0 !important;
  background: linear-gradient(135deg, #F0F9FF 0%, #E0F2FE 100%) !important;
  overflow: hidden !important;
  left: 0 !important;
  right: 0 !important;
  box-sizing: border-box !important;
}

/* Inner container for content - tăng độ đặc hiệu để ghi đè */
.sm-vision-system .sm-vision-container {
  width: 100% !important;
  max-width: 1320px !important;
  margin: 0 auto !important;
  padding: 0 20px !important;
  position: relative !important;
  z-index: 5 !important;
  display: block !important;
  left: auto !important;
  right: auto !important;
}

/* Content grid - sửa lỗi xéo lệch */
.sm-vision-system .sm-vision-content {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  gap: 40px !important;
  align-items: flex-start !important;
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  left: auto !important;
  right: auto !important;
  box-sizing: border-box !important;
}

/* Text column - sửa lỗi padding và margin */
.sm-vision-system .sm-vision-text {
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box !important;
}

/* Image column - sửa lỗi bố cục */
.sm-vision-system .sm-vision-media {
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box !important;
}

/* Reset all basic elements */
.sm-vision-system *,
.sm-vision-system *::before,
.sm-vision-system *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Ensure all headings use proper fonts */
.sm-vision-title,
.sm-vision-heading,
.sm-point-title {
  font-family: 'Be Vietnam Pro', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  font-weight: 700;
  color: #1e293b;
}

/* Ensure all paragraphs use proper fonts */
.sm-vision-description, 
.sm-point-description,
.sm-vision-conclusion p,
.sm-stat-label,
.sm-vision-quote p {
  font-family: 'Nunito', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* Header styling */
.sm-vision-header {
  text-align: center;
  margin-bottom: 60px;
  position: relative;
}

.sm-vision-badge {
  display: inline-block;
  padding: 8px 20px;
  background-color: rgba(1, 92, 146, 0.1);
  color: var(--primary-blue);
  border-radius: 30px;
  font-size: 0.95rem;
  font-weight: 600;
  margin-bottom: 20px;
  letter-spacing: 0.5px;
}

.sm-vision-title {
  font-size: 2.5rem;
  margin-bottom: 25px;
  line-height: 1.2;
}

.sm-title-gradient {
  background: var(--blue-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sm-title-underlined {
  position: relative;
  display: inline-block;
}

.sm-title-underlined::after {
  content: '';
  position: absolute;
  bottom: 8px;
  left: 0;
  width: 100%;
  height: 8px;
  background-color: rgba(1, 92, 146, 0.2);
  z-index: -1;
  transform: skewX(-5deg);
}

.sm-title-divider {
  height: 4px;
  width: 80px;
  background: var(--blue-gradient-secondary);
  margin: 0 auto 30px;
  border-radius: 2px;
}

/* Main content grid */
.sm-vision-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  align-items: flex-start;
}

/* Text column */
.sm-vision-text {
  box-sizing: border-box;
  width: 100%;
}

.sm-vision-heading {
  font-size: 1.75rem;
  margin-bottom: 25px;
  line-height: 1.3;
}

.sm-vision-highlight {
  color: var(--primary-blue);
  position: relative;
}

.sm-vision-highlight::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background-color: rgba(1, 92, 146, 0.15);
  z-index: -1;
}

.sm-vision-description {
  font-size: 1.05rem;
  line-height: 1.8;
  color: #475569;
  margin-bottom: 30px;
}

/* Quote box */
.sm-vision-quote {
  background-color: #ffffff;
  border-left: 4px solid var(--primary-blue);
  border-radius: 0 10px 10px 0;
  padding: 20px 25px;
  margin: 30px 0;
  box-shadow: 0 10px 30px rgba(1, 92, 146, 0.08);
  position: relative;
  font-style: italic;
  color: #334155;
  display: flex;
  align-items: center;
}

.sm-vision-quote p {
  flex: 1;
  margin: 0 15px;
  font-size: 1.1rem;
}

.sm-vision-quote i {
  color: var(--primary-blue);
  opacity: 0.5;
  font-size: 1.2rem;
}

/* Vision points grid - Professional Blue variations */
.sm-vision-points {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 25px;
  margin: 40px 0 30px;
}

.sm-vision-point {
  display: flex;
  align-items: flex-start;
  padding: 0;
  margin: 0;
}

.sm-vision-point:nth-child(1) .sm-point-icon {
  background-color: var(--primary-blue);
}

.sm-vision-point:nth-child(2) .sm-point-icon {
  background-color: var(--secondary-blue);
}

.sm-vision-point:nth-child(3) .sm-point-icon {
  background-color: var(--accent-blue);
}

.sm-vision-point:nth-child(4) .sm-point-icon {
  background-color: var(--light-blue);
}

.sm-vision-point:nth-child(4) .sm-point-icon i {
  color: var(--primary-blue) !important;
}

.sm-point-icon {
  width: 50px;
  height: 50px;
  min-width: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  position: relative;
  box-shadow: 0 8px 20px rgba(1, 92, 146, 0.15);
  overflow: hidden;
  transition: transform 0.3s ease;
}

.sm-vision-point:hover .sm-point-icon {
  transform: translateY(-5px) rotate(5deg);
}

.sm-point-icon i {
  color: white;
  font-size: 1.3rem;
  z-index: 1;
}

.sm-icon-glow {
  position: absolute;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3), transparent 70%);
  top: -50%;
  left: -50%;
  opacity: 0.7;
  transition: transform 0.5s ease;
}

.sm-vision-point:hover .sm-icon-glow {
  transform: scale(1.2) rotate(15deg);
}

.sm-point-content {
  flex: 1;
}

.sm-point-title {
  font-size: 1.1rem;
  margin-bottom: 8px;
  position: relative;
}

.sm-point-description {
  font-size: 0.95rem;
  color: #64748b;
  line-height: 1.6;
}

/* Image column */
.sm-vision-media {
  box-sizing: border-box;
  width: 100%;
}

.sm-vision-image-wrapper {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(1, 92, 146, 0.15);
  margin-bottom: 30px;
}

.sm-vision-image {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.5s ease;
}

.sm-vision-image-wrapper:hover .sm-vision-image {
  transform: scale(1.03);
}

/* Mission badge */
.sm-mission-badge {
  position: absolute;
  top: 20px;
  right: 20px;
  background: var(--blue-gradient);
  color: white;
  padding: 12px 20px;
  border-radius: 50px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 0.95rem;
  box-shadow: 0 10px 20px rgba(1, 92, 146, 0.25);
  z-index: 3;
}

.sm-mission-badge i {
  font-size: 1rem;
}

/* Stats grid */
.sm-vision-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.sm-stat-item {
  background-color: #ffffff;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 10px 25px rgba(1, 92, 146, 0.08);
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(1, 92, 146, 0.1);
}

.sm-stat-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(1, 92, 146, 0.15);
  border-color: var(--accent-blue);
}

.sm-stat-number {
  font-size: 2rem;
  font-weight: 800;
  color: var(--primary-blue);
  margin-bottom: 5px;
  display: block;
}

.sm-stat-label {
  font-size: 0.85rem;
  color: #64748b;
  line-height: 1.4;
}

.sm-stat-icon {
  position: absolute;
  bottom: -15px;
  right: -15px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(1, 92, 146, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.5;
}

.sm-stat-icon i {
  font-size: 1.5rem;
  color: var(--primary-blue);
}

/* Conclusion area */
.sm-vision-conclusion {
  text-align: center;
  max-width: 900px;
  margin: 60px auto 20px;
  padding: 0 20px;
}

.sm-vision-conclusion p {
  font-size: 1.2rem;
  line-height: 1.8;
  color: #334155;
  margin-bottom: 30px;
}

/* CTA button */
.sm-vision-action {
  margin-top: 30px;
  display: flex;
  justify-content: center;
}

.sm-vision-button {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  background: var(--blue-gradient);
  color: white;
  font-weight: 600;
  padding: 14px 30px;
  border-radius: 10px;
  text-decoration: none;
  box-shadow: 0 10px 20px rgba(1, 92, 146, 0.25);
  transition: all 0.3s ease;
}

.sm-vision-button:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(1, 92, 146, 0.35);
}

.sm-vision-button i {
  transition: transform 0.3s ease;
}

.sm-vision-button:hover i {
  transform: translateX(5px);
}

/* Background decorative elements */
.sm-bg-circle {
  position: absolute;
  width: 500px;
  height: 500px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(1, 92, 146, 0.05) 0%, rgba(1, 92, 146, 0) 70%);
  top: -250px;
  right: -200px;
  z-index: 1;
  pointer-events: none;
}

.sm-bg-dots {
  position: absolute;
  bottom: 20%;
  left: 5%;
  width: 200px;
  height: 200px;
  background-image: radial-gradient(circle, rgba(45, 130, 181, 0.15) 2px, transparent 2px);
  background-size: 20px 20px;
  border-radius: 50%;
  z-index: 1;
  pointer-events: none;
}

.sm-bg-wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 120px;
  background: linear-gradient(45deg, rgba(66, 140, 212, 0.1) 25%, transparent 25%),
              linear-gradient(-45deg, rgba(66, 140, 212, 0.1) 25%, transparent 25%),
              linear-gradient(45deg, transparent 75%, rgba(66, 140, 212, 0.1) 75%),
              linear-gradient(-45deg, transparent 75%, rgba(66, 140, 212, 0.1) 75%);
  background-size: 30px 30px;
  z-index: 1;
  pointer-events: none;
  opacity: 0.3;
}

/* Animation classes */
.sm-vision-animate {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.sm-vision-animate.sm-animated {
  opacity: 1;
  transform: translateY(0);
}

.sm-vision-animate:nth-child(1) { transition-delay: 0.1s; }
.sm-vision-animate:nth-child(2) { transition-delay: 0.2s; }
.sm-vision-animate:nth-child(3) { transition-delay: 0.3s; }
.sm-vision-animate:nth-child(4) { transition-delay: 0.4s; }
.sm-vision-animate:nth-child(5) { transition-delay: 0.5s; }

/* Responsive design */
@media (max-width: 1200px) {
  .sm-vision-container {
    max-width: 1140px;
  }
  
  .sm-vision-title {
    font-size: 2.2rem;
  }
  
  .sm-vision-heading {
    font-size: 1.6rem;
  }
}

@media (max-width: 991px) {
  .sm-vision-outer {
    padding: 80px 0 60px;
  }
  
  .sm-vision-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  .sm-vision-media {
    order: 1;
  }
  
  .sm-vision-title {
    font-size: 2rem;
  }
}

@media (max-width: 767px) {
  .sm-vision-outer {
    padding: 60px 0 50px;
  }
  
  .sm-vision-points {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .sm-vision-header {
    margin-bottom: 50px;
  }
  
  .sm-vision-title {
    font-size: 1.8rem;
  }
  
  .sm-vision-heading {
    font-size: 1.5rem;
  }
  
  .sm-vision-stats {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .sm-stat-number {
    font-size: 1.8rem;
  }
  
  .sm-vision-quote p {
    font-size: 1rem;
  }
}

@media (max-width: 575px) {
  .sm-vision-outer {
    padding: 50px 0 40px;
  }
  
  .sm-vision-container {
    padding: 0 15px;
  }
  
  .sm-vision-badge {
    padding: 6px 16px;
    font-size: 0.85rem;
  }
  
  .sm-vision-title {
    font-size: 1.6rem;
  }
  
  .sm-vision-quote {
    flex-direction: column;
    gap: 10px;
  }
  
  .sm-vision-quote p {
    margin: 0;
  }
  
  .sm-vision-stats {
    gap: 12px;
  }
  
  .sm-mission-badge {
    padding: 10px 16px;
    font-size: 0.85rem;
  }
  
  .sm-vision-conclusion p {
    font-size: 1.1rem;
  }
  
  .sm-vision-button {
    padding: 12px 24px;
  }
}
