﻿/* 
  MedicalEventsMain.css
  CSS for title and navigation tabs only
*/

.medical-events-page {
  width: 100%;
}

.medical-events-container {
  padding: 20px;
  color: #333;
}

.page-title {
  margin: 0 0 20px 0;
  font-size: 26px;
  font-weight: 600;
  color: #333;
}

.medical-events-tabs {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.medical-events-nav {
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  margin: 0;
  padding: 0;
  background-color: #f8f9fa;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 15px 20px;
  cursor: pointer;
  font-weight: 500;
  border: none;
  border-bottom: 3px solid transparent;
  transition: all 0.2s ease;
  color: #555;
  background: none;
  white-space: nowrap;
}

.tab-button i {
  margin-right: 5px;
}

.tab-button:hover {
  background-color: #f0f1f2;
  color: #007bff;
}

.tab-button.active {
  background-color: #ffffff;
  color: #007bff;
  border-bottom: 3px solid #007bff;
}

.tab-button.active i {
  color: #2180de;
}

.medical-events-content {
  padding: 20px;
}

/* Common table styling for all events tables */
.events-table-container {
  flex: 1;
  overflow: visible;
  margin-bottom: 20px;
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.events-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
}

.events-table th {
  padding: 12px 15px;
  text-align: left;
  font-weight: 600;
  color: #333;
  background-color: #f8f9fa;
  position: relative;
  border-bottom: 1px solid #eee;
}

.events-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
  color: #37474f;
}

.events-table tr:last-child td {
  border-bottom: none;
}

.events-table tr:hover {
  background-color: #f5f8ff;
}

/* Status badges for all tables */
.status-badge {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  margin: 0 auto;
}

/* Status badges for MedicationReceiving */
.status-new {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-active {
  background-color: #e3f2fd;
  color: #1565c0;
}

.status-completed {
  background-color: #e0f2f1;
  color: #00796b;
}

.status-expired {
  background-color: #ffebee;
  color: #c62828;
}

/* Status badges for MedicationAdministration */
.status-given {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-refused {
  background-color: #ffebee;
  color: #c62828;
}

.status-absent {
  background-color: #f3e5f5;
  color: #6a1b9a;
}

/* Status badges for MedicalIncidents - IMPORTANT: REMOVED */
/* Styles have been moved to MedicalIncidents.css */

/* Common action buttons for all medical event components */
.btn-view, .btn-edit, .btn-delete {
  border: none;
  border-radius: 4px;
  width: 32px;
  height: 32px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin: 0 2px;
  transition: all 0.2s;
}

.btn-view {
  background-color: #e3f2fd;
  color: #1565c0;
}

.btn-view:hover {
  background-color: #bbdefb;
}

.btn-edit {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.btn-edit:hover {
  background-color: #c8e6c9;
}

.btn-delete {
  background-color: #ffebee;
  color: #c62828;
}

.btn-delete:hover {
  background-color: #ffcdd2;
}

.action-cell {
  white-space: nowrap;
  text-align: center !important;
}

.tab-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.25);
}

.tab-button.active:focus {
  box-shadow: 0 0 0 2px rgba(33, 128, 222, 0.3);
}

.tab-button:active {
  transform: translateY(1px);
}

@media (max-width: 992px) {
  .tab-button i {
    margin-right: 0;
  }
  
  .tab-button {
    padding: 15px;
  }
  
  .tab-button span {
    display: none;
  }
}

@media (max-width: 576px) {
  .medical-events-container {
    padding: 10px;
  }
  
  .page-title {
    font-size: 22px;
  }
  
  .tab-button {
    padding: 10px;
    font-size: 16px;
  }
  
  .medical-events-content {
    padding: 10px;
  }
}

@media (prefers-reduced-motion: reduce) {
  .tab-button, .medical-events-content {
    transition: none;
  }
}
