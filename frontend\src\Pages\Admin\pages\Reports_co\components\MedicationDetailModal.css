/* Medication Detail Modal Styles - Multi-Theme Support */
.admin-medication-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 24px;
  backdrop-filter: blur(12px);
  animation: overlayFadeIn 0.4s ease-out;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

@keyframes overlayFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.admin-medication-modal {
  background: white;
  border-radius: 24px;
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  box-shadow:
    0 25px 80px rgba(59, 130, 246, 0.25),
    0 15px 40px rgba(0, 0, 0, 0.15);
  animation: modalSlideIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-40px) scale(0.85) rotateX(10deg);
  }
  50% {
    opacity: 0.8;
    transform: translateY(-10px) scale(1.02) rotateX(0deg);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1) rotateX(0deg);
  }
}

/* Modal Header */
.admin-medication-modal-header h2{
  color: #fff;
}

.admin-medication-modal-header {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
  padding: 32px 40px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Theme-specific header backgrounds */
.theme-blue .admin-medication-modal-header {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
}

.theme-green .admin-medication-modal-header {
  background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
}

.theme-purple .admin-medication-modal-header {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%);
}

.theme-orange .admin-medication-modal-header {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
}

.theme-teal .admin-medication-modal-header {
  background: linear-gradient(135deg, #14b8a6 0%, #0d9488 50%, #0f766e 100%);
}

.admin-medication-modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.1) 50%, transparent 60%);
  animation: shimmer 3s infinite;
  pointer-events: none;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.admin-medication-modal-title {
  display: flex;
  align-items: center;
  gap: 15px;
  position: relative;
  z-index: 1;
  text-align: center;
}

.admin-medication-modal-title i {
  font-size: 28px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.admin-medication-modal-title h2 {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  text-align: center;
}

.admin-medication-close-button {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  width: 44px;
  height: 44px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  position: absolute;
  right: 35px;
  z-index: 1;
  backdrop-filter: blur(10px);
}

.admin-medication-close-button:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: rotate(90deg) scale(1.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Modal Body */
.admin-medication-modal-body {
  padding: 0;
  flex: 1;
  overflow-y: auto;
  max-height: calc(85vh - 160px);
}

.admin-medication-modal-body::-webkit-scrollbar {
  width: 6px;
}

.admin-medication-modal-body::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.admin-medication-modal-body::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.admin-medication-modal-body::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Info Sections */
.admin-medication-info-section {
  padding: 25px 30px;
  border-bottom: 1px solid #e2e8f0;
}

.admin-medication-info-section:last-child {
  border-bottom: none;
}

.admin-medication-info-section h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 10px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e2e8f0;
}

.admin-medication-info-section h3 i {
  color: #4f46e5;
  font-size: 20px;
}

/* Basic Information Grid */
.admin-medication-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.admin-medication-info-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.admin-medication-info-item label {
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-medication-info-item span {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

/* Medication ID Badge */
.admin-medication-id-badge {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Medication Name */
.admin-medication-name {
  font-size: 18px !important;
  font-weight: 700 !important;
  color: #1e293b !important;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Medication Type */
.admin-medication-type {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white !important;
  padding: 6px 14px;
  border-radius: 16px;
  font-size: 14px !important;
  font-weight: 600 !important;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 3px 10px rgba(16, 185, 129, 0.3);
}

/* Dosage Form */
.admin-medication-dosage-form {
  background: #f3f4f6;
  color: #374151;
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid #d1d5db;
}

/* Stock Information */
.admin-medication-stock-info {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-radius: 16px;
  padding: 25px;
  border: 1px solid #e2e8f0;
}

.admin-medication-stock-item {
  display: flex;
  align-items: center;
  gap: 15px;
  background: white;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.admin-medication-stock-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 24px;
}

.admin-medication-stock-icon i.fa-check-circle {
  color: #10b981;
}

.admin-medication-stock-icon i.fa-exclamation-triangle {
  color: #f59e0b;
}

.admin-medication-stock-icon i.fa-times-circle {
  color: #ef4444;
}

.admin-medication-stock-details {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.admin-medication-stock-quantity {
  font-size: 20px !important;
  font-weight: 700 !important;
  color: #1f2937 !important;
}

.admin-medication-stock-status {
  font-size: 14px !important;
  font-weight: 600 !important;
  padding: 4px 8px;
  border-radius: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-medication-stock-status.in-stock {
  background: #dcfce7;
  color: #166534 !important;
}

.admin-medication-stock-status.low-stock {
  background: #fef3c7;
  color: #92400e !important;
}

.admin-medication-stock-status.out-of-stock {
  background: #fee2e2;
  color: #991b1b !important;
}

/* Date Information */
.admin-medication-date-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.admin-medication-date-item {
  background: white;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.admin-medication-date-item label {
  font-size: 14px !important;
  font-weight: 600 !important;
  color: #6b7280 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  margin-bottom: 8px;
  display: block;
}

.admin-medication-date-item span {
  font-size: 16px !important;
  font-weight: 600 !important;
  color: #1f2937 !important;
}

.admin-medication-expiry-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.admin-medication-expiry-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 14px !important;
  font-weight: 600 !important;
}

.admin-medication-expiry-status.expiry-good {
  background: #dcfce7;
  color: #166534 !important;
}

.admin-medication-expiry-status.expiring-warning {
  background: #dbeafe;
  color: #1e40af !important;
}

.admin-medication-expiry-status.expiring-soon {
  background: #fef3c7;
  color: #92400e !important;
}

.admin-medication-expiry-status.expired {
  background: #fee2e2;
  color: #991b1b !important;
}

.admin-medication-expiry-status small {
  font-size: 12px;
  opacity: 0.8;
}

/* Description */
.admin-medication-description {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.admin-medication-description p {
  margin: 0;
  line-height: 1.6;
  color: #374151;
  font-size: 15px;
}

/* Alerts Section */
.admin-medication-alerts {
  background: #fffbeb;
  border-left: 4px solid #f59e0b;
}

.admin-medication-alert-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.admin-medication-alert-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
}

.admin-medication-alert-item.admin-medication-danger {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
}

.admin-medication-alert-item.admin-medication-warning {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fcd34d;
}

.admin-medication-alert-item.admin-medication-info {
  background: #dbeafe;
  color: #1e40af;
  border: 1px solid #93c5fd;
}

.admin-medication-alert-item i {
  font-size: 16px;
}

/* Modal Footer */
.admin-medication-modal-footer {
  padding: 20px 30px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.admin-medication-btn-secondary {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: #6b7280;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.admin-medication-btn-secondary:hover {
  background: #4b5563;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-medication-modal {
    margin: 10px;
    max-width: calc(100vw - 20px);
    max-height: calc(100vh - 20px);
    border-radius: 16px;
  }

  .admin-medication-modal-header {
    padding: 20px 25px;
    flex-direction: column;
    text-align: center;
  }

  .admin-medication-close-button {
    position: static;
    margin-top: 10px;
  }

  .admin-medication-modal-title h2 {
    font-size: 22px;
  }

  .admin-medication-info-grid,
  .admin-medication-date-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .admin-medication-info-section {
    padding: 20px 25px;
  }

  .admin-medication-modal-body {
    max-height: calc(100vh - 180px);
  }
}

@media (max-width: 480px) {
  .admin-medication-modal {
    margin: 5px;
    max-width: calc(100vw - 10px);
    max-height: calc(100vh - 10px);
    border-radius: 12px;
  }

  .admin-medication-modal-header {
    padding: 15px 20px;
    flex-direction: column;
    text-align: center;
  }

  .admin-medication-close-button {
    position: static;
    margin-top: 8px;
  }

  .admin-medication-modal-title h2 {
    font-size: 18px;
  }

  .admin-medication-info-section {
    padding: 15px 20px;
  }

  .admin-medication-stock-info,
  .admin-medication-alerts {
    padding: 15px;
  }
}
