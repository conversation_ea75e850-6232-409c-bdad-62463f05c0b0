.community-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 30px 15px;
  display: grid;
  grid-template-columns: 1fr 300px;
  grid-template-areas:
    "header header"
    "filter filter"
    "posts sidebar";
  gap: 25px;
  font-family: 'Be Vietnam Pro', sans-serif;
  background-color: #ffffff;
  min-height: 100vh;
}

/* Override handled by global.css */

/* Header */
.community-header {
  grid-area: header;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #015C92 0%, #2D82B5 30%, #428CD4 60%, #88CDF6 100%);
  padding: 30px;
  border-radius: 12px;
  color: #ffffff;
  box-shadow: 0 8px 16px rgba(1, 92, 146, 0.3);
  position: relative;
  overflow: hidden;
  z-index: 1;
  border: none;
}

.community-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><path fill="rgba(255,255,255,0.1)" d="M42.7,-65.1C53.2,-52.6,58.1,-37.6,65.3,-22.6C72.6,-7.7,82.2,7.1,79.4,19.5C76.6,31.9,61.4,41.8,47.4,49.7C33.4,57.6,20.7,63.5,5.5,67.5C-9.7,71.4,-27.4,73.5,-41.7,66.9C-56,60.2,-66.8,44.8,-71.1,28.6C-75.4,12.4,-73.1,-4.7,-69.6,-22.7C-66,-40.7,-61.3,-59.5,-48.9,-71.4C-36.6,-83.4,-18.3,-88.4,-1.1,-86.9C16.1,-85.3,32.2,-77.1,42.7,-65.1Z" transform="translate(100 100)" /></svg>');
  background-repeat: no-repeat;
  background-size: contain;
  opacity: 0.2;
  transform: rotate(45deg);
  z-index: 0;
}

.community-title {
  position: relative;
  z-index: 1;
}

.community-title h1 {
  font-size: 2.2rem;
  color: #ffffff;
  margin: 0 0 8px;
  font-weight: 700;
  font-family: 'Be Vietnam Pro', sans-serif;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.community-title p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  margin: 0;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.create-post-btn {
  background: linear-gradient(135deg, #88CDF6 0%, #BCE6FF 100%);
  color: #015C92;
  border: none;
  padding: 12px 20px;
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  box-shadow: 0 4px 10px rgba(136, 205, 246, 0.4);
  font-family: 'Be Vietnam Pro', sans-serif;
}

.create-post-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 14px rgba(0, 0, 0, 0.15);
}

.create-post-btn i {
  margin-right: 8px;
  font-size: 1.1rem;
}

.community-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.refresh-posts-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Be Vietnam Pro', sans-serif;
  gap: 8px;
}

.refresh-posts-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.refresh-posts-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Filter bar */
.community-filter-bar {
  grid-area: filter;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  padding: 16px 20px;
  margin-bottom: 20px;
  box-shadow: var(--shadow);
  position: sticky;
  top: 10px;
  z-index: 100;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  overflow-x: auto;
  scrollbar-width: thin;
  scrollbar-color: #bbb transparent;
  padding-bottom: 4px;
}

.filter-tabs::-webkit-scrollbar {
  height: 4px;
}

.filter-tabs::-webkit-scrollbar-thumb {
  background-color: #bbb;
  border-radius: 4px;
}

.filter-tab {
  background: none;
  border: 1px solid var(--border-light);
  border-radius: 20px;
  padding: 8px 16px;
  color: var(--text-medium);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  font-family: 'Be Vietnam Pro', sans-serif;
  white-space: nowrap;
}

.filter-tab i {
  margin-right: 8px;
  font-size: 0.9rem;
}

.filter-tab.active {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(37, 99, 235, 0.2);
}

.filter-tab:hover:not(.active) {
  background-color: #f1f5f9;
  border-color: var(--primary-color);
  color: var(--primary-color);
}

/* Community search bar container - uses SearchBox component with community-search class */
.search-bar {
  position: relative;
  min-width: 200px;
  flex: 1;
  max-width: 400px;
}

/* Search results info */
.search-results-info {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 20px;
  color: #495057;
  font-size: 14px;
}

.search-results-info p {
  margin: 0;
}

.search-results-info strong {
  color: #015C92;
  font-weight: 600;
}

/* Search styling is handled by SearchBox component with community-search variant */

/* Posts section */
.posts-section {
  grid-area: posts;
}

.posts-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.post-card {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-light);
  position: relative;
  transition: all 0.3s ease;
}

.post-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.post-card.pinned {
  border-left: 4px solid var(--primary-color);
}

.pin-indicator {
  position: absolute;
  top: 8px;
  right: 12px;
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
  z-index: 3;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.pin-indicator.official {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
}

.pin-indicator.personal {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.bookmark-indicator.personal {
  position: absolute;
  top: 12px;
  left: 12px;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
  z-index: 2;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.post-card.bookmarked-post {
  border-left: 4px solid #f59e0b;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.1);
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-light);
}

.post-author {
  display: flex;
  align-items: center;
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.author-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: white;
  font-size: 18px;
}

.parent-icon {
  background: linear-gradient(135deg, #4e54c8 0%, #8f94fb 100%);
}

.default-icon {
  background: linear-gradient(135deg, #7F7FD5 0%, #86A8E7 100%);
}

.nurse-icon {
  background: linear-gradient(135deg, #00b09b, #96c93d);
}

/* Style chung cho các author-icon */
.author-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: white;
  font-size: 18px;
}

.author-info {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 3px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.author-badge {
  font-size: 11px;
  padding: 2px 8px;
  border-radius: 12px;
  margin-left: 8px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.author-badge.parent {
  background-color: #4e54c8;
  color: white;
  font-size: 11px;
  padding: 2px 8px;
  border-radius: 12px;
  margin-left: 8px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.author-badge.nurse {
  background-color: #00b09b;
  color: white;
}

.author-badge.admin {
  background-color: #ff6b6b;
  color: white;
  font-size: 11px;
  padding: 2px 8px;
  border-radius: 12px;
  margin-left: 8px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.post-time {
  font-size: 0.8rem;
  color: var(--text-light);
  font-family: 'Be Vietnam Pro', sans-serif;
}

.post-category {
  font-size: 0.85rem;
  padding: 6px 12px;
  background-color: #f7fafc;
  border-radius: var(--border-radius);
  color: var(--text-medium);
  display: flex;
  align-items: center;
  gap: 6px;
  font-family: 'Be Vietnam Pro', sans-serif;
  margin-right: 120px; /* Tạo khoảng trống cho pin indicator */
}

.post-content {
  padding: 20px;
}

.post-title {
  margin: 0 0 12px;
  font-size: 1.2rem;
  line-height: 1.4;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.post-title a {
  color: var(--text-dark);
  text-decoration: none;
  transition: color 0.2s;
}

.post-title a:hover {
  color: var(--primary-color);
}

.post-excerpt {
  color: var(--text-medium);
  margin: 0;
  line-height: 1.6;
  font-size: 0.95rem;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.post-footer {
  padding: 12px 20px;
  border-top: 1px solid var(--border-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fafafa;
}

.post-stats {
  display: flex;
  gap: 15px;
  font-size: 0.9rem;
  color: var(--text-medium);
  align-items: center;
}

.like-btn, .comments-btn, .bookmark-btn {
  background: none;
  border: none;
  padding: 6px 10px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  color: var(--text-medium);
  transition: all 0.2s;
  text-decoration: none;
  font-family: 'Be Vietnam Pro', sans-serif;
  font-size: 0.9rem;
}

.like-btn:hover, .comments-btn:hover {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

.like-btn.liked {
  color: #f44336;
  background-color: rgba(244, 67, 54, 0.08);
  font-weight: 500;
}

.like-btn.liked i {
  color: #f44336;
  animation: heartBeat 0.4s ease-in-out;
}

/* ✅ NEW: Loading state for like button */
.like-btn.loading {
  opacity: 0.7;
  cursor: not-allowed;
  pointer-events: none;
}

.like-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.like-btn .fa-spinner {
  color: #f44336;
}

/* Heart animation for better visual feedback */
@keyframes heartBeat {
  0% { transform: scale(1); }
  50% { transform: scale(1.3); }
  100% { transform: scale(1); }
}

.read-more-btn {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  font-family: 'Be Vietnam Pro', sans-serif;
  transition: all 0.2s;
}

.read-more-btn:hover {
  text-decoration: underline;
  transform: translateX(4px);
}

/* Sidebar */
.community-sidebar {
  grid-area: sidebar;
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.sidebar-section {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-light);
}

.sidebar-section h3 {
  padding: 15px;
  margin: 0;
  font-size: 1.1rem;
  background-color: #f8fafc;
  color: var(--text-dark);
  border-bottom: 1px solid var(--border-light);
  font-family: 'Be Vietnam Pro', sans-serif;
  font-weight: 600;
}

.topic-list, .rules-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.topic-list li, .rules-list li {
  padding: 10px 15px;
  border-bottom: 1px solid var(--border-light);
}

.topic-list li:last-child, .rules-list li:last-child {
  border-bottom: none;
}

.topic-link {
  display: flex;
  align-items: center;
  gap: 10px;
  background: none;
  border: none;
  padding: 8px 0;
  color: var(--text-medium);
  font-size: 0.95rem;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s;
  font-family: 'Be Vietnam Pro', sans-serif;
  width: 100%;
}

.topic-link:hover {
  color: var(--primary-color);
  transform: translateX(5px);
}

.topic-link.active {
  color: var(--primary-color);
  font-weight: 600;
}

.topic-link i {
  width: 20px;
  text-align: center;
  font-size: 0.95rem;
}

.rules-list li {
  color: var(--text-medium);
  font-size: 0.95rem;
  line-height: 1.5;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.nurse-contact {
  padding: 15px;
}

.nurse-info {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.nurse-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 12px;
  border: 2px solid var(--border-light);
}

.nurse-avatar-container {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #00b09b, #96c93d);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.nurse-name {
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 3px;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.nurse-title {
  font-size: 0.85rem;
  color: var(--text-light);
  font-family: 'Be Vietnam Pro', sans-serif;
}

.contact-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  text-align: center;
  padding: 10px;
  border-radius: var(--border-radius);
  text-decoration: none;
  margin-bottom: 10px;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.2s;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.contact-btn:last-child {
  margin-bottom: 0;
}

.contact-btn i {
  margin-right: 8px;
}

.contact-btn {
  background-color: var(--primary-color);
  color: white;
}

.contact-btn.message {
  background-color: #e3f2fd;
  color: var(--primary-color);
}

.contact-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Empty state */
.empty-posts {
  text-align: center;
  padding: 50px 20px;
  color: var(--text-medium);
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

.empty-posts i {
  font-size: 48px;
  opacity: 0.3;
  margin-bottom: 15px;
  color: var(--text-light);
}

.empty-posts p {
  font-size: 1.1rem;
  margin: 0 0 20px;
  color: var(--text-medium);
  font-family: 'Be Vietnam Pro', sans-serif;
}

.reset-filters-btn {
  background-color: #edf2f7;
  color: var(--text-medium);
  border: none;
  padding: 10px 16px;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 0.95rem;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  margin: 15px auto 0;
  font-family: 'Be Vietnam Pro', sans-serif;
  transition: all 0.2s;
}

.reset-filters-btn:hover {
  background-color: #e2e8f0;
  color: var(--text-dark);
}

/* Create post modal */
.create-post-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999; /* Đảm bảo modal hiển thị trên header */
  padding: 20px;
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.create-post-container {
  background-color: var(--bg-white);
  width: 100%;
  max-width: 700px;
  border-radius: var(--border-radius);
  overflow: hidden;
  position: relative;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
  animation: slideUp 0.4s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes slideUp {
  from { 
    transform: translateY(50px) scale(0.95); 
    opacity: 0; 
  }
  to { 
    transform: translateY(0) scale(1); 
    opacity: 1; 
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-light);
}

.modal-header h2 {
  margin: 0;
  font-size: 1.4rem;
  color: var(--text-dark);
  font-family: 'Be Vietnam Pro', sans-serif;
  font-weight: 600;
}

.close-modal-btn {
  background: none;
  border: none;
  font-size: 1.4rem;
  color: var(--text-light);
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.close-modal-btn:hover {
  background-color: #f5f5f5;
  color: var(--text-dark);
}

.create-post-form {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-dark);
  font-family: 'Be Vietnam Pro', sans-serif;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius);
  font-size: 0.95rem;
  font-family: 'Be Vietnam Pro', sans-serif;
  transition: all 0.2s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
}

.help-text {
  display: block;
  font-size: 0.85rem;
  color: var(--text-light);
  margin-top: 6px;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.file-upload-container {
  position: relative;
  margin-bottom: 8px;
}

.file-upload-container input[type="file"] {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
}

.file-upload-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background-color: #f5f5f5;
  border-radius: var(--border-radius);
  cursor: pointer;
  color: var(--text-medium);
  transition: all 0.2s;
  font-family: 'Be Vietnam Pro', sans-serif;
  font-size: 0.95rem;
}

.file-upload-btn:hover {
  background-color: #e2e8f0;
  color: var(--text-dark);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 30px;
}

.form-actions button {
  padding: 10px 20px;
  border-radius: var(--border-radius);
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.cancel-btn {
  background: none;
  border: 1px solid var(--border-light);
  color: var(--text-medium);
}

.cancel-btn:hover {
  background-color: #f7fafc;
  color: var(--text-dark);
}

.submit-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  gap: 8px;
}

.submit-btn:hover {
  background-color: var(--accent-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.submit-btn:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
  gap: 16px;
}

.pagination-btn {
  background-color: var(--bg-white);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius);
  padding: 10px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.95rem;
  color: var(--text-medium);
  cursor: pointer;
  transition: all 0.2s;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #f5f5f5;
  color: var(--text-dark);
  border-color: var(--text-medium);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-size: 0.95rem;
  color: var(--text-medium);
  font-family: 'Be Vietnam Pro', sans-serif;
}

/* Parent Pagination Controls - Modern Design */
.parent-pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 40px auto;
  gap: 20px;
  padding: 20px;
  background: #ffffff;
  backdrop-filter: blur(10px);
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(1, 92, 146, 0.1);
  grid-column: 1 / -1; /* Span across all grid columns */
  width: 100%;
  max-width: 600px; /* Limit maximum width */
}

.parent-pagination-btn {
  background: linear-gradient(135deg, #015C92 0%, #2D82B5 100%);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Be Vietnam Pro', sans-serif;
  box-shadow: 0 4px 15px rgba(1, 92, 146, 0.3);
}

.parent-pagination-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2D82B5 0%, #428CD4 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(1, 92, 146, 0.4);
}

.parent-pagination-btn:disabled {
  background: linear-gradient(135deg, #9ca3af, #d1d5db);
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.parent-pagination-info {
  font-size: 1rem;
  color: #374151;
  font-weight: 600;
  font-family: 'Be Vietnam Pro', sans-serif;
  padding: 10px 20px;
  background: rgba(1, 92, 146, 0.1);
  border-radius: 20px;
  border: 1px solid rgba(1, 92, 146, 0.2);
}

/* Responsive Design for Parent Pagination */
@media (max-width: 768px) {
  .parent-pagination-controls {
    margin: 30px auto;
    padding: 15px;
    gap: 15px;
    max-width: 90%;
  }

  .parent-pagination-btn {
    padding: 10px 16px;
    font-size: 0.9rem;
  }

  .parent-pagination-info {
    font-size: 0.9rem;
    padding: 8px 16px;
  }
}

@media (max-width: 480px) {
  .parent-pagination-controls {
    flex-direction: column;
    gap: 12px;
    margin: 20px auto;
    padding: 12px;
    max-width: 95%;
  }

  .parent-pagination-btn {
    width: 100%;
    justify-content: center;
    padding: 12px 20px;
  }

  .parent-pagination-info {
    order: -1;
    text-align: center;
    width: 100%;
  }
}

/* Style cho nút bookmark - thêm vào file Community.css */
.bookmark-btn {
  background: none;
  border: none;
  padding: 6px 10px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  color: var(--text-medium);
  transition: all 0.2s;
  font-family: 'Be Vietnam Pro', sans-serif;
  font-size: 0.9rem;
}

.bookmark-btn:hover {
  background-color: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.bookmark-btn.bookmarked {
  color: #f59e0b;
  background-color: rgba(245, 158, 11, 0.08);
}

.bookmark-btn.bookmarked i {
  color: #f59e0b;
  animation: bookmarkPulse 0.4s ease-in-out;
}

@keyframes bookmarkPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* Enhanced Like Button */