/* Medication List View Styles with Reports Namespace */

/* Container chính */
.reports-medication-list-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
  font-family: Arial, sans-serif;
}

/* Loading Section */
.reports-medication-loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: #6c757d;
  gap: 20px;
}

.reports-medication-loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Header */
.reports-medication-header {
  background: #ffffff;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.reports-medication-header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.reports-medication-back-button {
  background: #6c757d;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  transition: background-color 0.3s ease;
}

.reports-medication-back-button:hover {
  background: #5a6268;
}

.reports-medication-title {
  margin: 0;
  color: #333;
  font-size: 28px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
}

.reports-medication-subtitle {
  margin: 0;
  color: #666;
  font-size: 16px;
}

/* Statistics Cards */
.reports-medication-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.reports-medication-stat-card {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.reports-medication-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.reports-medication-stat-card.reports-medication-total {
  border-left: 4px solid #007bff;
}

.reports-medication-stat-card.reports-medication-low-stock {
  border-left: 4px solid #ffc107;
}

.reports-medication-stat-card.reports-medication-near-expiry {
  border-left: 4px solid #dc3545;
}

.reports-medication-stat-card.reports-medication-categories {
  border-left: 4px solid #28a745;
}

.reports-medication-stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.reports-medication-total .reports-medication-stat-icon {
  background: #007bff;
}

.reports-medication-low-stock .reports-medication-stat-icon {
  background: #ffc107;
}

.reports-medication-near-expiry .reports-medication-stat-icon {
  background: #dc3545;
}

.reports-medication-categories .reports-medication-stat-icon {
  background: #28a745;
}

.reports-medication-stat-content h3 {
  margin: 0 0 5px 0;
  font-size: 24px;
  font-weight: 700;
  color: #333;
}

.reports-medication-stat-content p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* Filters */
.reports-medication-filters {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 25px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.reports-medication-filter-group {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 15px;
  align-items: end;
}

.reports-medication-search-box {
  position: relative;
}

.reports-medication-search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

.reports-medication-search-box input {
  width: 100%;
  padding: 10px 15px 10px 35px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.reports-medication-search-box input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.reports-medication-filter-select {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background: white;
  cursor: pointer;
}

.reports-medication-filter-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.reports-medication-results-count {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
  color: #666;
  font-size: 14px;
  text-align: center;
}

/* Error Message */
.reports-medication-error-message {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.reports-medication-error-message button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
}

.reports-medication-error-message button:hover {
  background: #c82333;
}

/* Table */
.reports-medication-table-container {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.reports-medication-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.reports-medication-table thead {
  background: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
}

.reports-medication-table th {
  padding: 15px 12px;
  text-align: left;
  font-weight: 600;
  color: #495057;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.reports-medication-table-row {
  border-bottom: 1px solid #eee;
  transition: background-color 0.3s ease;
}

.reports-medication-table-row:hover {
  background: #f8f9fa;
}

.reports-medication-table td {
  padding: 15px 12px;
  vertical-align: middle;
}

.reports-medication-table-stt {
  font-weight: 600;
  color: #666;
  width: 60px;
  text-align: center;
}

.reports-medication-table-name {
  min-width: 250px;
}

.reports-medication-name-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.reports-medication-item-name {
  font-weight: 600;
  color: #333;
  font-size: 15px;
  margin: 0;
}

.reports-medication-item-description {
  color: #666;
  font-size: 13px;
  line-height: 1.3;
}

.reports-medication-table-type {
  min-width: 120px;
}

.reports-medication-type-badge {
  background: #e9ecef;
  color: #495057;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  display: inline-block;
}

.reports-medication-table-stock {
  min-width: 100px;
  text-align: center;
}

.reports-medication-stock-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.reports-medication-stock-quantity {
  font-weight: 700;
  font-size: 16px;
  color: #333;
}

.reports-medication-stock-status {
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.reports-medication-stock-status.in-stock {
  background: #d4edda;
  color: #155724;
}

.reports-medication-stock-status.low-stock {
  background: #fff3cd;
  color: #856404;
}

.reports-medication-stock-status.out-of-stock {
  background: #f8d7da;
  color: #721c24;
}

.reports-medication-table-expiry {
  min-width: 120px;
  text-align: center;
}

.reports-medication-expiry-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.reports-medication-expiry-date {
  font-weight: 600;
  font-size: 13px;
  color: #333;
}

.reports-medication-expiry-status {
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.reports-medication-expiry-status.good {
  background: #d4edda;
  color: #155724;
}

.reports-medication-expiry-status.warning {
  background: #fff3cd;
  color: #856404;
}

.reports-medication-expiry-status.expired {
  background: #f8d7da;
  color: #721c24;
}

.reports-medication-table-actions {
  width: 80px;
  text-align: center;
}

.reports-medication-action-button {
  background: #007bff;
  color: white;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.reports-medication-action-button:hover {
  background: #0056b3;
  transform: scale(1.1);
}

/* No Data */
.reports-medication-no-data {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  color: #666;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.reports-medication-no-data i {
  font-size: 48px;
  color: #ccc;
  margin-bottom: 15px;
}

.reports-medication-no-data h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 20px;
}

.reports-medication-no-data p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

/* Responsive */
@media (max-width: 1024px) {
  .reports-medication-filter-group {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .reports-medication-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .reports-medication-list-container {
    padding: 15px;
  }
  
  .reports-medication-header {
    padding: 20px;
  }
  
  .reports-medication-header-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .reports-medication-title {
    font-size: 24px;
  }
  
  .reports-medication-stats {
    grid-template-columns: 1fr;
  }
  
  .reports-medication-table-container {
    overflow-x: auto;
  }
  
  .reports-medication-table {
    min-width: 700px;
  }
  
  .reports-medication-table th,
  .reports-medication-table td {
    padding: 10px 8px;
  }
}

@media (max-width: 480px) {
  .reports-medication-header {
    padding: 15px;
  }
  
  .reports-medication-title {
    font-size: 20px;
  }
  
  .reports-medication-subtitle {
    font-size: 14px;
  }
  
  .reports-medication-stat-card {
    padding: 15px;
  }
  
  .reports-medication-stat-content h3 {
    font-size: 20px;
  }
  
  .reports-medication-filters {
    padding: 15px;
  }
  
  .reports-medication-no-data {
    padding: 30px 15px;
  }
  
  .reports-medication-no-data h3 {
    font-size: 18px;
  }
}
