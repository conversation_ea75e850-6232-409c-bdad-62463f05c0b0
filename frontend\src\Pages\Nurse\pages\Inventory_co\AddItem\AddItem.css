/* Add Item Modal - Namespaced Styles */
.add-item-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1050;
  opacity: 1;
  visibility: visible;
}

.add-item-modal-dialog {
  width: 90%;
  max-width: 700px;
  max-height: 90vh;
  margin: 1rem;
}

.add-item-modal-content {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: 90vh;
}

.add-item-modal-header {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: none;
}

.add-item-modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.add-item-btn-close {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease;
}

.add-item-btn-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.add-item-modal-body {
  padding: 1.5rem;
  flex: 1;
  overflow-y: auto;
}

.add-item-form-group {
  margin-bottom: 1rem;
}

.add-item-form-label {
  display: block;
  margin-bottom: 0.375rem;
  font-weight: 500;
  color: #495057;
  font-size: 0.875rem;
}

.add-item-form-control {
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  box-sizing: border-box;
}

.add-item-form-control:focus {
  border-color: #007bff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.add-item-form-control.is-invalid {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.add-item-alert {
  padding: 0.75rem 1rem;
  margin-top: 0.5rem;
  border: 1px solid transparent;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.add-item-alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.add-item-alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.add-item-status-badge {
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
}

.add-item-status-available {
  color: #155724;
  background-color: #d4edda;
}

.add-item-status-low {
  color: #856404;
  background-color: #fff3cd;
}

.add-item-status-out {
  color: #721c24;
  background-color: #f8d7da;
}

.add-item-modal-footer {
  background-color: #f8f9fa;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.add-item-btn {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.5;
  border-radius: 0.375rem;
  border: 1px solid transparent;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.2s ease;
}

.add-item-btn-secondary {
  background-color: #6c757d;
  border-color: #6c757d;
  color: white;
}

.add-item-btn-secondary:hover {
  background-color: #5a6268;
  border-color: #545b62;
}

.add-item-btn-primary {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.add-item-btn-primary:hover {
  background-color: #0056b3;
  border-color: #004085;
}

.add-item-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.add-item-spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: add-item-spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes add-item-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.add-item-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -0.75rem;
}

.add-item-col-6 {
  flex: 0 0 50%;
  max-width: 50%;
  padding: 0 0.75rem;
}

.add-item-col-12 {
  flex: 0 0 100%;
  max-width: 100%;
  padding: 0 0.75rem;
}

/* Utility Classes */
.add-item-me-1 { margin-right: 0.25rem; }
.add-item-me-2 { margin-right: 0.5rem; }
.add-item-text-danger { color: #dc3545; }
.add-item-text-muted { color: #6c757d; }
.add-item-fw-bold { font-weight: 600; }

@media (max-width: 768px) {
  .add-item-col-6 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .add-item-modal-dialog {
    width: 95%;
    margin: 0.5rem;
  }

  .add-item-modal-body {
    padding: 1rem;
  }
}
