/**
 * Fix CSS conflicts for headers across different pages
 * <PERSON><PERSON>i thiện với hiệu ứng màu từ đậm sang nhạt
 */

/* Fix cho community-header và health-guide-header với gradient đậm sang nhạt */
.community-header,
.health-guide-header {
  background: linear-gradient(135deg, #1a4bbc, #3b82f6, #60a5fa) !important;
  color: white !important;
  z-index: 1 !important;
  position: relative !important;
  overflow: hidden !important;
  box-shadow: 0 4px 20px rgba(37, 99, 235, 0.2) !important;
}

/* Thêm overlay gradient cho header để tạo độ sâu */
.community-header::before,
.health-guide-header::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.15), transparent 70%) !important;
  z-index: 0 !important;
  pointer-events: none !important;
}

/* Fix cho các pattern trang trí */
.community-header::after,
.health-guide-header::after {
  content: '' !important;
  position: absolute !important;
  bottom: 0 !important;
  right: 0 !important;
  width: 240px !important;
  height: 240px !important;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><path fill="rgba(255,255,255,0.1)" d="M42.7,-65.1C53.2,-52.6,58.1,-37.6,65.3,-22.6C72.6,-7.7,82.2,7.1,79.4,19.5C76.6,31.9,61.4,41.8,47.4,49.7C33.4,57.6,20.7,63.5,5.5,67.5C-9.7,71.4,-27.4,73.5,-41.7,66.9C-56,60.2,-66.8,44.8,-71.1,28.6C-75.4,12.4,-73.1,-4.7,-69.6,-22.7C-66,-40.7,-61.3,-59.5,-48.9,-71.4C-36.6,-83.4,-18.3,-88.4,-1.1,-86.9C16.1,-85.3,32.2,-77.1,42.7,-65.1Z" transform="translate(100 100)" /></svg>') !important;
  background-repeat: no-repeat !important;
  background-size: contain !important;
  opacity: 0.15 !important;
  transform: rotate(45deg) !important;
  z-index: 0 !important;
}

/* Thêm một pattern bổ sung bên trái để tăng chiều sâu */
.community-header::after,
.health-guide-header::after {
  content: '' !important;
  position: absolute !important;
  top: -50px !important;
  left: -50px !important;
  width: 180px !important;
  height: 180px !important;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><path fill="rgba(255,255,255,0.1)" d="M42.7,-65.1C53.2,-52.6,58.1,-37.6,65.3,-22.6C72.6,-7.7,82.2,7.1,79.4,19.5C76.6,31.9,61.4,41.8,47.4,49.7C33.4,57.6,20.7,63.5,5.5,67.5C-9.7,71.4,-27.4,73.5,-41.7,66.9C-56,60.2,-66.8,44.8,-71.1,28.6C-75.4,12.4,-73.1,-4.7,-69.6,-22.7C-66,-40.7,-61.3,-59.5,-48.9,-71.4C-36.6,-83.4,-18.3,-88.4,-1.1,-86.9C16.1,-85.3,32.2,-77.1,42.7,-65.1Z" transform="translate(100 100)" /></svg>') !important;
  background-repeat: no-repeat !important;
  background-size: contain !important;
  opacity: 0.15 !important;
  transform: rotate(180deg) !important;
  z-index: 0 !important;
}

/* Pattern ở bên phải */
.community-header::after,
.health-guide-header::after {
  content: '' !important;
  position: absolute !important;
  bottom: -20px !important;
  right: -20px !important;
  width: 240px !important; 
  height: 240px !important;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><path fill="rgba(255,255,255,0.1)" d="M42.7,-65.1C53.2,-52.6,58.1,-37.6,65.3,-22.6C72.6,-7.7,82.2,7.1,79.4,19.5C76.6,31.9,61.4,41.8,47.4,49.7C33.4,57.6,20.7,63.5,5.5,67.5C-9.7,71.4,-27.4,73.5,-41.7,66.9C-56,60.2,-66.8,44.8,-71.1,28.6C-75.4,12.4,-73.1,-4.7,-69.6,-22.7C-66,-40.7,-61.3,-59.5,-48.9,-71.4C-36.6,-83.4,-18.3,-88.4,-1.1,-86.9C16.1,-85.3,32.2,-77.1,42.7,-65.1Z" transform="translate(100 100)" /></svg>') !important;
  background-repeat: no-repeat !important;
  background-size: contain !important;
  opacity: 0.15 !important;
  transform: rotate(45deg) !important;
  z-index: 0 !important;
}

/* Fix cho article-card để không xung đột với .card trong ParentLayout */
.article-card {
  transform: none !important;
  transition: all 0.3s ease !important;
}

.article-card:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1) !important;
}

/* Đảm bảo content của header luôn hiển thị */
.community-title,
.health-guide-header-content,
.community-actions {
  position: relative !important;
  z-index: 2 !important;
}

/* Fix màu và hiển thị text trong header */
.community-title h1,
.health-guide-header h1 {
  color: white !important;
  font-weight: 700 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.15) !important;
  margin-bottom: 10px !important;
}

.community-title p,
.health-guide-header p {
  color: rgba(255, 255, 255, 0.9) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  opacity: 0.9 !important;
}

/* Thêm hiệu ứng cho health-guide-header */
.health-guide-header {
  padding: 50px 30px !important;
  border-radius: 12px !important;
  border-bottom: 4px solid rgba(26, 75, 188, 0.3) !important;
}

/* Thêm hiệu ứng cho community-header */
.community-header {
  padding: 40px 30px !important;
  border-radius: 12px !important;
  border-bottom: 4px solid rgba(26, 75, 188, 0.3) !important;
}

/* Responsive adjustment */
@media (max-width: 768px) {
  .community-header,
  .health-guide-header {
    padding: 30px 20px !important;
  }
}

/* Thêm hiệu ứng subtle khi hover vào header */
.community-header:hover,
.health-guide-header:hover {
  box-shadow: 0 6px 24px rgba(37, 99, 235, 0.25) !important;
}