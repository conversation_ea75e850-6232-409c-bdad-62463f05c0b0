/* CreateVaccinationPlan - Modern Design */
.admin-create-vaccination-plan {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(to bottom, #f8fafc, #f1f5f9);
  min-height: 100vh;
}

/* Header Section */
.admin-create-vaccination-form-header {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 48px 40px;
  border-radius: 20px;
  margin-bottom: 40px;
  text-align: center;
  box-shadow: 0 10px 40px rgba(59, 130, 246, 0.2);
  position: relative;
  overflow: hidden;
}

.admin-create-vaccination-form-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  transform: rotate(45deg);
}

.admin-create-vaccination-header-icon {
  font-size: 3.5rem;
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
  color: rgba(255, 255, 255, 0.9);
}

.admin-create-vaccination-header-content {
  position: relative;
  z-index: 1;
}

.admin-create-vaccination-header-content h2 {
  margin: 0 0 16px 0;
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.admin-create-vaccination-header-content p {
  margin: 0;
  font-size: 1.125rem;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.9);
}

/* Notifications */
.create-vaccination-notification {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  font-weight: 500;
}

.create-vaccination-notification.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.notification.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.notification-icon {
  font-size: 1.2rem;
}

.notification-content h4 {
  margin: 0 0 5px 0;
  font-size: 1rem;
}

.notification-content p {
  margin: 0;
  font-size: 0.9rem;
}

/* Form Container */
.admin-vaccination-form {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  margin-bottom: 40px;
  border: 1px solid #e2e8f0;
}

/* Form Sections */
.admin-form-section {
  margin-bottom: 40px;
  background: #ffffff;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  border: 1px solid #f1f5f9;
}

.admin-form-section h3 {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 24px 0;
  font-size: 1.5rem;
  color: #1e293b;
  padding-bottom: 16px;
  border-bottom: 2px solid #e2e8f0;
  font-weight: 700;
}

.admin-section-icon {
  color: #3b82f6;
  font-size: 1.25rem;
}

/* Form Grid */
.admin-form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
}

/* Form Groups */
.admin-create-vaccination-form-group {
  margin-bottom: 24px;
}

.admin-create-vaccination-form-group.full-width {
  grid-column: 1 / -1;
}

.admin-create-vaccination-form-group label {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: #333;
  font-size: 0.95rem;
}

.admin-label-icon {
  color: #3b82f6;
  font-size: 1rem;
}

/* Form Inputs */
.admin-create-vaccination-form-group input,
.admin-create-vaccination-form-group select,
.admin-create-vaccination-form-group textarea {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-sizing: border-box;
  background: #ffffff;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-create-vaccination-form-group input:focus,
.admin-create-vaccination-form-group select:focus,
.admin-create-vaccination-form-group textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.admin-create-vaccination-form-group input::placeholder,
.admin-create-vaccination-form-group textarea::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.admin-create-vaccination-form-group textarea {
  min-height: 140px;
  resize: vertical;
  line-height: 1.6;
}

.admin-helper-text {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 8px;
  font-style: italic;
  font-weight: 400;
}

/* Submit Button */
.admin-form-actions {
  text-align: center;
  margin-top: 40px;
  padding-top: 32px;
  border-top: 1px solid #e2e8f0;
}

.admin-submit-button {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  padding: 16px 32px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 16px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-submit-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(59, 130, 246, 0.4);
}

.admin-submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.admin-spinning {
  animation: admin-spin 1s linear infinite;
}

@keyframes admin-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Info Cards */
.admin-info-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  margin-top: 40px;
}

.admin-info-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  display: flex;
  gap: 16px;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
}

.admin-info-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border-color: #3b82f6;
}

/* Class Selection Modal */
.admin-class-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  backdrop-filter: blur(4px);
}

.admin-class-modal-content {
  background: #ffffff;
  border-radius: 20px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  border: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
}

.admin-class-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.admin-class-modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

.admin-class-modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.admin-class-modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.admin-class-modal-body {
  padding: 32px;
  overflow-y: auto;
  flex: 1;
}

.admin-class-search-container {
  margin-bottom: 24px;
}

.admin-class-search-input {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-class-search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

.admin-class-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.admin-grade-group {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.admin-grade-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e2e8f0;
}

.admin-grade-checkbox {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  font-weight: 600;
  color: #1e293b;
}

.admin-grade-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #3b82f6;
}

.admin-grade-name {
  font-size: 1.1rem;
}

.admin-grade-count {
  color: #6b7280;
  font-weight: 400;
  font-size: 0.9rem;
}

.admin-grade-classes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 8px;
}

.admin-class-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;
  font-size: 0.875rem;
}

.admin-class-item:hover {
  background: #f1f5f9;
  border-color: #3b82f6;
}

.admin-class-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #3b82f6;
}

.admin-class-modal-footer {
  padding: 24px 32px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.admin-selected-count {
  font-weight: 600;
  color: #374151;
}

.admin-modal-buttons {
  display: flex;
  gap: 12px;
}

.admin-modal-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.admin-modal-button.cancel {
  background: #f3f4f6;
  color: #374151;
}

.admin-modal-button.cancel:hover {
  background: #e5e7eb;
}

.admin-modal-button.confirm {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.admin-modal-button.confirm:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
}

/* Class Selection Button */
.admin-class-selection-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 16px 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  font-weight: 500;
  color: #374151;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-class-selection-button:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-1px);
}

.admin-class-selection-text {
  display: flex;
  align-items: center;
  gap: 12px;
}

.admin-class-selection-icon {
  color: #3b82f6;
  font-size: 1.1rem;
}

.admin-class-selection-arrow {
  color: #6b7280;
  transition: transform 0.2s ease;
}

/* Selected Classes Display */
.admin-selected-classes-display {
  margin-top: 20px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.admin-selected-classes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.admin-selected-classes-title {
  font-weight: 600;
  color: #374151;
  font-size: 1rem;
}

.admin-clear-all-button {
  padding: 8px 16px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.admin-clear-all-button:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

.admin-class-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.admin-class-tag {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.admin-remove-class-button {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  padding: 2px;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
}

.admin-remove-class-button:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Card Icon and Content */
.admin-card-icon {
  font-size: 2.5rem;
  margin-bottom: 12px;
  opacity: 0.8;
}

.admin-card-content h4 {
  margin: 0 0 12px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
}

.admin-card-content p {
  margin: 0;
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.5;
}

/* Field Error Styles */
.admin-field-error {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 8px;
  font-weight: 500;
}

/* Loading Classes */
.admin-loading-classes {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px;
  color: #6b7280;
  font-weight: 500;
}

/* No Search Results */
.admin-no-search-results {
  padding: 20px;
  text-align: center;
  color: #6b7280;
  font-style: italic;
}

/* Vaccine Selection Styles */
.admin-loading-vaccines {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px;
  color: #6b7280;
  font-weight: 500;
}

.admin-vaccine-list {
  display: grid;
  gap: 16px;
}

.admin-vaccine-item {
  background: #ffffff;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.admin-vaccine-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-1px);
}

.admin-vaccine-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  cursor: pointer;
}

.admin-vaccine-checkbox input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: #3b82f6;
  margin-top: 2px;
}

.admin-checkmark {
  display: none;
}

.admin-vaccine-info {
  flex: 1;
}

.admin-vaccine-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
}

.admin-vaccine-details {
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.5;
}

.admin-vaccine-description {
  color: #6b7280;
  font-style: italic;
}

.admin-no-vaccines {
  padding: 40px 20px;
  text-align: center;
  color: #6b7280;
  font-style: italic;
  background: #f8fafc;
  border-radius: 12px;
  border: 2px dashed #e2e8f0;
}

.admin-selected-vaccines-summary {
  margin-top: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-radius: 12px;
  border: 1px solid #3b82f6;
}

.admin-selected-vaccines-summary strong {
  color: #1e40af;
  font-weight: 600;
}

.admin-selected-vaccines-summary span {
  color: #1e40af;
  margin-left: 8px;
}

/* Vaccine Modal Styles */
.admin-vaccine-modal-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.admin-vaccine-modal-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  background: #ffffff;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.admin-vaccine-modal-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-1px);
}

.admin-vaccine-modal-item.selected {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.admin-vaccine-modal-checkbox {
  margin-top: 2px;
}

.admin-vaccine-modal-checkbox input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: #3b82f6;
}

.admin-vaccine-modal-info {
  flex: 1;
}

.admin-vaccine-modal-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
}

.admin-vaccine-modal-details {
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.5;
}

.admin-vaccine-modal-description {
  margin-top: 4px;
  color: #6b7280;
  font-style: italic;
}

.admin-vaccine-modal-icon {
  color: #3b82f6;
  font-size: 1.5rem;
  margin-top: 4px;
}

.card-icon {
  font-size: 2rem;
  width: 50px;
  flex-shrink: 0;
}

.card-content h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.1rem;
}

.card-content p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Vaccine Selection Styles */
.loading-vaccines {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 20px;
  text-align: center;
  color: #666;
}

.vaccine-list {
  display: grid;
  gap: 15px;
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.vaccine-item {
  background: white;
  border-radius: 8px;
  padding: 0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
}

.vaccine-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.vaccine-item label {
  padding: 15px;
  display: block;
  width: 100%;
}

.vaccine-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  width: 100%;
  position: relative;
}

.vaccine-checkbox input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-radius: 4px;
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  z-index: 1;
}

.vaccine-checkbox input[type="checkbox"]:checked + .checkmark {
  background: #4CAF50;
  border-color: #4CAF50;
}

.vaccine-checkbox input[type="checkbox"]:checked + .checkmark::after {
  content: '\2714'; /* Unicode checkmark symbol */
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 14px;
  font-weight: bold;
  line-height: 1;
}

/* Add a subtle hover effect to make it clear the whole box is clickable */
.vaccine-item:hover .checkmark {
  border-color: #4CAF50;
}

.vaccine-info {
  flex: 1;
  padding-left: 45px; /* Make space for the checkbox */
  width: 100%;
}

.vaccine-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  font-size: 1rem;
}

.vaccine-details {
  font-size: 0.85rem;
  color: #666;
  line-height: 1.4;
  padding-right: 10px;
}

.vaccine-description {
  display: block;
  margin-top: 3px;
  font-style: italic;
}

/* Ensure the whole item is clickable */
.vaccine-item label {
  cursor: pointer;
}

.no-vaccines {
  text-align: center;
  color: #999;
  padding: 30px;
}

/* Class Management Styles */
.class-selection-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.loading-classes {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 20px;
  text-align: center;
  color: #666;
  justify-content: center;
}

.class-selector-container {
  margin-bottom: 20px;
  position: relative;
}

.dropdown-label {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  font-size: 1rem;
}

/* Search Input Container */
.class-search-container {
  position: relative;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  transition: border-color 0.3s ease;
}

.search-input-wrapper:focus-within {
  border-color: #4CAF50;
}

.class-search-input {
  flex: 1;
  padding: 12px 15px;
  border: none;
  outline: none;
  font-size: 1rem;
  background: transparent;
}

.class-search-input::placeholder {
  color: #888;
}

.dropdown-toggle-btn {
  padding: 12px 15px;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dropdown-toggle-btn:hover {
  color: #4CAF50;
}

.dropdown-toggle-btn .rotated {
  transform: rotate(180deg);
}

/* Dropdown Content */
.class-dropdown-content {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 400px;
  overflow-y: auto;
  margin-top: 5px;
}

/* Grade Groups */
.grade-group {
  border-bottom: 1px solid #f0f0f0;
}

.grade-group:last-child {
  border-bottom: none;
}

.grade-header {
  padding: 12px 15px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  position: sticky;
  top: 0;
  z-index: 10;
}

.grade-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 600;
  color: #333;
  user-select: none;
}

.grade-checkbox input[type="checkbox"] {
  margin-right: 10px;
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.grade-name {
  flex: 1;
  font-size: 1rem;
}

.grade-count {
  font-size: 0.85rem;
  color: #666;
  font-weight: normal;
}

/* Class Items */
.grade-classes {
  padding: 5px 0;
}

.class-item {
  display: flex;
  align-items: center;
  padding: 8px 35px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  user-select: none;
}

.class-item:hover {
  background: #f0f8ff;
}

.class-item input[type="checkbox"] {
  margin-right: 10px;
  width: 14px;
  height: 14px;
  cursor: pointer;
}

.class-item input[type="checkbox"]:checked + .class-name {
  color: #4CAF50;
  font-weight: 500;
}

.class-name {
  font-size: 0.9rem;
  color: #333;
}

.no-classes,
.no-search-results {
  text-align: center;
  color: #999;
  padding: 30px;
  font-style: italic;
}

.selected-classes {
  margin-top: 20px;
  padding: 15px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.selected-classes h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.class-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 10px;
}

.class-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 0.85rem;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.2);
  transition: all 0.3s ease;
}

.class-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

.remove-class-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 2px;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 0.7rem;
}

.remove-class-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.class-selection-summary {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px dashed #e0e0e0;
  font-size: 0.85rem;
  color: #666;
  font-style: italic;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.clear-all-btn {
  background: linear-gradient(135deg, #f44336, #d32f2f);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.clear-all-btn:hover {
  background: linear-gradient(135deg, #d32f2f, #c62828);
  transform: translateY(-1px);
}

/* Selected Vaccines Summary */
.selected-vaccines-summary {
  margin-top: 15px;
  padding: 15px;
  background: #e8f5e8;
  border: 1px solid #4CAF50;
  border-radius: 8px;
  font-size: 0.9rem;
}

.selected-vaccines-summary strong {
  display: block;
  color: #2e7d32;
  margin-bottom: 5px;
}

.selected-vaccines-summary span {
  color: #1b5e20;
  font-style: italic;
}

/* Field Error Styles */
.field-error {
  color: #f44336;
  font-size: 0.8rem;
  margin-top: 5px;
  display: block;
}

.create-vaccination-form-group input.error,
.create-vaccination-form-group textarea.error,
.create-vaccination-form-group select.error {
  border-color: #f44336;
  box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
}

.create-vaccination-form-group input.error:focus,
.create-vaccination-form-group textarea.error:focus,
.create-vaccination-form-group select.error:focus {
  border-color: #f44336;
  box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .create-vaccination-plan {
    padding: 15px;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .header-content h2 {
    font-size: 1.5rem;
  }
  
  .header-content p {
    font-size: 1rem;
  }
  
  .vaccination-form {
    padding: 20px;
  }
  
  .vaccine-list {
    max-height: 250px;
  }
  
  .class-dropdown-content {
    max-height: 300px;
    position: relative;
    margin-top: 10px;
  }
  
  .grade-header {
    padding: 10px 12px;
  }
  
  .class-item {
    padding: 6px 25px;
  }
  
  .search-input-wrapper {
    flex-direction: column;
  }
  
  .class-search-input {
    padding: 10px 12px;
  }
  
  .dropdown-toggle-btn {
    padding: 8px 12px;
  }
  
  .class-tags {
    justify-content: center;
  }
  
  .info-cards {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .header-icon {
    font-size: 2rem;
  }
  
  .header-content h2 {
    font-size: 1.3rem;
  }
  
  .form-section h3 {
    font-size: 1.1rem;
  }
  
  .vaccination-form {
    padding: 15px;
  }
}