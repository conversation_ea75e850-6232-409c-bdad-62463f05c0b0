/* Admin Report Header - Unified Design with Color Themes */
.admin-report-header {
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  color: white;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.admin-report-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  transform: rotate(45deg);
}

.admin-report-header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
  position: relative;
  z-index: 1;
}

.admin-report-header-title {
  color: white;
  font-size: 32px;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.admin-report-header-title i {
  font-size: 28px;
}

.admin-report-header-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  font-weight: 500;
  margin: 8px 0 0 0;
  position: relative;
  z-index: 1;
}

/* Color Themes */

/* 1. Vaccine Reports - Blue */
.admin-report-header-blue {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
}

/* 2. Medication Reports - Green */
.admin-report-header-green {
  background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
}

/* 3. Health Checkup Reports - Purple */
.admin-report-header-purple {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%);
}

/* 4. Vaccination Reports - Orange */
.admin-report-header-orange {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
}

/* 5. Student Management - Teal */
.admin-report-header-teal {
  background: linear-gradient(135deg, #14b8a6 0%, #0d9488 50%, #0f766e 100%);
}

/* Back Button Styling within Header */
.admin-report-header .back-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.admin-report-header .back-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-report-header {
    padding: 24px;
    margin-bottom: 24px;
  }

  .admin-report-header-title {
    font-size: 24px;
  }

  .admin-report-header-title i {
    font-size: 20px;
  }

  .admin-report-header-subtitle {
    font-size: 14px;
  }

  .admin-report-header-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
