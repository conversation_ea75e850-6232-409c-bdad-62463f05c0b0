/* ===== MODERN USER TABLE DESIGN ===== */

/* Ensure React Icons are displayed correctly */
.action-btn svg,
.status-toggle svg,
.loading-icon,
.empty-icon {
  vertical-align: middle;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}

/* Modern Design Variables */
:root {
  /* Modern Color Palette */
  --table-white: #ffffff;
  --table-gray-50: #f8fafc;
  --table-gray-100: #f1f5f9;
  --table-gray-200: #e2e8f0;
  --table-gray-300: #cbd5e1;
  --table-gray-400: #94a3b8;
  --table-gray-500: #64748b;
  --table-gray-600: #475569;
  --table-gray-700: #334155;
  --table-gray-800: #1e293b;
  --table-gray-900: #0f172a;

  /* Blue Theme Colors */
  --table-blue-50: #eff6ff;
  --table-blue-100: #dbeafe;
  --table-blue-500: #3b82f6;
  --table-blue-600: #2563eb;
  --table-blue-700: #1d4ed8;

  /* Status Colors */
  --table-green: #10b981;
  --table-green-light: #d1fae5;
  --table-red: #ef4444;
  --table-red-light: #fee2e2;
  --table-yellow: #f59e0b;
  --table-yellow-light: #fef3c7;

  /* Typography */
  --table-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --table-font-size-xs: 0.75rem;
  --table-font-size-sm: 0.875rem;
  --table-font-size-base: 1rem;
  --table-font-size-lg: 1.125rem;

  /* Spacing */
  --table-space-1: 0.25rem;
  --table-space-2: 0.5rem;
  --table-space-3: 0.75rem;
  --table-space-4: 1rem;
  --table-space-6: 1.5rem;
  --table-space-8: 2rem;

  /* Borders & Radius */
  --table-border-width: 1px;
  --table-border-color: var(--table-gray-200);
  --table-radius: 0.5rem;
  --table-radius-lg: 0.75rem;
  --table-radius-xl: 1rem;

  /* Shadows */
  --table-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  --table-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --table-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* ===== MODERN TABLE CONTAINER ===== */
.admin-modern-table-container,
.modern-table-container {
  background: var(--table-white);
  border-radius: var(--table-radius-xl);
  overflow: hidden;
  border: none;
  box-shadow: var(--table-shadow-lg);
  font-family: var(--table-font-family);
}

/* ===== MODERN TABLE HEADER ===== */
.admin-table-header,
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--table-space-6) var(--table-space-8);
  background: linear-gradient(135deg, var(--table-blue-50), var(--table-white));
  border-bottom: 1px solid var(--table-gray-200);
  position: relative;
}

.table-header::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--table-blue-500), var(--table-blue-600), var(--table-blue-700));
}

.admin-table-header h3,
.table-header h3 {
  font-size: var(--table-font-size-lg);
  font-weight: 700;
  color: var(--table-gray-900);
  margin: 0;
  letter-spacing: -0.025em;
}

.admin-table-count,
.table-count {
  font-size: var(--table-font-size-sm);
  color: var(--table-blue-600);
  background: var(--table-blue-100);
  padding: var(--table-space-2) var(--table-space-4);
  border-radius: var(--table-radius-lg);
  font-weight: 600;
  border: 1px solid var(--table-blue-200);
}

/* ===== TABLE SCROLL ===== */
.admin-table-scroll,
.table-scroll {
  overflow-x: auto;
}

/* ===== MODERN TABLE ===== */
.admin-modern-table,
.modern-table {
  width: 100%;
  border-collapse: collapse;
  font-family: var(--table-font-family);
}

/* ===== TABLE LOADING & EMPTY STATES ===== */
.admin-table-loading-state,
.table-loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--table-space-8);
  text-align: center;
  background: var(--table-white);
  border-radius: var(--table-radius-xl);
  box-shadow: var(--table-shadow-lg);
  margin: var(--table-space-6) 0;
}

.admin-loading-content,
.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--table-space-4);
}

.admin-loading-icon,
.loading-icon {
  font-size: 2rem;
  color: var(--table-blue-500);
  animation: spin 1s linear infinite;
}

.admin-table-empty-state,
.table-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--table-space-8);
  text-align: center;
  background: var(--table-white);
  border-radius: var(--table-radius-xl);
  box-shadow: var(--table-shadow-lg);
  margin: var(--table-space-6) 0;
}

.admin-empty-content,
.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--table-space-4);
}

.admin-empty-icon,
.empty-icon {
  font-size: 3rem;
  color: var(--table-gray-400);
}

/* ===== TABLE ROWS ===== */
.admin-table-row,
.table-row {
  transition: all 0.3s ease;
  border-bottom: 1px solid var(--table-gray-200);
}

.admin-table-row:hover,
.table-row:hover {
  background: var(--table-blue-50);
}

.admin-table-row.even,
.table-row.even {
  background: var(--table-white);
}

.admin-table-row.odd,
.table-row.odd {
  background: var(--table-gray-50);
}

/* ===== BULK EMAIL ACTIONS ===== */
.admin-bulk-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--table-space-4) var(--table-space-6);
  background: linear-gradient(135deg, var(--table-blue-50), var(--table-white));
  border-bottom: 1px solid var(--table-gray-200);
  gap: var(--table-space-4);
}

.admin-bulk-selection {
  display: flex;
  align-items: center;
  gap: var(--table-space-3);
}

.admin-selection-info {
  font-size: var(--table-font-size-sm);
  color: var(--table-gray-600);
  font-weight: 500;
}

.admin-bulk-buttons {
  display: flex;
  gap: var(--table-space-3);
}

.admin-bulk-btn {
  display: flex;
  align-items: center;
  gap: var(--table-space-2);
  padding: var(--table-space-2) var(--table-space-4);
  border: none;
  border-radius: var(--table-radius);
  font-size: var(--table-font-size-sm);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-bulk-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.admin-bulk-btn-all {
  background: var(--table-blue-100);
  color: var(--table-blue-600);
  border: 1px solid var(--table-blue-200);
}

.admin-bulk-btn-all:hover:not(:disabled) {
  background: var(--table-blue-500);
  color: var(--table-white);
  transform: scale(1.05);
  box-shadow: var(--table-shadow);
}

.admin-bulk-btn-selected {
  background: var(--table-green-light);
  color: var(--table-green);
  border: 1px solid var(--table-green);
}

.admin-bulk-btn-selected:hover:not(:disabled) {
  background: var(--table-green);
  color: var(--table-white);
  transform: scale(1.05);
  box-shadow: var(--table-shadow);
}

/* ===== CHECKBOX COLUMN ===== */
.admin-col-checkbox,
.col-checkbox {
  width: 50px;
  text-align: center;
}

.admin-checkbox-input {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: var(--table-blue-600);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.admin-checkbox-input:hover {
  transform: scale(1.1);
  box-shadow: 0 0 0 2px var(--table-blue-100);
}

.admin-checkbox-input:checked {
  background-color: var(--table-blue-600);
  border-color: var(--table-blue-600);
}

.admin-checkbox-input:focus {
  outline: 2px solid var(--table-blue-300);
  outline-offset: 2px;
}

/* ===== SELECTED ROW STYLING ===== */
.admin-table-row.selected {
  background: var(--table-blue-50) !important;
  border-left: 3px solid var(--table-blue-500);
}

.admin-table-row.selected:hover {
  background: var(--table-blue-100) !important;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .admin-bulk-actions {
    flex-direction: column;
    gap: var(--table-space-3);
    align-items: stretch;
  }

  .admin-bulk-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }

  .admin-bulk-btn {
    flex: 1;
    min-width: 120px;
    justify-content: center;
  }

  .admin-bulk-btn span {
    display: none;
  }

  .admin-bulk-btn-selected span {
    display: inline;
  }

  .admin-col-checkbox {
    width: 40px;
  }

  .admin-checkbox-input {
    width: 16px;
    height: 16px;
  }
}

@media (max-width: 480px) {
  .admin-bulk-actions {
    padding: var(--table-space-3);
  }

  .admin-selection-info {
    font-size: var(--table-font-size-xs);
    text-align: center;
  }

  .admin-bulk-buttons {
    gap: var(--table-space-2);
  }

  .admin-bulk-btn {
    padding: var(--table-space-2) var(--table-space-3);
    font-size: var(--table-font-size-xs);
  }

  .admin-col-checkbox {
    width: 35px;
  }

  .admin-checkbox-input {
    width: 14px;
    height: 14px;
  }
}

/* ===== TABLE COLUMNS ===== */
.admin-col-id,
.col-id {
  width: 80px;
  text-align: center;
}

.admin-col-email,
.col-email {
  min-width: 200px;
}

.admin-col-phone,
.col-phone {
  width: 150px;
}

.admin-col-role,
.col-role {
  width: 120px;
  text-align: center;
}

.admin-col-status,
.col-status {
  width: 120px;
  text-align: center;
}

.admin-col-email-action,
.col-email-action {
  width: 80px;
  text-align: center;
}

.admin-col-actions,
.col-actions {
  width: 120px;
  text-align: center;
}

/* ===== TABLE CELL CONTENT ===== */
.admin-user-id,
.user-id {
  font-weight: 600;
  color: var(--table-gray-700);
  font-size: var(--table-font-size-sm);
}

.admin-user-email,
.user-email {
  color: var(--table-gray-900);
  font-size: var(--table-font-size-sm);
}

.admin-user-phone,
.user-phone {
  color: var(--table-gray-700);
  font-size: var(--table-font-size-sm);
}

/* ===== ROLE BADGES ===== */
.admin-role-badge,
.role-badge {
  padding: var(--table-space-1) var(--table-space-3);
  border-radius: var(--table-radius);
  font-size: var(--table-font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-role-badge.admin,
.role-badge.admin {
  background: var(--table-red-light);
  color: var(--table-red);
}

.admin-role-badge.nurse,
.role-badge.nurse {
  background: var(--table-blue-100);
  color: var(--table-blue-600);
}

.admin-role-badge.parent,
.role-badge.parent {
  background: var(--table-green-light);
  color: var(--table-green);
}

/* ===== STATUS TOGGLE ===== */
.admin-status-toggle,
.status-toggle {
  padding: var(--table-space-2) var(--table-space-3);
  border: none;
  border-radius: var(--table-radius);
  font-size: var(--table-font-size-xs);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: var(--table-space-1);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-status-toggle.active,
.status-toggle.active {
  background: var(--table-green-light);
  color: var(--table-green);
}

.admin-status-toggle.inactive,
.status-toggle.inactive {
  background: var(--table-red-light);
  color: var(--table-red);
}

.admin-status-toggle:hover,
.status-toggle:hover {
  transform: scale(1.05);
  box-shadow: var(--table-shadow);
}

.admin-toggle-icon,
.toggle-icon {
  font-size: 1rem;
}

/* ===== EMAIL BUTTON ===== */
.admin-email-btn,
.email-btn {
  padding: var(--table-space-2);
  border: none;
  border-radius: var(--table-radius);
  background: var(--table-blue-100);
  color: var(--table-blue-600);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.admin-email-btn:hover,
.email-btn:hover {
  background: var(--table-blue-500);
  color: var(--table-white);
  transform: scale(1.1);
}

.admin-email-btn.sent,
.email-btn.sent {
  background: var(--table-green-light);
  color: var(--table-green);
}

.admin-spin-icon,
.spin-icon {
  animation: spin 1s linear infinite;
}

.admin-check-icon,
.check-icon {
  color: var(--table-green);
}

.admin-envelope-icon,
.envelope-icon {
  color: var(--table-blue-600);
}

/* ===== ACTION BUTTONS ===== */
.admin-action-buttons,
.action-buttons {
  display: flex;
  gap: var(--table-space-1);
  justify-content: center;
}

.admin-action-btn,
.action-btn {
  padding: var(--table-space-2);
  border: none;
  border-radius: var(--table-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.admin-action-btn.view,
.action-btn.view {
  background: var(--table-blue-100);
  color: var(--table-blue-600);
}

.admin-action-btn.view:hover,
.action-btn.view:hover {
  background: var(--table-blue-500);
  color: var(--table-white);
  transform: scale(1.1);
}

.admin-action-btn.edit,
.action-btn.edit {
  background: var(--table-yellow-light);
  color: var(--table-yellow);
}

.admin-action-btn.edit:hover,
.action-btn.edit:hover {
  background: var(--table-yellow);
  color: var(--table-white);
  transform: scale(1.1);
}

.modern-table thead {
  background: var(--color-gray-50);
}

.modern-table th {
  padding: 14px 16px;
  text-align: left;
  font-weight: 600;
  font-size: 14px;
  color: var(--color-gray-800);
  border-bottom: 1px solid var(--color-gray-200);
  white-space: nowrap;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.modern-table tbody tr {
  border-bottom: 1px solid var(--color-gray-100);
}

.modern-table tbody tr:hover {
  background: var(--color-gray-50);
}

.modern-table tbody tr:last-child {
  border-bottom: none;
}

.modern-table td {
  padding: 14px 16px;
  font-size: 14px;
  vertical-align: middle;
}

/* Cell Content */
.user-id {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: var(--color-gray-600);
  background: var(--color-gray-100);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius);
  font-size: var(--font-size-xs);
  display: inline-block;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.user-name {
  font-weight: 500;
  color: var(--color-gray-900);
}

.user-email {
  color: var(--color-gray-600);
  font-size: var(--font-size-xs);
}

.user-phone {
  font-family: 'Courier New', monospace;
  color: var(--color-gray-600);
  background: var(--color-gray-50);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius);
  font-size: var(--font-size-xs);
  display: inline-block;
}

/* Role Badges */
.role-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid transparent;
}

.role-badge.admin {
  background: #fef2f2;
  color: #991b1b;
  border-color: #fecaca;
}

.role-badge.nurse {
  background: #eff6ff;
  color: #1e40af;
  border-color: #bfdbfe;
}

.role-badge.parent {
  background: #f0fdf4;
  color: #166534;
  border-color: #bbf7d0;
}

/* Status Toggle */
.status-toggle {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid transparent;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: #ffffff;
  outline: none;
  min-width: 130px;
  justify-content: center;
  position: relative;
}

.status-toggle:focus {
  outline: 2px solid rgba(16, 185, 129, 0.2);
  outline-offset: 2px;
}

.status-toggle.active {
  color: #065f46;
  background: #d1fae5;
  border-color: #a7f3d0;
}

.status-toggle.active:hover {
  background: #a7f3d0;
  border-color: #6ee7b7;
}

.status-toggle.active::before {
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #10b981;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
}

.status-toggle.inactive {
  color: #6b7280;
  background: #f9fafb;
  border-color: #e5e7eb;
}

.status-toggle.inactive:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.toggle-icon {
  font-size: 18px;
  margin-right: 4px;
  color: inherit;
}

/* Action Buttons */
.action-buttons {
  display: flex !important;
  flex-direction: row !important;
  gap: 12px;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-width: 120px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  background: #ffffff;
  outline: none;
  position: relative;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.action-btn svg {
  width: 16px;
  height: 16px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-btn:focus {
  outline: 2px solid rgba(59, 130, 246, 0.2);
  outline-offset: 2px;
}

.action-btn.view {
  color: #3b82f6;
  border-color: #bfdbfe;
  background: #eff6ff;
}

.action-btn.view:hover:not(:disabled) {
  background: #dbeafe;
  border-color: #93c5fd;
}

.action-btn.view svg {
  color: #3b82f6;
}

.action-btn.edit {
  color: #d97706 !important;
  border-color: #fbbf24 !important;
  background: #fef3c7 !important;
}

.action-btn.edit:hover:not(:disabled) {
  background: #fde68a !important;
  border-color: #f59e0b !important;
  color: #b45309 !important;
}

.action-btn.edit svg {
  color: #d97706 !important;
}

.action-btn.edit:hover:not(:disabled) svg {
  color: #b45309 !important;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f9fafb;
  color: #9ca3af;
  border-color: #e5e7eb;
}

.action-btn:disabled svg {
  color: #9ca3af;
}

.action-btn:disabled:hover {
  transform: none;
  box-shadow: none;
  background: #f9fafb;
}

/* Email Button Styling */
.email-btn {
  background: var(--color-blue) !important;
  color: var(--color-white) !important;
  border: 1px solid var(--color-blue) !important;
  border-radius: var(--radius) !important;
  padding: 6px !important;
  font-size: 14px !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 36px !important;
  min-height: 36px !important;
  position: relative !important;
}

.email-btn svg {
  width: 16px !important;
  height: 16px !important;
  display: block !important;
  margin: 0 auto !important;
}

.email-btn:hover:not(:disabled) {
  background: #2563eb !important;
  border-color: #2563eb !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3) !important;
}

.email-btn:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

.email-btn.sent {
  background: var(--color-green) !important;
  border-color: var(--color-green) !important;
}

.email-btn.sent:hover {
  background: var(--color-green) !important;
  border-color: var(--color-green) !important;
  transform: none !important;
}

.email-btn .spin-icon {
  animation: spin 1s linear infinite !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.email-btn .check-icon {
  color: var(--color-white) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.email-btn .envelope-icon {
  color: var(--color-white) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Loading State */
.table-loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  background: var(--color-white);
  border-radius: var(--radius);
  border: var(--border-width) solid var(--border-color);
}

.loading-content {
  text-align: center;
  padding: var(--space-6);
}

.loading-icon {
  font-size: 2.5rem;
  color: var(--color-gray-600);
  margin-bottom: var(--space-4);
}

.loading-content h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0 0 var(--space-2) 0;
}

.loading-content p {
  color: var(--color-gray-600);
  margin: 0;
  font-size: var(--font-size-sm);
}

/* Empty State */
.table-empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  background: var(--color-white);
  border-radius: var(--radius);
  border: var(--border-width) solid var(--border-color);
}

.empty-content {
  text-align: center;
  padding: var(--space-8);
  max-width: 400px;
}

.empty-icon {
  font-size: 4rem;
  color: var(--color-gray-300);
  margin-bottom: var(--space-4);
}

.empty-content h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0 0 var(--space-3) 0;
}

.empty-content p {
  color: var(--color-gray-600);
  line-height: 1.5;
  margin: 0;
  font-size: var(--font-size-sm);
}

/* Column Widths */
.col-id { width: 100px; }
.col-email { width: 180px; }
.col-phone { width: 120px; }
.col-role { width: 100px; }
.col-status { width: 120px; }
.col-email-action { width: 80px; min-width: 80px; }
.col-email-sent { width: 140px; }
.col-actions { width: 140px; min-width: 140px; }

/* Responsive */
@media (max-width: 1024px) {
  .table-header {
    padding: var(--space-3);
  }
  
  .modern-table th,
  .modern-table td {
    padding: var(--space-3);
  }
  
  .col-email { width: 180px; }
  .col-phone { width: 120px; }
}

@media (max-width: 768px) {
  .table-header {
    flex-direction: column;
    gap: var(--space-3);
    align-items: stretch;
  }
  
  .table-count {
    text-align: center;
  }
  
  .modern-table th,
  .modern-table td {
    padding: var(--space-2);
    font-size: var(--font-size-xs);
  }
  
  .action-buttons {
    gap: 8px;
    flex-direction: row;
  }
  
  .action-btn {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }
  
  /* Hide some columns on mobile */
  .col-phone,
  .col-email,
  .col-email-sent {
    display: none;
  }
  
  .role-badge,
  .status-toggle {
    font-size: 10px;
    padding: 3px 8px;
  }
  
  .user-id,
  .user-phone {
    font-size: 10px;
    padding: 2px 6px;
  }
  
  .toggle-icon {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .modern-table-container {
    margin: 0 -var(--space-4);
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
  
  .table-header {
    padding: var(--space-3);
  }
  
  .table-header h3 {
    font-size: var(--font-size-base);
  }
  
  .modern-table th,
  .modern-table td {
    padding: var(--space-2);
  }
  
  /* Show only essential columns */
  .col-id,
  .col-phone,
  .col-email,
  .col-email-sent {
    display: none;
  }
  
  .col-username {
    width: auto;
  }
  
  .loading-content,
  .empty-content {
    padding: var(--space-4);
  }
  
  .empty-icon {
    font-size: 2.5rem;
    margin-bottom: var(--space-3);
  }
  
  .empty-content h3 {
    font-size: var(--font-size-base);
  }
}

/* Email Send Column Styles */
.col-email-sent {
  width: 140px;
  text-align: center;
}

.email-status {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
  white-space: nowrap;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.email-status.sent {
  background: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.email-status.hidden {
  color: #9ca3af;
  font-style: italic;
  text-transform: none;
}

.email-send-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.email-send-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.email-send-btn:disabled,
.email-send-btn.sending {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.email-send-btn .send-icon {
  font-size: 0.75rem;
}

.email-send-btn .spin-icon {
  animation: spin 1s linear infinite;
}

.status-icon {
  font-size: 0.75rem;
}

/* Responsive adjustments for email column */
@media (max-width: 768px) {
  .col-email-sent {
    width: 100px;
  }

  .email-send-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.625rem;
    gap: 0.25rem;
  }

  .email-status {
    padding: 0.25rem 0.5rem;
    font-size: 0.625rem;
    gap: 0.25rem;
  }
}

@media (max-width: 480px) {
  .col-email-sent {
    display: none;
  }
}