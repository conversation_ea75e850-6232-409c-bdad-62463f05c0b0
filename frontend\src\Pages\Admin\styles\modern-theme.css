/* Modern Admin Theme - Color Variables */
:root {
  /* Primary Colors - Blue Theme for School Medical */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* Blue Colors - Main Theme */
  --blue-50: #eff6ff;
  --blue-100: #dbeafe;
  --blue-200: #bfdbfe;
  --blue-300: #93c5fd;
  --blue-400: #60a5fa;
  --blue-500: #3b82f6;
  --blue-600: #2563eb;
  --blue-700: #1d4ed8;
  --blue-800: #1e40af;
  --blue-900: #1e3a8a;

  /* Purple Colors */
  --purple-50: #faf5ff;
  --purple-100: #f3e8ff;
  --purple-200: #e9d5ff;
  --purple-300: #d8b4fe;
  --purple-400: #c084fc;
  --purple-500: #a855f7;
  --purple-600: #9333ea;
  --purple-700: #7c3aed;
  --purple-800: #6b21a8;
  --purple-900: #581c87;

  /* Gray Colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Slate Colors */
  --slate-50: #f8fafc;
  --slate-100: #f1f5f9;
  --slate-200: #e2e8f0;
  --slate-300: #cbd5e1;
  --slate-400: #94a3b8;
  --slate-500: #64748b;
  --slate-600: #475569;
  --slate-700: #334155;
  --slate-800: #1e293b;
  --slate-900: #0f172a;

  /* Success Colors */
  --success-50: #f0fdf4;
  --success-100: #dcfce7;
  --success-200: #bbf7d0;
  --success-300: #86efac;
  --success-400: #4ade80;
  --success-500: #22c55e;
  --success-600: #16a34a;
  --success-700: #15803d;
  --success-800: #166534;
  --success-900: #14532d;

  /* Warning Colors */
  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-200: #fde68a;
  --warning-300: #fcd34d;
  --warning-400: #fbbf24;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;
  --warning-800: #92400e;
  --warning-900: #78350f;

  /* Error Colors */
  --error-50: #fef2f2;
  --error-100: #fee2e2;
  --error-200: #fecaca;
  --error-300: #fca5a5;
  --error-400: #f87171;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --error-700: #b91c1c;
  --error-800: #991b1b;
  --error-900: #7f1d1d;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* Font Weights */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
}

/* Modern Button Styles */
.admin_ui_btn_modern {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-tight);
  transition: all 0.2s ease;
  border: 1px solid transparent;
  cursor: pointer;
  text-decoration: none;
}

.admin_ui_btn_primary {
  background: var(--blue-600);
  color: white;
  border-color: var(--blue-600);
}

.admin_ui_btn_primary:hover {
  background: var(--blue-700);
  border-color: var(--blue-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.admin_ui_btn_secondary {
  background: var(--gray-100);
  color: var(--gray-700);
  border-color: var(--gray-200);
}

.admin_ui_btn_secondary:hover {
  background: var(--gray-200);
  border-color: var(--gray-300);
}

.admin_ui_btn_success {
  background: var(--success-600);
  color: white;
  border-color: var(--success-600);
}

.admin_ui_btn_success:hover {
  background: var(--success-700);
  border-color: var(--success-700);
}

.admin_ui_btn_warning {
  background: var(--warning-500);
  color: white;
  border-color: var(--warning-500);
}

.admin_ui_btn_warning:hover {
  background: var(--warning-600);
  border-color: var(--warning-600);
}

.admin_ui_btn_error {
  background: var(--error-600);
  color: white;
  border-color: var(--error-600);
}

.admin_ui_btn_error:hover {
  background: var(--error-700);
  border-color: var(--error-700);
}

/* Modern Card Styles */
.admin_ui_card_modern {
  background: white;
  border-radius: var(--radius-xl);
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
}

.admin_ui_card_modern:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.admin_ui_card_header {
  padding: 1.5rem 1.5rem 0 1.5rem;
  border-bottom: 1px solid var(--gray-100);
}

.admin_ui_card_body {
  padding: 1.5rem;
}

.admin_ui_card_footer {
  padding: 0 1.5rem 1.5rem 1.5rem;
  border-top: 1px solid var(--gray-100);
}

/* Modern Input Styles */
.admin_ui_input_modern {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  transition: all 0.2s ease;
  background: white;
}

.admin_ui_input_modern:focus {
  outline: none;
  border-color: var(--indigo-500);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Modern Badge Styles */
.admin_ui_badge_modern {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.admin_ui_badge_primary {
  background: var(--indigo-100);
  color: var(--indigo-800);
}

.admin_ui_badge_success {
  background: var(--success-100);
  color: var(--success-800);
}

.admin_ui_badge_warning {
  background: var(--warning-100);
  color: var(--warning-800);
}

.admin_ui_badge_error {
  background: var(--error-100);
  color: var(--error-800);
}
