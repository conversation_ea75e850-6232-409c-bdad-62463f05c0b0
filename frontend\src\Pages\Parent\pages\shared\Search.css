/**
 * DEPRECATED: Legacy shared search styling
 * This file is kept for backward compatibility with older components
 * New components should use the modern SearchBox component with unique class variants
 * 
 * @deprecated Use SearchBox component with className prop instead
 */

/* 
 * This file now serves as a fallback for any legacy search implementations
 * All new search functionality should use:
 * - health-guide-search for HealthGuide pages
 * - community-search for Community pages  
 * - modern-search-container base class from SearchBox component
 */

/* Legacy fallback - minimal styling to prevent breaks */
.legacy-search-fallback {
  /* Intentionally minimal to encourage migration to SearchBox component */
  position: relative;
  width: 100%;
  margin-bottom: 20px;
}

.legacy-search-fallback input {
  width: 100%;
  padding: 12px 40px 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
}

.legacy-search-fallback button {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: #6b7280;
  border: none;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  cursor: pointer;
}

/* Note: To use modern search styling, replace with:
 * <SearchBox className="health-guide-search" /> or
 * <SearchBox className="community-search" />
 */