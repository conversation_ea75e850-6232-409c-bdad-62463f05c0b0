.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  margin: 20px auto;
  max-width: 1000px;
  padding: 40px;
}

.spinner-container {
  margin-bottom: 20px;
}

.spinner-border {
  display: inline-block;
  width: 60px;
  height: 60px;
  border: 5px solid rgba(52, 152, 219, 0.2);
  border-radius: 50%;
  border-top-color: #3498db;
  animation: spin 1s linear infinite;
  box-sizing: border-box;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-container p {
  font-size: 1.1rem;
  color: #4a5568;
  margin-top: 15px;
  font-weight: 500;
}

/* Responsive styles */
@media (max-width: 768px) {
  .loading-container {
    height: 300px;
    padding: 30px;
  }
  
  .spinner-border {
    width: 50px;
    height: 50px;
  }
}

@media (max-width: 576px) {
  .loading-container {
    height: 250px;
    padding: 25px;
  }
  
  .spinner-border {
    width: 40px;
    height: 40px;
    border-width: 4px;
  }
  
  .loading-container p {
    font-size: 1rem;
  }
}