/* Admin Dashboard Content */
.admin_ui_dash_content {
  padding: 0;
  background: transparent;
  min-height: 100vh;
}

/* Header Section */
.admin_ui_dash_header {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
  color: white !important;
  padding: 32px;
  border-radius: 16px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.15) !important;
  position: relative;
  overflow: hidden;
}

.admin_ui_dash_header-content {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80%;
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  gap: 16px;
}

.admin_ui_dash_header h1 {
  color: white;
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  letter-spacing: -0.025em;
}

.admin_ui_dash_header p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  margin: 0;
  font-weight: 400;
}



/* Error Banner */
.admin_ui_dash_error-banner {
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%) !important;
  border: 1px solid #d1d5db !important;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 24px;
  color: #6b7280 !important;
  font-size: 14px;
}

/* Info Banner */
.admin_ui_dash_info-banner {
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%) !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 24px;
  color: #4b5563 !important;
  font-size: 14px;
}

/* Stats Container */
.admin_ui_dash_stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.admin_ui_dash_stat_card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease;
}

.admin_ui_dash_stat_card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.admin_ui_dash_stat_icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.admin_ui_dash_stat-icon.students {
  background: linear-gradient(135deg, #64748b 0%, #475569 100%) !important;
}

.admin_ui_dash_stat-icon.events {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%) !important;
}

.admin_ui_dash_stat-icon.upcoming {
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%) !important;
}

.admin_ui_dash_stat-icon.reports {
  background: linear-gradient(135deg, #78716c 0%, #57534e 100%) !important;
}

.admin_ui_dash_stat-details h3 {
  color: #475569;
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 4px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin_ui_dash_stat-value {
  color: #1e293b;
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
}

/* Facebook-Style Charts Section */
.admin_ui_dash_charts-section {
  margin-bottom: 32px;
}

.admin_ui_dash_section-header {
  margin-bottom: 24px;
}

.admin_ui_dash_section-header h2 {
  color: #1e293b;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.admin_ui_dash_charts-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.admin_ui_dash_chart-card {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
  min-height: 320px;
}

.admin_ui_dash_chart-card.full-width {
  grid-column: 1 / -1;
}

.admin_ui_dash_chart-header {
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.admin_ui_dash_chart-header h3 {
  color: #374151;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  text-transform: none;
  letter-spacing: 0;
}

.admin_ui_dash_chart-header .admin_ui_dash_chart-icon {
  width: 20px;
  height: 20px;
  color: #6b7280;
  font-size: 20px;
}

.admin_ui_dash_chart-subtitle {
  color: #9ca3af;
  font-size: 14px;
  font-weight: 400;
  margin-top: 6px;
}

.admin_ui_dash_chart-content {
  height: 240px;
  position: relative;
  display: flex;
  flex-direction: column;
}

.admin_ui_dash_chart-content.large {
  height: 320px;
}

/* Metric Display (Large Numbers) */
.admin_ui_dash_chart-metric {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 20px;
}

.admin_ui_dash_chart-metric-value {
  font-size: 64px;
  font-weight: 700;
  color: #111827;
  line-height: 1;
  margin: 0;
}

.admin_ui_dash_chart-metric-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
  margin-top: 8px;
}

/* Mini Chart Area */
.admin_ui_dash_chart-mini {
  height: 80px;
  width: 100%;
  margin-top: auto;
}

/* Donut Chart Styles */
.admin_ui_dash_chart-donut {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 160px;
  position: relative;
  margin-bottom: 20px;
}

.admin_ui_dash_chart-donut-center {
  position: absolute;
  text-align: center;
  z-index: 2;
}

.admin_ui_dash_chart-donut-value {
  font-size: 40px;
  font-weight: 700;
  color: #111827;
  line-height: 1;
}

.admin_ui_dash_chart-donut-label {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
  margin-top: 4px;
}

/* Legend for Donut Charts */
.admin_ui_dash_chart-legend {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 0;
}

.admin_ui_dash_chart-legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
}

.admin_ui_dash_chart-legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  flex-shrink: 0;
}

.admin_ui_dash_chart-legend-label {
  color: #374151;
  font-weight: 500;
  flex: 1;
}

.admin_ui_dash_chart-legend-value {
  color: #6b7280;
  font-weight: 600;
}

/* Country List Styles */
.admin_ui_dash_chart-country-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.admin_ui_dash_chart-country-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 0;
  border-bottom: 1px solid #e5e7eb;
}

.admin_ui_dash_chart-country-item:last-child {
  border-bottom: none;
}

.admin_ui_dash_chart-country-flag {
  width: 24px;
  height: 18px;
  border-radius: 3px;
  object-fit: cover;
  flex-shrink: 0;
}

.admin_ui_dash_chart-country-name {
  flex: 1;
  font-size: 15px;
  color: #374151;
  font-weight: 500;
}

.admin_ui_dash_chart-country-value {
  font-size: 15px;
  color: #6b7280;
  font-weight: 600;
}

/* Bar Chart Styles */
.admin_ui_dash_chart-bars {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  height: 140px;
  margin-top: 20px;
}

.admin_ui_dash_chart-bar {
  flex: 1;
  background: #64748b !important;
  border-radius: 6px 6px 0 0;
  position: relative;
  min-height: 24px;
}

.admin_ui_dash_chart-bar.secondary {
  background: #9ca3af !important;
}

.admin_ui_dash_chart-bar-label {
  position: absolute;
  bottom: -24px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  white-space: nowrap;
}

/* Large Center Number Style */
.admin_ui_dash_chart-large-number {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  flex-direction: column;
}

.admin_ui_dash_chart-large-number-value {
  font-size: 96px;
  font-weight: 700;
  color: #111827;
  line-height: 1;
}

.admin_ui_dash_chart-large-number-label {
  font-size: 16px;
  color: #6b7280;
  font-weight: 500;
  margin-top: 12px;
}

.admin_ui_dash_chart-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #9ca3af;
  font-size: 16px;
  animation: admin_ui_dash_pulse 2s infinite;
}

.admin_ui_dash_chart-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #ef4444;
  font-size: 16px;
}

@keyframes admin_ui_dash_pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Dashboard Content Grid (Events & Alerts) */
.admin_ui_dash_content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.admin_ui_dash_card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.admin_ui_dash_card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0 !important;
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%) !important;
}

.admin_ui_dash_card-header h2 {
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.admin_ui_dash_view-all-btn {
  background: linear-gradient(135deg, #64748b 0%, #475569 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
}

.admin_ui_dash_card-content {
  padding: 0;
}

/* Events Table */
.admin_ui_dash_events-table {
  width: 100%;
  border-collapse: collapse;
}

.admin_ui_dash_events-table th {
  background: #f9fafb !important;
  color: #4b5563 !important;
  font-weight: 600;
  font-size: 0.875rem;
  text-align: left;
  padding: 16px 24px;
  border-bottom: 1px solid #e2e8f0 !important;
}

.admin_ui_dash_events-table td {
  padding: 16px 24px;
  border-bottom: 1px solid #f1f5f9;
  color: #1e293b;
  font-size: 0.875rem;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 200px;
}

.admin_ui_dash_events-table tr:hover {
  background: #f9fafb !important;
}

.admin_ui_dash_event-status-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.65rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.admin_ui_dash_event-status-tag.completed {
  background: #d1fae5 !important;
  color: #059669 !important;
}

.admin_ui_dash_event-status-tag.upcoming {
  background: #fef3c7 !important;
  color: #d97706 !important;
}

.admin_ui_dash_event-status-tag.in-progress {
  background: #f9fafb !important;
  color: #6b7280 !important;
}

.admin_ui_dash_event-status-tag.cancelled {
  background: #f3f4f6 !important;
  color: #9ca3af !important;
}

/* Health Alerts */
.admin_ui_dash_alerts {
  padding: 8px;
}

.admin_ui_dash_alert-item {
  padding: 16px;
  margin-bottom: 12px;
  border-radius: 8px;
  border-left: 4px solid;
}

.admin_ui_dash_alert-item:last-child {
  margin-bottom: 0;
}

.admin_ui_dash_alert-item.high {
  background: #f9fafb !important;
  border-color: #6b7280 !important;
}

.admin_ui_dash_alert-item.medium {
  background: #f3f4f6 !important;
  border-color: #9ca3af !important;
}

.admin_ui_dash_alert-item.low {
  background: #f9fafb !important;
  border-color: #d1d5db !important;
}

.admin_ui_dash_alert-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.admin_ui_dash_alert-header h3 {
  color: #1e293b;
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0;
  flex: 1;
}

.admin_ui_dash_alert-date {
  color: #64748b;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
  margin-left: 12px;
}

.admin_ui_dash_alert-description {
  color: #475569;
  font-size: 0.8rem;
  line-height: 1.5;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .admin_ui_dash_charts-row {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
  }
  
  .admin_ui_dash_chart-card.full-width {
    grid-column: 1;
  }
}

@media (max-width: 768px) {
  .admin_ui_dash_content {
    padding: 16px;
  }
  
  .admin_ui_dash_header {
    background: linear-gradient(135deg, #374151 0%, #4b5563 100%) !important;
    padding: 32px 20px;
    border-radius: 16px;
  }
  
  .admin_ui_dash_header-content {
    width: 90%;
  }
  
  .admin_ui_dash_header h1 {
    font-size: 1.75rem;
  }
  
  .admin_ui_dash_header p {
    font-size: 1rem;
  }
  
  .admin_ui_dash_stats-container {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .admin_ui_dash_content-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .admin_ui_dash_charts-row {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .admin_ui_dash_chart-content {
    height: 200px;
  }
  
  .admin_ui_dash_chart-content.large {
    height: 260px;
  }
  
  .admin_ui_dash_chart-metric-value {
    font-size: 48px;
  }
  
  .admin_ui_dash_chart-large-number-value {
    font-size: 72px;
  }
  
  .admin_ui_dash_chart-donut-value {
    font-size: 32px;
  }
  
  .admin_ui_dash_card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .admin_ui_dash_view-all-btn {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .admin_ui_dash_content {
    padding: 12px;
  }
  
  .admin_ui_dash_header {
    padding: 24px 16px;
    border-radius: 12px;
  }
  
  .admin_ui_dash_header-content {
    width: 95%;
  }
  
  .admin_ui_dash_header h1 {
    font-size: 1.5rem;
  }
  
  .admin_ui_dash_header p {
    font-size: 0.9rem;
  }
  
  .admin_ui_dash_stat-card {
    padding: 16px;
  }
  
  .admin_ui_dash_stat-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }
  
  .admin_ui_dash_stat-value {
    font-size: 1.5rem;
  }
  
  .admin_ui_dash_chart-card {
    padding: 24px;
    min-height: 280px;
  }
  
  .admin_ui_dash_chart-content {
    height: 180px;
  }
  
  .admin_ui_dash_chart-content.large {
    height: 220px;
  }
  
  .admin_ui_dash_chart-metric-value {
    font-size: 40px;
  }
  
  .admin_ui_dash_chart-large-number-value {
    font-size: 60px;
  }
  
  .admin_ui_dash_chart-donut-value {
    font-size: 28px;
  }
  
  .admin_ui_dash_chart-bars {
    height: 100px;
  }
  
  .admin_ui_dash_chart-country-item {
    padding: 10px 0;
  }
  
  .admin_ui_dash_chart-country-flag {
    width: 20px;
    height: 15px;
  }
  
  .admin_ui_dash_chart-country-name,
  .admin_ui_dash_chart-country-value {
    font-size: 14px;
  }
  
  .admin_ui_dash_events-table th,
  .admin_ui_dash_events-table td {
    padding: 12px 16px;
    font-size: 0.8rem;
  }
  
  .admin_ui_dash_alert-item {
    padding: 12px;
  }
}

/* High Specificity Rules to Override Global Styles AND AdminLayout */
.admin-content .admin_ui_dash_content {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
  padding: 24px !important;
  min-height: 100vh !important;
  margin: -30px !important;
  border-radius: 0 !important;
  box-shadow: none !important;
}

.admin-content .admin_ui_dash_content .admin_ui_dash_chart-card,
.admin-content .admin_ui_dash_content .admin_ui_dash_card {
  background: white !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05) !important;
}

.admin-content .admin_ui_dash_content .admin_ui_dash_chart-card:hover,
.admin-content .admin_ui_dash_content .admin_ui_dash_card:hover {
  transform: none !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
}

/* Ensure buttons don't get global styling */
.admin-content .admin_ui_dash_content .admin_ui_dash_view-all-btn,
.admin-content .admin_ui_dash_content button {
  background: linear-gradient(135deg, #64748b 0%, #475569 100%) !important;
  color: white !important;
  border: none !important;
}

/* Ensure all text colors are neutral */
.admin-content .admin_ui_dash_content h1,
.admin-content .admin_ui_dash_content h2,
.admin-content .admin_ui_dash_content h3,
.admin-content .admin_ui_dash_content p {
  color: inherit !important;
}

/* Override any global color utilities */
.admin-content .admin_ui_dash_content .text-primary {
  color: #4b5563 !important;
}

.admin-content .admin_ui_dash_content .bg-primary {
  background-color: #64748b !important;
}

/* Override AdminLayout header styles */
.admin-content .admin_ui_dash_content .admin_ui_dash_header {
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%) !important;
  color: white !important;
  box-shadow: 0 10px 25px rgba(55, 65, 81, 0.15) !important;
}

/* Override AdminLayout stat icon styles */
.admin-content .admin_ui_dash_content .admin_ui_dash_stat-icon.students {
  background: linear-gradient(135deg, #64748b 0%, #475569 100%) !important;
}

.admin-content .admin_ui_dash_content .admin_ui_dash_stat-icon.events {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%) !important;
}

.admin-content .admin_ui_dash_content .admin_ui_dash_stat-icon.upcoming {
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%) !important;
}

.admin-content .admin_ui_dash_content .admin_ui_dash_stat-icon.reports {
  background: linear-gradient(135deg, #78716c 0%, #57534e 100%) !important;
}

/* Override AdminLayout chart bar styles */
.admin-content .admin_ui_dash_content .admin_ui_dash_chart-bar {
  background: #64748b !important;
}

.admin-content .admin_ui_dash_content .admin_ui_dash_chart-bar.secondary {
  background: #9ca3af !important;
}

/* Override AdminLayout status tag styles */
.admin-content .admin_ui_dash_content .admin_ui_dash_event-status-tag.completed {
  background: #d1fae5 !important;
  color: #059669 !important;
}

.admin-content .admin_ui_dash_content .admin_ui_dash_event-status-tag.upcoming {
  background: #fef3c7 !important;
  color: #d97706 !important;
}

.admin-content .admin_ui_dash_content .admin_ui_dash_event-status-tag.in-progress {
  background: #f9fafb !important;
  color: #6b7280 !important;
}

.admin-content .admin_ui_dash_content .admin_ui_dash_event-status-tag.cancelled {
  background: #f3f4f6 !important;
  color: #9ca3af !important;
}

/* Override AdminLayout table styles */
.admin-content .admin_ui_dash_content .admin_ui_dash_events-table th {
  background: #f9fafb !important;
  color: #4b5563 !important;
  border-bottom: 1px solid #e2e8f0 !important;
}

.admin-content .admin_ui_dash_content .admin_ui_dash_events-table tr:hover {
  background: #f9fafb !important;
}

/* Override AdminLayout card header styles */
.admin-content .admin_ui_dash_content .admin_ui_dash_card-header {
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%) !important;
  border-bottom: 1px solid #e2e8f0 !important;
}

/* Override AdminLayout banner styles */
.admin-content .admin_ui_dash_content .admin_ui_dash_error-banner {
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%) !important;
  border: 1px solid #d1d5db !important;
  color: #6b7280 !important;
}

.admin-content .admin_ui_dash_content .admin_ui_dash_info-banner {
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%) !important;
  border: 1px solid #e5e7eb !important;
  color: #4b5563 !important;
}

/* Override AdminLayout alert styles */
.admin-content .admin_ui_dash_content .admin_ui_dash_alert-item.high {
  background: #f9fafb !important;
  border-color: #6b7280 !important;
}

.admin-content .admin_ui_dash_content .admin_ui_dash_alert-item.medium {
  background: #f3f4f6 !important;
  border-color: #9ca3af !important;
}

.admin-content .admin_ui_dash_content .admin_ui_dash_alert-item.low {
  background: #f9fafb !important;
  border-color: #d1d5db !important;
}

