.introduction-page {
  margin-top: 0;
  background-color: #ffffff;
  min-height: 100vh;
}

.page-banner {
  background: linear-gradient(135deg, #015C92 0%, #2D82B5 30%, #428CD4 60%, #88CDF6 100%);
  color: #ffffff;
  padding: 60px 0;
  text-align: center;
  margin-bottom: 50px;
  box-shadow: 0 4px 15px -3px rgba(1, 92, 146, 0.3);
  border: none;
  border-radius: 0 0 30px 30px;
}

.page-title {
  color: #fff;
  font-size: 2.8rem;
  font-weight: 700;
  margin: 0;
}

.intro-banner-container,
.intro-content-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px 15px;
}

.intro-content-container {
  padding: 20px 15px 80px;
}

/* Override handled by global.css */

.intro-section {
  margin-bottom: 50px;
}

.intro-section h2 {
  color: #2c3e50;
  font-size: 1.8rem;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 10px;
}

.intro-section h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(to right, #3498db, #9b59b6);
}

.intro-section p {
  color: #555;
  line-height: 1.8;
  margin-bottom: 15px;
  font-size: 1.05rem;
}

.intro-section strong {
  color: #2c3e50;
}