/* Merged styles from index.css */
.health-checkups-page {
  width: 100%;
}

.health-checkups-container {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Styles for the header and title */
.health-checkups-header {
  margin-bottom: 15px;
}

.health-checkups-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #3c4b64;
  margin: 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #3498db;
  display: inline-block;
}

.health-checkups-tabs {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;
}

.tab-button {
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 10px 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #495057;
  outline: none;
}

.tab-button i {
  font-size: 1rem;
}

.tab-button:hover {
  background-color: #e9ecef;
}

.tab-button.active {
  background-color: #3498db;
  color: white;
  border-color: #3498db;
}

.tab-button.back-button {
  margin-left: auto;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
}

.health-checkups-content {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  width: 100%;
  overflow: hidden;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #3498db;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .health-checkups-tabs {
    flex-direction: column;
    align-items: stretch;
    gap: 5px;
  }
  
  .tab-button.back-button {
    margin-left: 0;
    margin-top: 10px;
  }
}
