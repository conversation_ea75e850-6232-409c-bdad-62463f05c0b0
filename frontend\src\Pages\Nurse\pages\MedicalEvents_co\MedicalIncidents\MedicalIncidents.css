/* MedicalIncidents.css - Enhanced styles for Medical Incidents management */

/* CSS cho container chứa bộ lọc */
.filter-section {
  padding: 15px;
  margin-bottom: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

/* Style cho medical-events-header - container chứa search và button trên cùng một hàng */
.medical-events-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
  width: 100%;
  flex-wrap: nowrap; /* <PERSON><PERSON><PERSON> cá<PERSON> phần tử bị xuống dòng */
}

/* CSS cho thanh tìm kiếm mới - dựa theo mẫu */
.search-container {
  display: flex;
  justify-content: flex-start;
  width: 100%;
  max-width: 600px;
}

.search-box {
  display: flex;
  align-items: stretch;
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  background-color: #fff;
  height: 38px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.search-type-dropdown {
  width: 160px;
  padding: 0 10px;
  border: none;
  border-right: 1px solid #e0e0e0;
  background-color: #f5f5f5;
  outline: none;
  font-size: 14px;
  color: #555;
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 12px;
  padding-right: 30px;
  height: 100%;
}

.search-input {
  flex-grow: 1;
  padding: 0 15px;
  border: none;
  outline: none;
  font-size: 14px;
  height: 100%;
}

.search-btn {
  background-color: #5a8dee;
  color: white;
  border: none;
  padding: 0 16px;
  cursor: pointer;
  transition: background-color 0.3s;
  font-size: 14px;
  font-weight: 500;
  min-width: 100px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-btn:hover {
  background-color: #4a7fd6;
}

.search-btn i {
  margin-right: 5px;
}

/* Bảng nhỏ gọn hơn */
.table-container {
  width: 100%;
  overflow-x: auto;
}

/* CSS cụ thể cho bảng events */
.events-table-container {
  width: 100%;
  overflow-x: auto;
  margin-top: 20px;
}

.events-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 950px; /* Đảm bảo bảng đủ rộng */
}

table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem; /* Font size nhỏ hơn */
}

table th, 
table td {
  padding: 10px 12px; /* Tăng padding đủ để thấy nội dung */
  border: 1px solid #e0e0e0;
  text-align: left;
}

table th {
  background-color: #f5f5f5;
  font-weight: 600;
  color: #333;
  position: sticky;
  top: 0;
  z-index: 10;
}

table tr:hover {
  background-color: #f9f9f9;
}

/* Định nghĩa độ rộng cụ thể cho các cột trong events-table */
.events-table th:nth-child(1) { width: 5%; } /* ID */
.events-table th:nth-child(2) { width: 10%; } /* Mã học sinh */
.events-table th:nth-child(3) { width: 15%; } /* Tên học sinh */
.events-table th:nth-child(4) { width: 15%; } /* Ngày giờ */
.events-table th:nth-child(5) { width: 10%; } /* Mức độ */
.events-table th:nth-child(6) { width: 10%; } /* Thông báo PH */
.events-table th:nth-child(7) { width: 10%; } /* Theo dõi */
.events-table th:nth-child(8) { width: 15%; } /* Hành động */

.events-table td {
  word-break: break-word;
}

/* Cột action nhỏ gọn hơn */
.action-cell {
  width: 100px; /* Thu hẹp độ rộng */
  text-align: center;
  white-space: nowrap;
}

.action-cell button {
  padding: 5px 6px; /* Thu nhỏ padding */
  margin: 0 2px; /* Giảm margin */
  background: none;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.9em;
  color: #555;
  transition: all 0.2s;
}

.action-cell .btn-view {
  color: #2196f3;
}

.action-cell .btn-edit {
  color: #4caf50;
}

.action-cell .btn-delete {
  color: #f44336;
}

.action-cell button:hover {
  background-color: rgba(0,0,0,0.05);
  transform: scale(1.1);
}

/* Làm mô tả ngắn gọn với tooltip */
td {
  max-width: 200px; /* Tăng kích thước tối đa cho cell */
  min-width: 80px; /* Đảm bảo có min-width để không bị thu nhỏ quá */
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

/* Thêm tooltip khi hover để xem đầy đủ thông tin */
td:hover {
  overflow: visible;
  white-space: normal;
  position: relative;
}

td:hover::after {
  content: attr(title);
  position: absolute;
  left: 0;
  top: 100%;
  background: white;
  padding: 5px;
  border: 1px solid #ddd;
  border-radius: 3px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  z-index: 100;
  max-width: 200px;
  white-space: normal;
}

/* Filter section đã được định nghĩa ở trên */

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 15px;
}

.filter-item {
  flex: 1;
  min-width: 200px;
}

.filter-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.filter-item input,
.filter-item select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.95rem;
  background-color: #fff;
}

.filter-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.filter-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  color: #fff;
}

.filter-btn.apply {
  background-color: #2196f3;
}

.filter-btn.apply:hover {
  background-color: #0d8aee;
}

.filter-btn.reset {
  background-color: #9e9e9e;
}

.filter-btn.reset:hover {
  background-color: #757575;
}

.add-event-btn {
  padding: 10px 20px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
}

.add-event-btn:hover {
  background-color: #388e3c;
}

.status-badge {
  display: inline-block;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  min-width: 80px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.btn-primary {
  background-color: #2196f3;
  color: white;
}

.btn-primary:hover {
  background-color: #1976d2;
}

.btn-success {
  background-color: #4caf50;
  color: white;
}

.btn-success:hover {
  background-color: #388e3c;
}

.btn-danger {
  background-color: #f44336;
  color: white;
}

.btn-danger:hover {
  background-color: #d32f2f;
}

.btn-secondary {
  background-color: #9e9e9e;
  color: white;
}

.btn-secondary:hover {
  background-color: #757575;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Status icons */
.status-icon {
  font-size: 1.2rem;
}

.status-icon.notified {
  color: #4caf50;
}

.status-icon.not-notified {
  color: #ff9800;
}

.status-icon.follow-up {
  color: #2196f3;
}

.status-icon.no-follow-up {
  color: #4caf50;
}

/* Severity colors - Enhanced for better visibility */
td .severity-high, 
td .severity-critical,
.status-badge.severity-high, 
.status-badge.severity-critical {
  background-color: #ffebee !important;
  color: #d32f2f !important;
  border: 1px solid #ef9a9a !important;
  font-weight: 600 !important;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
}

td .severity-medium, 
td .severity-moderate,
.status-badge.severity-medium, 
.status-badge.severity-moderate {
  background-color: #fff8e1 !important;
  color: #ff8f00 !important;
  border: 1px solid #ffe082 !important;
  font-weight: 600 !important;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
}

td .severity-low, 
td .severity-mild,
.status-badge.severity-low, 
.status-badge.severity-mild {
  background-color: #e8f5e9 !important;
  color: #388e3c !important;
  border: 1px solid #a5d6a7 !important;
  font-weight: 600 !important;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
}

td .severity-unknown,
.status-badge.severity-unknown {
  background-color: #f5f5f5 !important;
  color: #757575 !important;
  border: 1px solid #e0e0e0 !important;
  font-weight: 600 !important;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
}

/* Styles for view details modal */
.view-details-modal {
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.view-details-modal .modal-body {
  padding: 15px 20px;
}

/* Detail sections */
.detail-sections {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-section {
  background-color: #f9f9f9;
  border-radius: 6px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.detail-section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 15px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e0e0e0;
}

/* Grid and row layouts for details */
.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

.detail-row {
  display: flex;
  flex-direction: column;
  margin-bottom: 12px;
}

.detail-item {
  margin-bottom: 10px;
}

.detail-label {
  font-weight: 600;
  color: #555;
  font-size: 0.9rem;
  margin-right: 8px;
  display: block;
  margin-bottom: 3px;
}

.detail-value {
  color: #333;
  font-size: 0.95rem;
}

/* Status indicators in detail view */
.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* Image in detail view */
.image-container {
  margin-top: 10px;
  text-align: center;
}

.incident-image {
  max-width: 100%;
  max-height: 300px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

/* Medications list */
.medications-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.medication-item {
  background-color: white;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

/* Modal footer */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 15px 20px;
  border-top: 1px solid #e0e0e0;
}

/* Styles for medication search */
.search-container {
  position: relative;
  width: 100%;
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  z-index: 1000;
}

.search-result-item {
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover {
  background-color: #f5f5f5;
}

.search-result-item .item-name {
  font-weight: 500;
  margin-bottom: 3px;
}

.search-result-item .item-details {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: #666;
}

.search-loading {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
}

.selected-item-info {
  margin-top: 5px;
  font-size: 0.85rem;
  color: #555;
}

.id-badge {
  display: inline-block;
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 2px 8px;
  border-radius: 10px;
  font-weight: 500;
}

.medication-search-field {
  flex: 2; /* Tăng kích thước cho trường tìm kiếm thuốc */
}

/* Enhanced medication display */
.medication-detail-item {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 10px;
  border-left: 4px solid #4caf50;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.medication-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
  flex-wrap: wrap;
}

.medication-name {
  font-weight: 600;
  color: #333;
  font-size: 0.95rem;
}

.medication-quantity {
  background-color: #e8f5e9;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.85rem;
  color: #2e7d32;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.medication-note {
  font-size: 0.85rem;
  color: #666;
  margin-top: 5px;
  font-style: italic;
}

.no-medications {
  color: #757575;
  font-style: italic;
  text-align: center;
  padding: 10px;
}

.image-caption {
  font-size: 0.85rem;
  color: #666;
  text-align: center;
  margin-top: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
}

/* Enhanced Filter Styles */
.filter-container {
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  padding: 16px;
  margin-bottom: 20px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 12px;
}

.filter-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #2196f3;
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-body {
  padding-bottom: 10px;
}

.filter-input,
.filter-select {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 12px;
  width: 100%;
  transition: border-color 0.2s;
}

.filter-input:focus,
.filter-select:focus {
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
  outline: none;
}

.filter-item label {
  display: flex;
  align-items: center;
  gap: 5px;
  font-weight: 500;
  margin-bottom: 5px;
  color: #555;
}

.filter-item label i {
  color: #2196f3;
}

.filter-actions {
  display: flex;
  justify-content: flex-start;
  gap: 10px;
  padding-top: 12px;
  border-top: 1px solid #e0e0e0;
}

.filter-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.filter-btn.apply {
  background-color: #2196f3;
  color: white;
}

.filter-btn.apply:hover {
  background-color: #1976d2;
}

.filter-btn.reset {
  background-color: #f5f5f5;
  color: #555;
}

.filter-btn.reset:hover {
  background-color: #e0e0e0;
}

.add-event-btn {
  background-color: #4caf50;
  color: white;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.add-event-btn:hover {
  background-color: #388e3c;
}

.search-result-message {
  margin: 15px 0;
  padding: 10px;
  border-radius: 4px;
  font-weight: 500;
  text-align: center;
}

.search-result-message.success {
  background-color: #e8f5e9;
  color: #2e7d32;
  border: 1px solid #c8e6c9;
}

.search-result-message.no-result {
  background-color: #fff8e1;
  color: #ff8f00;
  border: 1px solid #ffecb3;
}

/* Form validation styles */
.form-error {
  color: #d32f2f;
  font-size: 0.8rem;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.form-error i {
  font-size: 0.9rem;
}

.input-error {
  border-color: #d32f2f !important;
  background-color: #ffebee !important;
}

.form-group label .required {
  color: #d32f2f;
  margin-left: 3px;
}

/* CSS cho phần nội dung chính */
.medical-events-content {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.08);
  padding: 20px;
  margin: 20px auto;
  max-width: 1600px; /* Tăng kích thước tối đa */
  width: 98%; /* Tăng chiều rộng */
  overflow-x: auto; /* Đảm bảo nếu nội dung quá rộng có thể scroll */
}

/* Responsive styles */
@media screen and (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    gap: 10px;
  }
  
  .filter-item {
    width: 100%;
  }
  
  .filter-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-btn, .add-event-btn {
    width: 100%;
    margin: 5px 0;
  }
  
  .view-details-modal {
    width: 95%;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
}

/* Modal overlay and container */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  width: 80%;
  max-width: 700px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #555;
  transition: color 0.3s;
}

.close-btn:hover {
  color: #f44336;
}

.modal-body {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

/* Styles for medications section */
.medications-container {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 15px;
  margin: 10px 0;
  background-color: #f9f9f9;
}

.medication-item {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px dashed #ddd;
}

.medication-item:last-child {
  border-bottom: none;
  margin-bottom: 5px;
}

.medication-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.medication-field {
  flex: 1;
  min-width: 150px;
}

.medication-field label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 0.9rem;
  color: #555;
}

.medication-field input {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 0.9rem;
}

.add-medication-btn {
  background-color: #27ae60;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 15px;
  margin-top: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  transition: all 0.2s;
}

.add-medication-btn:hover {
  background-color: #219653;
  transform: translateY(-2px);
}

.remove-medication-btn {
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.remove-medication-btn:hover {
  background-color: #c0392b;
}

/* Image Modal Styles */
.image-modal-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  display: flex;
  flex-direction: column;
}

.image-modal-body {
  flex: 1;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  overflow: auto;
  max-height: 70vh;
}

.full-size-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.full-size-image:hover {
  transform: scale(1.02);
}

.image-container {
  margin: 10px 0;
  text-align: center;
}

.incident-image {
  max-width: 100%;
  max-height: 200px;
  object-fit: contain;
  border-radius: 4px;
  border: 1px solid #ddd;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.incident-image:hover {
  transform: scale(1.05);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.clickable {
  cursor: pointer;
}

/* Add this to your MedicalIncidents.css file */
.medication-search-container {
  position: relative;
  width: 100%;
}

.medication-search-results {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  z-index: 1000;
}

.medication-search-item {
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
}

.medication-search-item:hover {
  background-color: #f5f5f5;
}

.medication-search-item:last-child {
  border-bottom: none;
}

.medication-search-item .item-name {
  font-weight: bold;
}

.medication-search-item .item-details {
  font-size: 0.85em;
  color: #666;
}

.medication-search-loading {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  padding: 10px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  text-align: center;
  color: #666;
  z-index: 1000;
}

/* ======= CSS FROM DeleteConfirmModal.jsx (styled-jsx) ======= */
.delete-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;
}

.delete-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 450px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideIn 0.3s ease-out;
  overflow: hidden;
}

.delete-modal-header {
  background: #dc3545;
  color: white;
  padding: 20px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: none;
}

.delete-modal-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.delete-modal-close {
  background: transparent;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.delete-modal-close:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
}

.delete-modal-close:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.delete-modal-body {
  padding: 40px 24px;
  text-align: center;
}

.delete-icon-container {
  width: 80px;
  height: 80px;
  background: #fff2f2;
  border: 3px solid #dc3545;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
}

.delete-icon {
  font-size: 32px;
  color: #dc3545;
}

.delete-question {
  color: #333;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 16px;
  line-height: 1.4;
}

.delete-warning {
  color: #666;
  font-size: 16px;
  margin: 0;
  line-height: 1.5;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.warning-icon {
  color: #ffc107;
}

.delete-modal-footer {
  background: #f8f9fa;
  padding: 20px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid #dee2e6;
}

.delete-btn-cancel,
.delete-btn-confirm {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  justify-content: center;
}

.delete-btn-cancel {
  background: #6c757d;
  color: white;
}

.delete-btn-cancel:hover:not(:disabled) {
  background: #5a6268;
  transform: translateY(-1px);
}

.delete-btn-confirm {
  background: #dc3545;
  color: white;
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

.delete-btn-confirm:hover:not(:disabled) {
  background: #c82333;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.4);
}

.delete-btn-cancel:disabled,
.delete-btn-confirm:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.delete-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@media (max-width: 480px) {
  .delete-modal {
    margin: 20px;
    width: calc(100% - 40px);
  }

  .delete-modal-body {
    padding: 30px 20px;
  }

  .delete-modal-footer {
    flex-direction: column;
    gap: 8px;
  }

  .delete-btn-cancel,
  .delete-btn-confirm {
    width: 100%;
  }
}

/* ======= CSS FROM DeleteConfirmModal.css ======= */
/* Enhanced version with !important for better specificity */
.delete-modal-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background-color: rgba(0, 0, 0, 0.6) !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
  z-index: 99999 !important;
  animation: fadeIn 0.3s ease-out;
  box-sizing: border-box !important;
}

.delete-modal {
  position: relative !important;
  background: white !important;
  border-radius: 12px !important;
  width: 90% !important;
  max-width: 450px !important;
  min-width: 300px !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
  animation: slideIn 0.3s ease-out;
  overflow: hidden !important;
  margin: 0 auto !important;
  transform: translateX(0) translateY(0) !important;
}

/* ======= CSS FROM MedicalIncidentAddModal.jsx ======= */
.lukhang-medical-incident-modal-wrapper {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-height: 100vh !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  z-index: 1055 !important;
}

/* Fix dropdown arrow display issues */
.lukhang-medical-incident-modal-wrapper .form-select,
.lukhang-medical-incident-modal-wrapper select.form-control,
.lukhang-medical-incident-modal-wrapper .medical-severity-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right 0.75rem center !important;
  background-size: 16px 12px !important;
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
}

/* Remove multiple arrows from dropdown */
.lukhang-medical-incident-modal-wrapper select::-ms-expand {
  display: none !important;
}

.lukhang-medical-incident-modal-wrapper .form-select::after,
.lukhang-medical-incident-modal-wrapper .medical-severity-select::after {
  display: none !important;
}

/* Ensure only one arrow per dropdown */
.lukhang-medical-incident-modal-wrapper .dropdown-toggle::after {
  display: none !important;
}

.lukhang-medical-incident-modal-wrapper .modal-dialog {
  margin: 2rem auto !important;
  width: 90vw !important;
  max-width: 1200px !important;
  display: flex !important;
  align-items: center !important;
  min-height: auto !important;
  position: relative !important;
}

.lukhang-medical-modal-content-custom {
  border-radius: 1rem !important;
  overflow: hidden !important;
  box-shadow: 0 20px 60px rgba(0, 123, 255, 0.2) !important;
  border: none !important;
  width: 100% !important;
  max-height: 90vh !important;
  display: flex !important;
  flex-direction: column !important;
  background: white !important;
  position: relative !important;
}

.lukhang-medical-header-custom {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 1rem 1rem 0 0 !important;
  padding: 1.5rem 2rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  flex-shrink: 0 !important;
  min-height: 80px !important;
}

.lukhang-medical-title-custom {
  color: white !important;
  font-weight: 600 !important;
  font-size: 1.4rem !important;
  margin: 0 !important;
  flex: 1 !important;
  display: flex !important;
  align-items: center !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.lukhang-medical-title-custom i {
  color: white !important;
  margin-right: 0.75rem !important;
  font-size: 1.2rem !important;
}

.lukhang-medical-close-button-custom {
  background: rgba(255,255,255,0.15) !important;
  border: 2px solid rgba(255,255,255,0.4) !important;
  color: white !important;
  border-radius: 50% !important;
  width: 48px !important;
  height: 48px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.3s ease !important;
  flex-shrink: 0 !important;
  margin-left: 1.5rem !important;
  font-size: 1.1rem !important;
  text-decoration: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.lukhang-medical-close-button-custom:hover {
  background: rgba(255,255,255,0.3) !important;
  border-color: rgba(255,255,255,0.6) !important;
  color: white !important;
  transform: rotate(90deg) scale(1.15) !important;
  text-decoration: none !important;
}

.lukhang-medical-close-button-custom:focus {
  box-shadow: 0 0 0 4px rgba(255,255,255,0.3) !important;
  color: white !important;
  outline: none !important;
  text-decoration: none !important;
}

.lukhang-medical-close-button-custom:active {
  color: white !important;
  text-decoration: none !important;
}

.lukhang-medical-body-custom {
  flex: 1 !important;
  overflow-y: auto !important;
  max-height: calc(90vh - 240px) !important;
  padding: 2rem !important;
  min-height: 300px !important;
}

.lukhang-medical-footer-custom {
  flex-shrink: 0 !important;
  padding: 2.5rem 2rem !important;
  background: #f8f9fa !important;
  border-top: 1px solid #e9ecef !important;
  min-height: 120px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 1.5rem !important;
  position: relative !important;
  z-index: 10 !important;
  margin-top: auto !important;
}

@media (max-width: 992px) {
  .lukhang-medical-incident-modal-wrapper .modal-dialog {
    width: 95vw !important;
    margin: 1rem auto !important;
  }

  .lukhang-medical-header-custom {
    padding: 1.25rem 1.5rem !important;
    min-height: 70px !important;
  }

  .lukhang-medical-title-custom {
    font-size: 1.2rem !important;
  }

  .lukhang-medical-close-button-custom {
    width: 42px !important;
    height: 42px !important;
    margin-left: 1rem !important;
  }

  .lukhang-medical-body-custom {
    padding: 1.5rem !important;
    max-height: calc(90vh - 220px) !important;
  }

  .lukhang-medical-footer-custom {
    padding: 2rem 1.5rem !important;
    min-height: 110px !important;
  }
}

@media (max-width: 768px) {
  .lukhang-medical-incident-modal-wrapper .modal-dialog {
    width: 98vw !important;
    margin: 0.5rem auto !important;
  }

  .lukhang-medical-header-custom {
    padding: 1rem 1.25rem !important;
    min-height: 65px !important;
  }

  .lukhang-medical-title-custom {
    font-size: 1.1rem !important;
  }

  .lukhang-medical-close-button-custom {
    width: 38px !important;
    height: 38px !important;
    margin-left: 0.75rem !important;
    font-size: 1rem !important;
  }

  .lukhang-medical-body-custom {
    padding: 1.25rem !important;
    max-height: calc(90vh - 200px) !important;
  }

  .lukhang-medical-footer-custom {
    padding: 1.75rem 1.25rem !important;
    min-height: 100px !important;
  }
}

/* Additional styling for dropdown elements */
.lukhang-medical-incident-modal-wrapper .medical-severity-select:focus {
  border-color: #0d6efd !important;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25) !important;
}

.lukhang-medical-incident-modal-wrapper .medical-student-input:focus,
.lukhang-medical-incident-modal-wrapper .medical-medication-search:focus {
  border-color: #0d6efd !important;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25) !important;
}

/* Dropdown menu styling */
.lukhang-medical-incident-modal-wrapper .dropdown-menu {
  border: 1px solid #0d6efd !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.lukhang-medical-incident-modal-wrapper .dropdown-item:hover {
  background-color: #f8f9fa !important;
  color: #0d6efd !important;
}

/* ======= CSS FROM MedicalIncidentUpdateModal.jsx ======= */
.lukhang-medical-update-modal-wrapper {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-height: 100vh !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  z-index: 1055 !important;
}

/* Fix dropdown arrow display issues */
.lukhang-medical-update-modal-wrapper .form-select,
.lukhang-medical-update-modal-wrapper select.form-control,
.lukhang-medical-update-modal-wrapper select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right 0.75rem center !important;
  background-size: 16px 12px !important;
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
}

/* Remove multiple arrows from dropdown */
.lukhang-medical-update-modal-wrapper select::-ms-expand {
  display: none !important;
}

.lukhang-medical-update-modal-wrapper .form-select::after,
.lukhang-medical-update-modal-wrapper select::after {
  display: none !important;
}

/* Ensure only one arrow per dropdown */
.lukhang-medical-update-modal-wrapper .dropdown-toggle::after {
  display: none !important;
}

.lukhang-medical-update-modal-wrapper .modal-dialog {
  margin: 2rem auto !important;
  width: 90vw !important;
  max-width: 1200px !important;
  display: flex !important;
  align-items: center !important;
  min-height: auto !important;
  position: relative !important;
}

.lukhang-medical-update-modal-content-custom {
  border-radius: 1rem !important;
  overflow: hidden !important;
  box-shadow: 0 20px 60px rgba(255, 193, 7, 0.2) !important;
  border: none !important;
  width: 100% !important;
  max-height: 90vh !important;
  display: flex !important;
  flex-direction: column !important;
  background: white !important;
  position: relative !important;
}

.lukhang-medical-update-header-custom {
  background: #0d6efd !important;
  color: white !important;
  border: none !important;
  border-radius: 1rem 1rem 0 0 !important;
  padding: 1.5rem 2rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  flex-shrink: 0 !important;
  min-height: 80px !important;
}

.lukhang-medical-update-title-custom {
  color: white !important;
  font-weight: 600 !important;
  font-size: 1.4rem !important;
  margin: 0 !important;
  flex: 1 !important;
  display: flex !important;
  align-items: center !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.lukhang-medical-update-title-custom i {
  color: white !important;
  margin-right: 0.75rem !important;
  font-size: 1.2rem !important;
}

.lukhang-medical-update-close-button-custom {
  background: rgba(255, 255, 255, 0.15) !important;
  border: 2px solid rgba(255, 255, 255, 0.4) !important;
  color: white !important;
  border-radius: 50% !important;
  width: 48px !important;
  height: 48px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.3s ease !important;
  flex-shrink: 0 !important;
  margin-left: 1.5rem !important;
  font-size: 1.1rem !important;
  text-decoration: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.lukhang-medical-update-close-button-custom:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  border-color: rgba(255, 255, 255, 0.6) !important;
  color: white !important;
  transform: rotate(90deg) scale(1.15) !important;
  text-decoration: none !important;
}

.lukhang-medical-update-close-button-custom:focus {
  box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.3) !important;
  color: white !important;
  outline: none !important;
  text-decoration: none !important;
}

.lukhang-medical-update-close-button-custom:active {
  color: white !important;
  text-decoration: none !important;
}

.lukhang-medical-update-body-custom {
  flex: 1 !important;
  overflow-y: auto !important;
  max-height: calc(90vh - 240px) !important;
  padding: 2rem !important;
  min-height: 300px !important;
}

.lukhang-medical-update-footer-custom {
  flex-shrink: 0 !important;
  padding: 2.5rem 2rem !important;
  background: #f8f9fa !important;
  border-top: 1px solid #e9ecef !important;
  min-height: 120px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 1.5rem !important;
  position: relative !important;
  z-index: 10 !important;
  margin-top: auto !important;
}

@media (max-width: 992px) {
  .lukhang-medical-update-modal-wrapper .modal-dialog {
    width: 95vw !important;
    margin: 1rem auto !important;
  }

  .lukhang-medical-update-header-custom {
    padding: 1.25rem 1.5rem !important;
    min-height: 70px !important;
  }

  .lukhang-medical-update-title-custom {
    font-size: 1.2rem !important;
  }

  .lukhang-medical-update-close-button-custom {
    width: 42px !important;
    height: 42px !important;
    margin-left: 1rem !important;
  }

  .lukhang-medical-update-body-custom {
    padding: 1.5rem !important;
    max-height: calc(90vh - 220px) !important;
  }

  .lukhang-medical-update-footer-custom {
    padding: 2rem 1.5rem !important;
    min-height: 110px !important;
  }
}

@media (max-width: 768px) {
  .lukhang-medical-update-modal-wrapper .modal-dialog {
    width: 98vw !important;
    margin: 0.5rem auto !important;
  }

  .lukhang-medical-update-header-custom {
    padding: 1rem 1.25rem !important;
    min-height: 65px !important;
  }

  .lukhang-medical-update-title-custom {
    font-size: 1.1rem !important;
  }

  .lukhang-medical-update-close-button-custom {
    width: 38px !important;
    height: 38px !important;
    margin-left: 0.75rem !important;
    font-size: 1rem !important;
  }

  .lukhang-medical-update-body-custom {
    padding: 1.25rem !important;
    max-height: calc(90vh - 200px) !important;
  }

  .lukhang-medical-update-footer-custom {
    padding: 1.75rem 1.25rem !important;
    min-height: 100px !important;
  }
}

/* Additional styling for dropdown elements */
.lukhang-medical-update-modal-wrapper .form-select:focus {
  border-color: #0d6efd !important;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25) !important;
}

.lukhang-medical-update-modal-wrapper .form-control:focus {
  border-color: #0d6efd !important;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25) !important;
}

/* Dropdown menu styling for update modal */
.lukhang-medical-update-modal-wrapper .dropdown-menu {
  border: 1px solid #0d6efd !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.lukhang-medical-update-modal-wrapper .dropdown-item:hover {
  background-color: #e7f1ff !important;
  color: #084298 !important;
}

/* Fix severity level dropdown styling */
.lukhang-medical-update-modal-wrapper select[name="severityLevel"] {
  padding-right: 2.5rem !important;
}
