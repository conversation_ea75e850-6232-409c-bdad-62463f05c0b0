{"name": "school-medical-website", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "start": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@reduxjs/toolkit": "^2.8.2", "a.js": "^0.0.1", "aos": "^2.3.4", "axios": "^1.10.0", "bootstrap": "^5.3.7", "chart.js": "^4.5.0", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "jspdf": "^3.0.1", "lodash": "^4.17.21", "react": "^18.2.0", "react-bootstrap": "^2.10.10", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.1", "react-toastify": "^11.0.5", "redux": "^5.0.1", "sweetalert2": "^11.22.2", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-react": "^4.0.4", "vite": "^6.3.5"}}