.admin_ui_layout {
  min-height: 100vh;
  background: #fafbfc;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  color: #1a202c;
}

.admin_ui_main {
  display: flex;
  flex: 1;
  height: 100vh;
  overflow: hidden;
  margin-left: 280px;
}

.admin_ui_content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  background: #ffffff;
  position: relative;
  padding: 32px;
  margin: 16px;
  border-radius: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  max-height: calc(100vh - 32px);
}

.admin_ui_content::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle at center, rgba(99, 102, 241, 0.03) 0%, transparent 70%);
  border-radius: 50%;
  z-index: 0;
  pointer-events: none;
}

/* Responsive */
@media (max-width: 768px) {
  .admin_ui_main {
    margin-left: 70px;
  }

  .admin_ui_content {
    padding: 20px;
    margin: 8px;
    border-radius: 12px;
  }
}

@media (max-width: 576px) {
  .admin_ui_main {
    margin-left: 60px;
  }

  .admin_ui_content {
    padding: 16px;
    margin: 4px;
  }
}