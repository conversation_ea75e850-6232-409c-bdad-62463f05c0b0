/**
 * StudentProfile.css - Thiết kế mới
 * Sử dụng tiền tố sp- để tránh xung đột
 * Tối ưu cho hiển thị thông tin học sinh
 */

/* ------- Basic Reset & Variables ------- */
.sp-container * {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  --sp-primary: #015C92;
  --sp-primary-light: #88CDF6;
  --sp-primary-bg: #f0f8ff;
  --sp-primary-border: #88CDF6;
  --sp-text-primary: #1e293b;
  --sp-text-dark: #1a202c;
  --sp-text-medium: #4a5568;
  --sp-text-light: #718096;
  --sp-border: #e2e8f0;
  --sp-bg-light: #f7fafc;
  --sp-white: #ffffff;
  --sp-shadow: 0 1px 3px rgba(1, 92, 146, 0.2);
  --sp-radius: 12px;
  --sp-icon-blue: #015C92;
  --sp-icon-purple: #2D82B5;
  --sp-icon-green: #428CD4;
}

/* ------- Main Container ------- */
.sp-container {
  max-width: 1000px;
  margin: 0 auto;
  font-family: 'Be Vietnam Pro', system-ui, -apple-system, sans-serif;
  color: var(--sp-text-dark);
  background-color: #ffffff;
  min-height: 100vh;
  padding: 2rem 1rem;
}

/* Override handled by global.css */

/* ------- Header Styles ------- */
.sp-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  position: relative;
  background: linear-gradient(135deg, #015c92 0%, #2d82b5 50%, #428cd4 100%);
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.sp-title {
  margin: 0;
  text-align: center;
  font-size: 2rem;
  font-weight: 700;
  /* color: var(--sp-text-primary); */
  color: #ffffff;
  padding-bottom: 12px;
  position: relative;
  flex: 1;
}

.sp-title::after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: 0;
  width: 60px;
  height: 3px;
  background: var(--sp-primary);
  border-radius: 2px;
  transform: translateX(-50%);
}

.sp-back-btn {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
  padding: 8px 15px;
  border-radius: 8px;
  font-size: 13px;
}

/* Responsive fixes */
@media (max-width: 768px) {
  .sp-header {
    flex-direction: column;
    margin-bottom: 20px;
  }
  
  .sp-title {
    margin: 20px auto;
    order: 2;
  }
  
  .sp-back-btn {
    position: static;
    transform: none;
    margin-bottom: 15px;
    order: 1;
    align-self: flex-start;
  }
}

/* ------- Info Card Layout ------- */
.sp-card {
  background: var(--sp-white);
  border-radius: var(--sp-radius);
  box-shadow: var(--sp-shadow);
  margin-bottom: 20px;
  overflow: hidden;
  border: 1px solid var(--sp-border);
}

.sp-card-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--sp-border);
  background-color: #f8fafc;
}

.sp-card-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--sp-primary-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.sp-card-icon i {
  font-size: 14px;
}

.sp-icon-blue {
  color: var(--sp-icon-blue);
}

.sp-icon-purple {
  color: var(--sp-icon-purple);
}

.sp-icon-green {
  color: var(--sp-icon-green);
}

.sp-card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--sp-text-primary);
}

.sp-card-body {
  padding: 20px;
}

.sp-card-footer {
  padding: 12px 20px;
  border-top: 1px solid var(--sp-border);
  background-color: var(--sp-bg-light);
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* ------- Student Profile Header ------- */
.sp-student-card {
  background-color: var(--sp-primary-bg);
  border: 1px solid var(--sp-primary-border);
  border-radius: var(--sp-radius);
  padding: 0;
  margin-bottom: 20px;
  box-shadow: var(--sp-shadow);
  overflow: hidden;
}

.sp-student-header {
  display: flex;
  align-items: center;
  padding: 24px;
}

.sp-student-avatar {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background-color: var(--sp-white);
  overflow: hidden;
  border: 2px solid var(--sp-white);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  margin-right: 24px;
  flex-shrink: 0;
}

.sp-student-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.sp-student-info {
  flex: 1;
}

.sp-student-name {
  font-size: 18px;
  font-weight: 700;
  color: var(--sp-text-dark);
  margin-bottom: 4px;
}

.sp-student-details {
  font-size: 13px;
  color: var(--sp-text-medium);
  line-height: 1.5;
}

/* ------- Info Grid ------- */
.sp-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 12px;
}

.sp-info-item {
  margin-bottom: 12px;
}

.sp-info-label {
  display: block;
  font-size: 12px;
  color: var(--sp-text-light);
  margin-bottom: 4px;
}

.sp-info-value {
  font-size: 14px;
  font-weight: 500;
  color: var(--sp-text-dark);
}

/* ------- Button Styles ------- */
.sp-btn {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  gap: 6px;
  white-space: nowrap;
  margin-left: 20px;
}

.sp-btn i {
  font-size: 14px;
}

.sp-btn-primary {
  background-color: var(--sp-primary);
  color: white;
}

.sp-btn-primary:hover {
  background-color: #2563eb;
  box-shadow: 0 2px 5px rgba(37, 99, 235, 0.3);
}

.sp-btn-secondary {
  background-color: white;
  border: 1px solid var(--sp-border);
  color: var(--sp-text-dark);
}

.sp-btn-secondary:hover {
  background-color: var(--sp-bg-light);
  border-color: #d1d5db;
}

/* ------- Loading States ------- */
.sp-loading {
  padding: 40px 0;
  text-align: center;
  color: var(--sp-text-medium);
}

.sp-error {
  padding: 20px;
  text-align: center;
  background-color: #fee2e2;
  border-radius: var(--sp-radius);
  color: #b91c1c;
  margin: 20px 0;
}

/* ------- Special styling for info sections ------- */
.sp-info-section-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--sp-text-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

/* ------- Responsive Layout ------- */
@media (max-width: 768px) {
  .sp-info-grid {
    grid-template-columns: 1fr;
  }
  
  .sp-card-footer {
    flex-direction: column;
  }
  
  .sp-btn {
    width: 100%;
    justify-content: center;
  }
  
  .sp-student-header {
    flex-direction: column;
    text-align: center;
  }
  
  .sp-student-avatar {
    margin: 0 auto 12px auto;
    width: 150px;
    height: 150px;
  }
  
  .sp-card-header {
    padding: 14px;
  }
  
  .sp-card-body {
    padding: 16px;
  }
}

/* Responsive cho màn hình rất nhỏ */
@media (max-width: 480px) {
  .sp-student-avatar {
    width: 120px !important;
    height: 120px !important;
  }
  
  .sp-container {
    padding: 1rem 0.5rem;
  }
}

/* Thêm vào phần CSS hiện có */

/* Hiển thị email trên toàn bộ chiều rộng grid */
.sp-full-width {
  grid-column: 1 / -1; /* Chiếm toàn bộ chiều rộng của grid */
}

.sp-full-width .sp-info-value {
  word-break: break-all; /* Ngắt từ nếu cần thiết */
  max-width: 100%; /* Đảm bảo không vượt quá chiều rộng container */
}

/* ------- Student Selection Styles (chonhocsinhtabparent) ------- */
.chonhocsinhtabparent {
  background: var(--sp-white);
  border: 1px solid var(--sp-border);
  border-radius: var(--sp-radius);
  margin-bottom: 20px;
  box-shadow: var(--sp-shadow);
}

.chonhocsinhtabparent-select,
.selectstudentfix {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--sp-border);
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  background-color: var(--sp-white);
  color: var(--sp-text-primary);
  transition: all 0.3s ease;
}

.chonhocsinhtabparent-select:focus,
.selectstudentfix:focus {
  outline: none;
  border-color: var(--sp-primary);
  box-shadow: 0 0 0 3px rgba(1, 92, 146, 0.1);
}

.chonhocsinhtabparent-select:hover,
.selectstudentfix:hover {
  border-color: var(--sp-primary-light);
}

/* Responsive styles for student selection */
@media (max-width: 768px) {
  .chonhocsinhtabparent {
    margin-bottom: 16px;
  }
  
  .chonhocsinhtabparent-select,
  .selectstudentfix {
    padding: 10px 12px;
    font-size: 16px; /* Prevents zoom on iOS */
  }
}
