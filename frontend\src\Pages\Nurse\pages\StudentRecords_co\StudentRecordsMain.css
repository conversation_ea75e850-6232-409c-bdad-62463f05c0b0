.student-records-page {
  width: 100%;
  height: 100%;
}

.student-records-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f9f9f9;
  border-radius: 10px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.student-records-tabs {
  display: flex;
  background-color: #fff;
  border-radius: 10px 10px 0 0;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
  position: sticky;
  top: 0;
  z-index: 10;
}

.sr-tab {
  padding: 15px 25px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  border-bottom: 3px solid transparent;
  transition: all 0.25s ease-in-out;
  color: #637381;
  font-size: 0.95rem;
}

.sr-tab i {
  font-size: 1.1rem;
  color: #4a90e2;
}

.sr-tab:hover {
  background-color: #f0f7ff;
  color: #4a90e2;
}

.sr-tab.active {
  border-bottom-color: #4a90e2;
  color: #4a90e2;
  background-color: #f0f7ff;
}

.student-records-content {
  flex: 1;
  overflow-y: auto;
  background-color: #f9f9f9;
  border-radius: 0 0 10px 10px;
  padding: 5px;
}

.page-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1d5d86;
  margin: 0 0 20px 0;
  padding: 15px 20px 0;
  position: relative;
}

.page-title:after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 20px;
  width: 60px;
  height: 3px;
  background-color: #4cc1a9;
  border-radius: 2px;
}
