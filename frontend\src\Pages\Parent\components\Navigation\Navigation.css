/* Update these CSS variables to match your global.css */
/* :root {
  --primary: #154082;
  --secondary: #075a64;
  --primary-light: #3a75c4;
  --text-dark: #000000;
  --text-medium: #666666;
  --bg-white: #ffffff;
} */

/* Modern Navigation - Dark Blue Theme for Parent Pages */

.main-navigation {
  display: block; /* Hiển thị navigation */
  background: linear-gradient(135deg, #5BA8A0 0%, #94B447 25%, #3B5284 50%, #5D6E1E 100%);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0;
  position: sticky;
  top: 130px; /* Dưới header 130px */
  z-index: 900;
  box-shadow: 0 2px 10px rgba(91, 168, 160, 0.15);
  transition: top 0.35s cubic-bezier(0.21, 0.6, 0.35, 1);
}

/* Khi header scroll và shrink xuống 60px */
.header.scrolled ~ .main-navigation,
.main-navigation.header-scrolled {
  top: 60px; /* <PERSON><PERSON><PERSON><PERSON> chỉnh để stick sát với header đã scroll */
}

.nav-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 1520px;
  margin: 0 auto;
  padding: 0 2rem;
}

.logo {
  flex-shrink: 0;
}

.logo-img {
  height: 40px;
  width: auto;
}

.main-nav-list {
  display: flex;
  list-style: none;
  gap: 0;
  align-items: center;
  margin: 0;
  padding: 0;
  width: 100%;
  justify-content: center;
}

.main-nav-item {
  flex: 1;
  max-width: 300px;
}

.main-nav-link {
  display: block;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  padding: 1rem 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  border-radius: 0;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.main-nav-link:hover {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  transform: translateY(-1px);
  border-radius: 12px;
}

.main-nav-link.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: 600;
  position: relative;
  border-radius: 12px;
}

.main-nav-link.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #FBBF24, #F59E0B);
}

/* Alternative: Hide navigation entirely for cleaner design */
.main-navigation.hidden {
  display: none;
}

/* Responsive */
@media (max-width: 640px) {
  .main-navigation {
    display: none; /* Hide on mobile since header has main navigation */
  }
}

@media (max-width: 768px) {
  .nav-wrapper {
    padding: 0 1rem;
  }
  
  .main-nav-link {
    padding: 0.75rem 1rem;
    font-size: 0.85rem;
  }
}

/* Modern floating navigation - alternative style */
.modern-navigation {
  background: linear-gradient(135deg, #5BA8A0 0%, #94B447 25%, #3B5284 50%, #5D6E1E 100%);
  backdrop-filter: blur(12px);
  border-radius: 20px;
  margin: 1rem auto;
  max-width: 800px;
  padding: 0.5rem;
  box-shadow: 0 10px 30px rgba(91, 168, 160, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-navigation .main-nav-list {
  gap: 0.5rem;
}

.modern-navigation .main-nav-link {
  border-radius: 15px;
  padding: 0.75rem 1.5rem;
  color: rgba(255, 255, 255, 0.9);
}

.modern-navigation .main-nav-link:hover {
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 4px 15px rgba(91, 168, 160, 0.3);
}

.modern-navigation .main-nav-link.active {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 15px rgba(91, 168, 160, 0.3);
}

/* Container để đảm bảo navigation cố định có vị trí đúng */
.navigation-wrapper {
  position: relative;
  height: 60px; /* Chiều cao của navigation */
  width: 100%;
}

/* Ẩn thanh cuộn cho navigation */
.modern-navigation::-webkit-scrollbar {
  display: none;
}

/* Style khi cuộn trang */
.modern-navigation.scrolled {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  background-color: rgba(255, 255, 255, 0.97);
  backdrop-filter: blur(10px);
}

/* Container cho menu */
.nav-container {
  max-width: 1360px;
  margin: 0 auto;
  padding: 0;
  height: 100%;
  overflow: visible !important;
}

/* Menu chính */
.nav-menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  justify-content: space-around;
  width: 100%;
  height: 100%;
}

/* Item trong menu */
.nav-item {
  position: relative;
  display: flex;
  justify-content: center;
  flex: 1;
  margin: 0;
  padding: 0;
  height: 100%;
}

/* Style cho link */
.modern-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  width: 100%;
  text-align: center;
  color: var(--text-dark, #000000);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  transition: all 0.2s ease;
  position: relative;
  gap: 5px;
  height: 100%;
}

/* Icon trong link */
.modern-link i {
  font-size: 1.2rem;
  margin-bottom: 4px;
  transition: all 0.3s ease;
}

/* Tiêu đề navigation */
.nav-title {
  display: block;
  white-space: nowrap;
}

/* Hiệu ứng hover */
.modern-link:hover {
  color: var(--primary-light, #3a75c4);
  background-color: rgba(240, 244, 248, 0.5);
}

/* Hiệu ứng hover cho icon */
.modern-link:hover i {
  transform: translateY(-2px);
}

/* Style cho link active */
.modern-link.active {
  color: var(--primary-color, #154082);
  font-weight: 600;
  position: relative;
}

/* Style cho icon khi active */
.modern-link.active i {
  color: var(--primary-color, #154082);
}

/* Indicator cho active link */
.active-indicator {
  position: absolute;
  bottom: 0;
  left: 10%;
  width: 80%;
  height: 3px;
  background-color: var(--primary-color, #154082);
  border-radius: 1.5px;
}

/* JavaScript will handle the scrolled state */
/* Add this class via JS when header is scrolled */
.navigation-adjust-for-scroll {
  top: 60px !important;
}