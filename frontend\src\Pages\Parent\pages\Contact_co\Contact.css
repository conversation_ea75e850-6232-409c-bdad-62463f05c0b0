.parent-contact-page {
  --primary: #015C92;
  --primary-dark: #2D82B5;
  --secondary: #88CDF6;
  --text-dark: #1e293b;
  --text-medium: #4a5568;
  --text-light: #718096;
  --success: #428CD4;
  --error: #ef4444;
  --warning: #88CDF6;
  background-color: #ffffff;
  min-height: 100vh;
}

/* Header */
.contact-header {
  background: linear-gradient(135deg, #015C92 0%, #2D82B5 30%, #428CD4 60%, #88CDF6 100%);
  color: #ffffff;
  padding: 50px 0;
  text-align: center;
  position: relative;
  overflow: hidden;
  border-radius: 0 0 50px 50px;
  margin-bottom: 40px;
  box-shadow: 0 10px 25px -5px rgba(1, 92, 146, 0.3);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-header-actions {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 2;
}

.refresh-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 10px 15px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
}

.refresh-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.contact-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 60px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23ffffff' fill-opacity='1' d='M0,128L48,149.3C96,171,192,213,288,218.7C384,224,480,192,576,181.3C672,171,768,181,864,186.7C960,192,1056,192,1152,181.3C1248,171,1344,149,1392,138.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
  background-size: cover;
  background-position: center;
}

.contact-header-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

/* Override handled by global.css */

.contact-header h1 {
  color: #fff;
  font-size: 36px;
  font-weight: 800;
  margin-bottom: 16px;
}

.contact-header p {
  font-size: 16px;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}

/* Contact content */
.contact-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px 60px;
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 40px;
  position: relative;
}

/* Override handled by global.css */

/* Information cards */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.info-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 5px 15px -5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.info-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px -5px rgba(0, 0, 0, 0.15);
}

.info-icon {
  width: 40px;
  height: 40px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  color: var(--primary);
  font-size: 16px;
  transition: all 0.3s ease;
}

.info-card:hover .info-icon {
  background: var(--primary);
  color: white;
}

.info-card h3 {
  font-size: 16px;
  margin-bottom: 10px;
  color: var(--text-dark);
}

.info-card p {
  color: var(--text-medium);
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 6px;
}

.info-card p:last-child {
  margin-bottom: 0;
}

/* Previous contacts history */
.previous-contacts {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 5px 15px -5px rgba(0, 0, 0, 0.1);
  margin-top: 5px;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.previous-contacts h3 {
  font-size: 16px;
  margin-bottom: 15px;
  color: var(--text-dark);
  padding-bottom: 10px;
  border-bottom: 1px solid #e2e8f0;
}

.contact-history {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.history-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  background: #f8fafc;
  transition: all 0.3s ease;
}

.history-item:hover {
  background: #f1f5f9;
}

.history-status {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  flex-shrink: 0;
}

.history-status.completed {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.history-status.pending {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.history-content {
  flex-grow: 1;
}

.history-content h4 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--text-dark);
}

.history-content p {
  font-size: 13px;
  color: var(--text-medium);
  margin-bottom: 4px;
}

.history-date {
  font-size: 12px;
  color: var(--text-light);
}

/* Contact form */
.contact-form-container {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 10px 30px -5px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.contact-form-container h2 {
  font-size: 24px;
  margin-bottom: 25px;
  color: var(--text-dark);
  position: relative;
  padding-bottom: 12px;
}

.contact-form-container h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(to right, var(--primary), var(--primary-dark));
  border-radius: 2px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-dark);
  font-size: 14px;
}

.required {
  color: var(--error);
}

input, select, textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 10px;
  background-color: #f8fafc;
  font-size: 14px;
  color: var(--text-dark);
  transition: all 0.3s ease;
}

input.filled, input[readonly] {
  background-color: #edf2ff;
  border-color: #dbeafe;
  color: var(--text-dark);
  cursor: default;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background-color: white;
}

input::placeholder, textarea::placeholder {
  color: #cbd5e1;
}

select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23475569'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  padding-right: 40px;
}

textarea {
  resize: none;
  height: 150px;
}

/* File upload */
.file-upload {
  margin-bottom: 30px;
}

.upload-box {
  border: 2px dashed #cbd5e1;
  border-radius: 10px;
  padding: 25px;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  margin-bottom: 8px;
}

.upload-box:hover {
  border-color: var(--primary);
  background-color: rgba(59, 130, 246, 0.03);
}

.upload-box i {
  font-size: 30px;
  color: var(--primary);
  margin-bottom: 10px;
}

.upload-box p {
  color: var(--text-medium);
  font-size: 14px;
}

.file-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.file-info {
  font-size: 12px;
  color: var(--text-light);
  text-align: center;
}

.submit-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 10px;
  font-weight: 600;
  font-size: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 10px;
}

.submit-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px -5px rgba(59, 130, 246, 0.4);
}

.submit-btn i {
  font-size: 16px;
  transition: transform 0.3s ease;
}

.submit-btn:hover i {
  transform: translateX(4px);
}

/* Form messages */
.form-message {
  padding: 12px 16px;
  border-radius: 10px;
  margin-bottom: 20px;
  font-size: 14px;
}

.form-message.success {
  background-color: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
  color: var(--success);
}

.form-message.error {
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  color: var(--error);
}

/* Student information display */
.student-info-display {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.student-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 16px;
  border: 2px solid #3b82f6;
}

.student-details {
  flex: 1;
}

.student-name {
  font-weight: 600;
  font-size: 16px;
  color: #1e293b;
  margin-bottom: 4px;
}

.student-class {
  font-size: 14px;
  color: #64748b;
}

.no-student-info {
  padding: 12px;
  background-color: #fee2e2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #b91c1c;
  font-size: 14px;
}

/* Student selection styles */
.chonhocsinhtabparent {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 250px;
}

.chonhocsinhtabparent select {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  color: #333;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.chonhocsinhtabparent select:focus {
  border-color: #4a6cf7;
  box-shadow: 0 0 0 3px rgba(74, 108, 247, 0.1);
  outline: none;
}

.selected-student-info {
  margin-top: 10px;
  border-top: 1px dashed #e0e0e0;
  padding-top: 15px;
}

.student-info-display {
  display: flex;
  align-items: center;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.student-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #fff;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.student-details {
  margin-left: 15px;
}

.student-name {
  font-weight: 600;
  font-size: 18px;
  color: #333;
}

.student-class {
  color: #666;
  margin-top: 3px;
}

.student-school {
  color: #888;
  font-size: 14px;
  margin-top: 2px;
}

.no-student-info {
  padding: 15px;
  background-color: #fff3cd;
  border: 1px solid #ffeeba;
  border-radius: 5px;
  color: #856404;
}

/* Responsive styles */
@media (max-width: 992px) {
  .contact-container {
    grid-template-columns: 1fr;
    padding: 0 20px 50px;
    gap: 30px;
  }
  
  .contact-header {
    padding: 40px 0;
  }
  
  .contact-header h1 {
    font-size: 30px;
  }
  
  .contact-form-container {
    padding: 25px;
  }
  
  .contact-info {
    order: 2;
  }
  
  .contact-form-container {
    order: 1;
  }
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
    gap: 0;
  }
  
  .contact-header h1 {
    font-size: 28px;
  }
  
  .contact-header p {
    font-size: 14px;
  }
}

@media (max-width: 576px) {
  .contact-header {
    padding: 35px 0;
    border-radius: 0 0 30px 30px;
  }
  
  .contact-header h1 {
    font-size: 24px;
  }
  
  .contact-form-container h2 {
    font-size: 20px;
  }
  
  .info-card {
    padding: 15px;
  }
  
  .submit-btn {
    width: 100%;
    padding: 12px 24px;
    font-size: 14px;
  }
}