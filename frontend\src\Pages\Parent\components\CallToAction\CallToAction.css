/* Call to Action Section - Professional Blue Theme */

:root {
  --primary-blue: #015C92;
  --secondary-blue: #2D82B5;
  --accent-blue: #428CD4;
  --light-blue: #88CDF6;
  --lightest-blue: #BCE6FF;
  --blue-gradient: linear-gradient(135deg, #015C92 0%, #2D82B5 25%, #428CD4 50%, #88CDF6 75%, #BCE6FF 100%);
}

.cta {
  position: relative;
  background: var(--blue-gradient);
  padding: 80px 0 40px;
  color: #fff;
  overflow: hidden;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
  margin-bottom: -1px !important;
  margin-top: 0 !important;
}

/* Floating elements with Professional Blue tones */
.cta-float {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  z-index: 1;
  animation-fill-mode: both;
}

.cta-float-1 {
  width: 200px;
  height: 200px;
  top: -100px;
  left: 10%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(136, 205, 246, 0.1) 100%);
  animation: float 12s infinite ease-in-out;
}

.cta-float-2 {
  width: 120px;
  height: 120px;
  bottom: -60px;
  right: 15%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.08) 0%, rgba(66, 140, 212, 0.1) 100%);
  animation: float 8s infinite ease-in-out reverse;
}

.cta-float-3 {
  width: 80px;
  height: 80px;
  top: 60px;
  right: 10%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.12) 0%, rgba(45, 130, 181, 0.1) 100%);
  animation: float 15s infinite ease-in-out;
}

/* Container giống như Hero */
.cta-container {
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin: 0;
}

/* Content styling */
.cta-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 5%;
}

.cta-title {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  font-weight: 700;
  color: #fff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  line-height: 1.2;
}

.cta-description {
  font-size: 1.2rem;
  margin-bottom: 2.5rem;
  line-height: 1.7;
  font-weight: 400;
  opacity: 0.95;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 2.5rem;
}

/* Buttons */
.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.cta-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  text-decoration: none;
  gap: 0.75rem;
  transition: all 0.3s ease;
  background-color: rgba(255, 255, 255, 0.95);
  color: var(--primary-blue);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border: none;
  cursor: pointer;
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.25);
  background-color: #ffffff;
  color: var(--secondary-blue);
}

.cta-button-secondary {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.cta-button-secondary:hover {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border-color: rgba(255, 255, 255, 0.5);
}

.cta-button i {
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.cta-button:hover i {
  transform: translateX(3px);
}

/* Enhanced floating animation */
@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg) scale(1);
  }
  33% {
    transform: translateY(-15px) rotate(3deg) scale(1.05);
  }
  66% {
    transform: translateY(-25px) rotate(-2deg) scale(0.98);
  }
  100% {
    transform: translateY(0) rotate(0deg) scale(1);
  }
}

/* Background pattern overlay */
.cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="25" cy="25" r="2" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1.5" fill="white" opacity="0.08"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.12"/><circle cx="10" cy="80" r="1.5" fill="white" opacity="0.09"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.11"/></svg>');
  animation: float 25s ease-in-out infinite;
  z-index: 1;
}

/* Add subtle gradient overlay for depth */
.cta::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0, 0, 0, 0.05) 0%, transparent 50%, rgba(255, 255, 255, 0.05) 100%);
  z-index: 1;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .cta {
    padding: 70px 0 35px;
  }
  
  .cta-title {
    font-size: 2.2rem;
  }
  
  .cta-description {
    font-size: 1.1rem;
  }
}

@media (max-width: 768px) {
  .cta {
    padding: 60px 0 30px;
  }
  
  .cta-title {
    font-size: 2rem;
  }
  
  .cta-description {
    font-size: 1rem;
    margin-bottom: 2rem;
  }
  
  .cta-buttons {
    gap: 1rem;
  }
  
  .cta-button {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
  
  .cta-content {
    padding: 0 4%;
  }
}

@media (max-width: 480px) {
  .cta {
    padding: 50px 0 25px;
  }
  
  .cta-title {
    font-size: 1.75rem;
    margin-bottom: 1.25rem;
  }
  
  .cta-description {
    font-size: 0.95rem;
    margin-bottom: 1.75rem;
  }
  
  .cta-buttons {
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
  }
  
  .cta-button {
    width: 100%;
    max-width: 280px;
    padding: 0.75rem 1.25rem;
  }
  
  .cta-content {
    padding: 0 3%;
  }
  
  /* Adjust floating elements for mobile */
  .cta-float-1 {
    width: 150px;
    height: 150px;
    top: -75px;
  }
  
  .cta-float-2 {
    width: 100px;
    height: 100px;
    bottom: -50px;
  }
  
  .cta-float-3 {
    width: 60px;
    height: 60px;
  }
}

/* Thêm connector để nối với footer */
.footer-connector {
  height: 1px;
  background-color: var(--primary-blue);
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
  position: relative;
  z-index: 10;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .cta {
    background: linear-gradient(135deg, #0a1121 0%, #1a2332 25%, #2a3441 50%, #3a4550 75%, #4a5660 100%);
  }
  
  .cta-button {
    background-color: rgba(255, 255, 255, 0.9);
    color: var(--primary-blue);
  }
  
  .cta-button:hover {
    background-color: #ffffff;
  }
}
