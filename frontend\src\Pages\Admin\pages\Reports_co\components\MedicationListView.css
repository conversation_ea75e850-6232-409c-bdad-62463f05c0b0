/* Medication List View - Multi-Theme Support */

/* Container chính */
.reports-medication-list-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Theme-specific backgrounds */
.reports-medication-list-container.theme-blue {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.reports-medication-list-container.theme-green {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

.reports-medication-list-container.theme-purple {
  background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
}

.reports-medication-list-container.theme-orange {
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

.reports-medication-list-container.theme-teal {
  background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
}

/* Loading Section */
.reports-medication-loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 70vh;
  color: #64748b;
  gap: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.08);
  border: 1px solid #e2e8f0;
  padding: 40px;
}

.reports-medication-loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #f1f5f9;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.reports-medication-loading-section p {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

/* Header */
.reports-medication-header {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.08);
  position: relative;
  overflow: hidden;
}

.reports-medication-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #2563eb, #1d4ed8);
}

.reports-medication-header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.reports-medication-back-button {
  background: linear-gradient(135deg, #64748b, #475569);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(100, 116, 139, 0.3);
}

.reports-medication-back-button:hover {
  background: linear-gradient(135deg, #475569, #334155);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(100, 116, 139, 0.4);
}

.reports-medication-title {
  margin: 0;
  color: #1e293b;
  font-size: 32px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.reports-medication-subtitle {
  margin: 8px 0 0 0;
  color: #64748b;
  font-size: 16px;
  font-weight: 500;
}

/* Statistics Cards */
.reports-medication-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.reports-medication-stat-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.08);
  position: relative;
  overflow: hidden;
}

.reports-medication-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.reports-medication-stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(59, 130, 246, 0.15);
  border-color: #bfdbfe;
}

.reports-medication-stat-card.reports-medication-total::before {
  background: linear-gradient(90deg, #3b82f6, #2563eb);
}

.reports-medication-stat-card.reports-medication-low-stock::before {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.reports-medication-stat-card.reports-medication-near-expiry::before {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.reports-medication-stat-card.reports-medication-categories::before {
  background: linear-gradient(90deg, #10b981, #059669);
}

.reports-medication-stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.reports-medication-total .reports-medication-stat-icon {
  background: #007bff;
}

.reports-medication-low-stock .reports-medication-stat-icon {
  background: #ffc107;
}

.reports-medication-near-expiry .reports-medication-stat-icon {
  background: #dc3545;
}

.reports-medication-categories .reports-medication-stat-icon {
  background: #28a745;
}

.reports-medication-stat-content h3 {
  margin: 0 0 5px 0;
  font-size: 24px;
  font-weight: 700;
  color: #333;
}

.reports-medication-stat-content p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* Filters */
.reports-medication-filters {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 25px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.reports-medication-filter-group {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 15px;
  align-items: end;
}

.reports-medication-search-box {
  position: relative;
}

.reports-medication-search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

.reports-medication-search-box input {
  width: 100%;
  padding: 10px 15px 10px 35px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.reports-medication-search-box input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.reports-medication-filter-select {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background: white;
  cursor: pointer;
}

.reports-medication-filter-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.reports-medication-results-count {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
  color: #666;
  font-size: 14px;
  text-align: center;
}

/* Error Message */
.reports-medication-error-message {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.reports-medication-error-message button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
}

.reports-medication-error-message button:hover {
  background: #c82333;
}

/* Table */
.reports-medication-table-container {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow-x: auto;
  overflow-y: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.reports-medication-table {
  width: 100%;
  min-width: 1000px;
  border-collapse: collapse;
  font-size: 14px;
}

.reports-medication-table thead {
  background: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
}

.reports-medication-table th {
  padding: 15px 12px;
  text-align: left;
  font-weight: 600;
  color: #495057;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.reports-medication-table-row {
  border-bottom: 1px solid #eee;
  transition: background-color 0.3s ease;
}

.reports-medication-table-row:hover {
  background: #f8f9fa;
}

.reports-medication-table td {
  padding: 15px 12px;
  vertical-align: middle;
}

.reports-medication-table-stt {
  font-weight: 600;
  color: #666;
  width: 60px;
  text-align: center;
}

.reports-medication-table-name {
  min-width: 250px;
}

.reports-medication-name-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.reports-medication-item-name {
  font-weight: 600;
  color: #333;
  font-size: 15px;
  margin: 0;
}

.reports-medication-item-description {
  color: #666;
  font-size: 13px;
  line-height: 1.3;
}

.reports-medication-table-type {
  min-width: 120px;
}

.reports-medication-type-badge {
  background: #e9ecef;
  color: #495057;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  display: inline-block;
}

.reports-medication-table-stock {
  min-width: 100px;
  text-align: center;
}

.reports-medication-stock-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.reports-medication-stock-quantity {
  font-weight: 700;
  font-size: 16px;
  color: #333;
}

.reports-medication-stock-status {
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.reports-medication-stock-status.in-stock {
  background: #d4edda;
  color: #155724;
}

.reports-medication-stock-status.low-stock {
  background: #fff3cd;
  color: #856404;
}

.reports-medication-stock-status.out-of-stock {
  background: #f8d7da;
  color: #721c24;
}

.reports-medication-table-expiry {
  min-width: 120px;
  text-align: center;
}

.reports-medication-expiry-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.reports-medication-expiry-date {
  font-weight: 600;
  font-size: 13px;
  color: #333;
}

.reports-medication-expiry-status {
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.reports-medication-expiry-status.good {
  background: #d4edda;
  color: #155724;
}

.reports-medication-expiry-status.warning {
  background: #fff3cd;
  color: #856404;
}

.reports-medication-expiry-status.expired {
  background: #f8d7da;
  color: #721c24;
}

.reports-medication-table-actions {
  width: 80px;
  text-align: center;
}

.reports-medication-action-button {
  background: #007bff;
  color: white;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.reports-medication-action-button:hover {
  background: #0056b3;
  transform: scale(1.1);
}

/* Admin History Toolbar */
.admin-history-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  gap: 24px;
  flex-wrap: wrap;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 24px 28px;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.admin-search-filter-group {
  display: flex;
  gap: 20px;
  flex: 1;
  min-width: 320px;
}

.admin-search-box {
  position: relative;
  flex: 2;
  max-width: 420px;
}

.admin-search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #3b82f6;
  font-size: 1.1rem;
  z-index: 1;
}

.admin-search-box input {
  width: 100%;
  padding: 14px 20px 14px 44px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  background: #ffffff;
  color: #1e293b;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-search-box input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.admin-search-box input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.admin-filter-dropdown {
  position: relative;
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 200px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-filter-dropdown:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.admin-filter-icon {
  position: absolute;
  left: 16px;
  color: #3b82f6;
  font-size: 1.1rem;
  z-index: 1;
}

.admin-filter-dropdown select {
  padding: 14px 20px 14px 44px;
  border: none;
  border-radius: 12px;
  background: transparent;
  font-size: 0.875rem;
  min-width: 200px;
  cursor: pointer;
  color: #1e293b;
  font-weight: 600;
}

.admin-filter-dropdown select:focus {
  outline: none;
}

.admin-toolbar-buttons {
  display: flex;
  gap: 16px;
  align-items: center;
}

.admin-results-count {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.admin-refresh-button {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 14px 24px;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  font-size: 0.875rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.admin-refresh-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
}

.admin-refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* No Data */
.reports-medication-no-data {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  color: #666;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.reports-medication-no-data i {
  font-size: 48px;
  color: #ccc;
  margin-bottom: 15px;
}

.reports-medication-no-data h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 20px;
}

.reports-medication-no-data p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

/* Responsive */
@media (max-width: 1024px) {
  .reports-medication-filter-group {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .reports-medication-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .reports-medication-list-container {
    padding: 15px;
  }
  
  .reports-medication-header {
    padding: 20px;
  }
  
  .reports-medication-header-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .reports-medication-title {
    font-size: 24px;
  }
  
  .reports-medication-stats {
    grid-template-columns: 1fr;
  }
  
  .reports-medication-table-container {
    overflow-x: auto;
  }
  
  .reports-medication-table {
    min-width: 700px;
  }
  
  .reports-medication-table th,
  .reports-medication-table td {
    padding: 10px 8px;
  }
}

@media (max-width: 480px) {
  .reports-medication-header {
    padding: 15px;
  }
  
  .reports-medication-title {
    font-size: 20px;
  }
  
  .reports-medication-subtitle {
    font-size: 14px;
  }
  
  .reports-medication-stat-card {
    padding: 15px;
  }
  
  .reports-medication-stat-content h3 {
    font-size: 20px;
  }
  
  .reports-medication-filters {
    padding: 15px;
  }
  
  .reports-medication-no-data {
    padding: 30px 15px;
  }
  
  .reports-medication-no-data h3 {
    font-size: 18px;
  }
}

/* Status Badges for Stock and Expiry Status */
.reports-medication-status-badge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
  min-width: 80px;
  transition: all 0.3s ease;
}

/* Stock Status Badges */
.reports-medication-in-stock {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
  border: 1px solid #c3e6cb;
}

.reports-medication-low-stock {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  color: #856404;
  border: 1px solid #ffeaa7;
}

.reports-medication-out-of-stock {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Expiry Status Badges */
.reports-medication-good {
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.reports-medication-warning {
  background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
  color: #b8860b;
  border: 1px solid #fdcb6e;
}

.reports-medication-expired {
  background: linear-gradient(135deg, #fab1a0 0%, #e17055 100%);
  color: #8b0000;
  border: 1px solid #e17055;
}

/* Hover effects for status badges */
.reports-medication-status-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Table cell styling for status columns */
.reports-medication-table-stock-status,
.reports-medication-table-expiry-status {
  text-align: center;
  min-width: 120px;
  padding: 12px 8px;
}

/* Additional styling for better visual hierarchy */
.reports-medication-table-stock-status .reports-medication-status-badge,
.reports-medication-table-expiry-status .reports-medication-status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

/* Pagination Styles */
.admin-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
  margin-top: 0;
}

.admin-pagination-info {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.admin-pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.admin-pagination-btn {
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  background: white;
  color: #64748b;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
  min-width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.admin-pagination-btn:hover:not(:disabled) {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #475569;
}

.admin-pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.admin-pagination-btn.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.admin-pagination-btn.active:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.admin-pagination-ellipsis {
  padding: 8px 4px;
  color: #9ca3af;
  font-size: 0.875rem;
}

@media (max-width: 768px) {
  .admin-pagination {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
  }

  .admin-pagination-controls {
    flex-wrap: wrap;
    justify-content: center;
  }
}
