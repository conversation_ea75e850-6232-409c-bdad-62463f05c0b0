# 🏥 Logic Hiển thị Trạng thái "Đã tạo HS" - Health Checkup Module

## 📍 **Vị trí Logic trong CampaignDetailPage.jsx**

### 🎯 **Tổng quan:**
Logic hiển thị trạng thái "đã tạo hồ sơ" được xử lý thông qua:
1. **API trả về field `hasCheckupRecord`** từ backend
2. **Conditional rendering** dựa trên field này
3. **Reload data** sau khi tạo hồ sơ thành công
4. **Button state management** để hiển thị đúng trạng thái

---

## 🔄 **1. Logic Kiểm tra Trạng thái (renderCheckupActions)**

### **📍 Vị trí:** Lines 187-215 trong `CampaignDetailPage.jsx`

<augment_code_snippet path="frontend/src/Pages/Nurse/pages/HealthCheckups_co/CheckupList/CampaignDetailPage.jsx" mode="EXCERPT">
```javascript
// Render checkup actions
const renderCheckupActions = (student) => {
  if (student.hasCheckupRecord) {
    return (
      <Button variant="outline-success" size="sm" disabled>
        <FaEdit className="me-1" />
        Đã khám
      </Button>
    );
  } else if (student.consentStatus === 'APPROVED') {
    return (
      <Button 
        variant="outline-primary" 
        size="sm" 
        onClick={() => handleCreateCheckup(student)}
      >
        <FaPlus className="me-1" />
        Tạo hồ sơ khám
      </Button>
    );
  } else {
    return (
      <Button variant="outline-secondary" size="sm" disabled>
        <FaTimesCircle className="me-1" />
        Chưa đồng ý
      </Button>
    );
  }
};
```
</augment_code_snippet>

### **🎯 Logic:**
- **Điều kiện "Đã khám":** `student.hasCheckupRecord === true`
- **Button disabled:** Màu xanh, không thể click
- **Icon:** `FaEdit` với text "Đã khám"
- **Fallback:** Nếu chưa có record và đã đồng ý → hiển thị "Tạo hồ sơ khám"

---

## 🔄 **2. Load Dữ liệu từ API**

### **📍 Vị trí:** Lines 52-88 - `useEffect` load campaign data

<augment_code_snippet path="frontend/src/Pages/Nurse/pages/HealthCheckups_co/CheckupList/CampaignDetailPage.jsx" mode="EXCERPT">
```javascript
// Load campaign and students data
useEffect(() => {
  const loadCampaignData = async () => {
    if (!campaignId) {
      setError('ID chiến dịch không hợp lệ');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      
      // Load campaigns to find the specific one
      const campaignsData = await getHealthCampaigns();
      const foundCampaign = campaignsData.find(c => c.id === parseInt(campaignId));
      
      if (!foundCampaign) {
        setError('Không tìm thấy chiến dịch');
        setLoading(false);
        return;
      }

      setCampaign(foundCampaign);

      // Load students for this campaign
      const studentsData = await getCampaignStudents(foundCampaign.id);
      setCampaignStudents(studentsData);
      
    } catch (err) {
      setError('Lỗi tải dữ liệu chiến dịch');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  loadCampaignData();
}, [campaignId, getHealthCampaigns, getCampaignStudents]);
```
</augment_code_snippet>

### **🎯 Logic:**
- **API Call:** `getCampaignStudents(campaignId)` 
- **Response:** Mảng students với field `hasCheckupRecord`
- **State Update:** `setCampaignStudents(studentsData)`

---

## 🔄 **3. API Service - getCampaignStudents**

### **📍 Vị trí:** `healthCheckupService.js` - Lines 624-642

<augment_code_snippet path="frontend/src/services/APINurse/healthCheckupService.js" mode="EXCERPT">
```javascript
// Get student list for a specific campaign (API cũ)
export const getCampaignStudents = async (campaignId) => {
  try {
    const token = sessionService.getToken();
    const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}/api/v1/health-campaigns/${campaignId}/students`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : ''
      }
    });
    if (!response.ok) {
      throw new Error(`Error ${response.status}: ${response.statusText}`);
    }
    return await response.json();
  } catch (error) {
    console.error(`Error fetching students for campaign ${campaignId}:`, error);
    throw error;
  }
};
```
</augment_code_snippet>

### **🎯 API Endpoint:**
- **URL:** `GET /api/v1/health-campaigns/{campaignId}/students`
- **Response:** Array of student objects với field `hasCheckupRecord`
- **Backend Logic:** Backend kiểm tra xem student đã có medical checkup record chưa

---

## 🔄 **4. Sau khi Tạo Hồ sơ Thành công**

### **📍 Vị trí:** Lines 130-157 - `handleCheckupCreated` function

<augment_code_snippet path="frontend/src/Pages/Nurse/pages/HealthCheckups_co/CheckupList/CampaignDetailPage.jsx" mode="EXCERPT">
```javascript
// Handle successful checkup creation
const handleCheckupCreated = async (formData) => {
  try {
    // Call the API to create checkup
    await addHealthCheckup(formData);

    setShowCreateModal(false);
    setStudentForCheckup(null);

    // Reload students data to reflect changes
    try {
      const studentsData = await getCampaignStudents(campaign.id);
      setCampaignStudents(studentsData);
    } catch (err) {
      console.error('Error reloading students:', err);
    }

    Swal.fire({
      icon: 'success',
      title: 'Thành công!',
      text: 'Hồ sơ khám sức khỏe đã được tạo thành công.',
      timer: 2000,
      showConfirmButton: false
    });
  } catch (error) {
    console.error('Error creating checkup:', error);
    throw error; // Re-throw để modal không đóng khi có lỗi
  }
};
```
</augment_code_snippet>

### **🎯 Logic:**
1. **Tạo hồ sơ:** `await addHealthCheckup(formData)`
2. **Đóng modal:** `setShowCreateModal(false)`
3. **Reload data:** `getCampaignStudents(campaign.id)` - **KEY STEP**
4. **Update state:** `setCampaignStudents(studentsData)`
5. **Show success:** SweetAlert notification

---

## 🔄 **5. Hiển thị trong Table**

### **📍 Vị trí:** Lines 443-457 - Table row rendering

<augment_code_snippet path="frontend/src/Pages/Nurse/pages/HealthCheckups_co/CheckupList/CampaignDetailPage.jsx" mode="EXCERPT">
```javascript
{currentStudents.map((student, index) => (
  <tr key={student.studentId}>
    <td>{startIndex + index + 1}</td>
    <td>{student.studentName}</td>
    <td>{student.studentClass}</td>
    <td>{student.parentName}</td>
    <td>{getConsentStatus(student.consentStatus)}</td>
    <td>
      <Button variant="outline-info" size="sm" className="me-2" onClick={() => handleViewConsent(student.parentConsentId)}>
        <FaInfoCircle /> Xem
      </Button>
      {renderCheckupActions(student)}
    </td>
  </tr>
))}
```
</augment_code_snippet>

### **🎯 Logic:**
- **Mỗi row:** Gọi `renderCheckupActions(student)`
- **Conditional rendering:** Dựa trên `student.hasCheckupRecord`
- **Real-time update:** Sau khi reload data, UI tự động update

---

## 🔄 **6. Backend Logic (Giả định)**

### **🎯 Backend xử lý `hasCheckupRecord`:**
```sql
-- Giả định query backend
SELECT 
  s.*,
  CASE 
    WHEN mc.id IS NOT NULL THEN true 
    ELSE false 
  END as hasCheckupRecord
FROM campaign_students cs
JOIN students s ON cs.student_id = s.id
LEFT JOIN medical_checkups mc ON mc.student_id = s.id 
  AND mc.campaign_id = cs.campaign_id
WHERE cs.campaign_id = ?
```

### **🎯 Logic:**
- **JOIN:** Students với Medical Checkups
- **LEFT JOIN:** Để lấy cả students chưa có checkup
- **CASE WHEN:** Nếu có medical_checkup record → `hasCheckupRecord = true`

---

## 📊 **Luồng hoạt động hoàn chỉnh:**

```
1. Component mount → Load campaign data
2. Call getCampaignStudents(campaignId) → API call
3. Backend query → Check medical_checkup records
4. Response → Students array với hasCheckupRecord field
5. setCampaignStudents(data) → Update state
6. renderCheckupActions(student) → Check hasCheckupRecord
7. hasCheckupRecord = true → Show "Đã khám" button (disabled)
8. User tạo hồ sơ → handleCheckupCreated()
9. addHealthCheckup() → Create record in backend
10. Reload getCampaignStudents() → Refresh data
11. hasCheckupRecord now = true → Button changes to "Đã khám"
```

---

## 🎯 **Key Points:**

1. **Field từ Backend:** `hasCheckupRecord` boolean field
2. **Conditional Rendering:** Dựa trên field này để hiển thị button
3. **Real-time Update:** Reload data sau khi tạo hồ sơ thành công
4. **Button States:** 
   - `hasCheckupRecord = true` → "Đã khám" (disabled, green)
   - `consentStatus = 'APPROVED'` → "Tạo hồ sơ khám" (active, blue)
   - Else → "Chưa đồng ý" (disabled, gray)
5. **API Endpoint:** `GET /api/v1/health-campaigns/{campaignId}/students`

---

## 🔍 **So sánh với Vaccination Module:**

| **Aspect** | **Health Checkup** | **Vaccination** |
|------------|-------------------|-----------------|
| **Status Source** | Backend field `hasCheckupRecord` | Calculated from notes |
| **Status Logic** | Simple boolean check | Complex note analysis |
| **Update Trigger** | API reload after create | Context state update |
| **Button Text** | "Đã khám" | "Đã tạo HS - Hoàn thành" |
| **Complexity** | Simple | Complex with monitoring |

Health Checkup module sử dụng approach đơn giản hơn với backend trả về trực tiếp trạng thái! 🚀
