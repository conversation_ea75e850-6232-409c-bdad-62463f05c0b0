/* Admin Report Type Selector Component */
.admin-reports-type-selector {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  margin-bottom: 24px;
}

.admin-reports-type-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.admin-reports-type-label i {
  color: #3b82f6;
  font-size: 1rem;
}

.admin-reports-type-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  max-width: 900px;
  margin: 0 auto;
}

.admin-reports-type-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 20px;
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  aspect-ratio: 1.1;
  text-align: center;
  min-height: 140px;
}

.admin-reports-type-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.1), transparent);
  transition: left 0.5s;
}

.admin-reports-type-option:hover::before {
  left: 100%;
}

/* Color Theme Styles for Admin Report Type Options */

/* Blue Theme - Vaccine Reports */
.admin-reports-type-option-blue {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-color: #bfdbfe;
}

.admin-reports-type-option-blue:hover {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.admin-reports-type-option-blue.selected {
  border-color: #1d4ed8;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1e40af;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  border-width: 3px;
}

.admin-reports-type-option-blue.selected .admin-reports-type-title,
.admin-reports-type-option-blue.selected .admin-reports-type-description {
  color: #1e40af;
  font-weight: 600;
}

.admin-reports-type-option-blue.selected .admin-reports-type-title i {
  color: #1d4ed8;
}

/* Green Theme - Medication Reports */
.admin-reports-type-option-green {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  border-color: #a7f3d0;
}

.admin-reports-type-option-green:hover {
  border-color: #10b981;
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

.admin-reports-type-option-green.selected {
  border-color: #047857;
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  color: #065f46;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  border-width: 3px;
}

.admin-reports-type-option-green.selected .admin-reports-type-title,
.admin-reports-type-option-green.selected .admin-reports-type-description {
  color: #065f46;
  font-weight: 600;
}

.admin-reports-type-option-green.selected .admin-reports-type-title i {
  color: #047857;
}

/* Purple Theme - Health Checkup Reports */
.admin-reports-type-option-purple {
  background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
  border-color: #ddd6fe;
}

.admin-reports-type-option-purple:hover {
  border-color: #8b5cf6;
  background: linear-gradient(135deg, #f3e8ff 0%, #ddd6fe 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.15);
}

.admin-reports-type-option-purple.selected {
  border-color: #6d28d9;
  background: linear-gradient(135deg, #f3e8ff 0%, #ddd6fe 100%);
  color: #581c87;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
  border-width: 3px;
}

.admin-reports-type-option-purple.selected .admin-reports-type-title,
.admin-reports-type-option-purple.selected .admin-reports-type-description {
  color: #581c87;
  font-weight: 600;
}

.admin-reports-type-option-purple.selected .admin-reports-type-title i {
  color: #6d28d9;
}

/* Orange Theme - Vaccination Reports */
.admin-reports-type-option-orange {
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
  border-color: #fde68a;
}

.admin-reports-type-option-orange:hover {
  border-color: #f59e0b;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);
}

.admin-reports-type-option-orange.selected {
  border-color: #b45309;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #92400e;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
  border-width: 3px;
}

.admin-reports-type-option-orange.selected .admin-reports-type-title,
.admin-reports-type-option-orange.selected .admin-reports-type-description {
  color: #92400e;
  font-weight: 600;
}

.admin-reports-type-option-orange.selected .admin-reports-type-title i {
  color: #b45309;
}

/* Teal Theme - Student Management */
.admin-reports-type-option-teal {
  background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
  border-color: #99f6e4;
}

.admin-reports-type-option-teal:hover {
  border-color: #14b8a6;
  background: linear-gradient(135deg, #ccfbf1 0%, #99f6e4 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(20, 184, 166, 0.15);
}

.admin-reports-type-option-teal.selected {
  border-color: #0f766e;
  background: linear-gradient(135deg, #ccfbf1 0%, #99f6e4 100%);
  color: #134e4a;
  box-shadow: 0 4px 12px rgba(20, 184, 166, 0.3);
  border-width: 3px;
}

.admin-reports-type-option-teal.selected .admin-reports-type-title,
.admin-reports-type-option-teal.selected .admin-reports-type-description {
  color: #134e4a;
  font-weight: 600;
}

.admin-reports-type-option-teal.selected .admin-reports-type-title i {
  color: #0f766e;
}

/* Default hover and selected styles (fallback) */
.admin-reports-type-option:hover {
  border-color: #3b82f6;
  background: #eff6ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.admin-reports-type-option.selected {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

/* Radio button colors for different themes */
.admin-reports-type-option-blue input[type="radio"] {
  accent-color: #3b82f6;
}

.admin-reports-type-option-green input[type="radio"] {
  accent-color: #10b981;
}

.admin-reports-type-option-purple input[type="radio"] {
  accent-color: #8b5cf6;
}

.admin-reports-type-option-orange input[type="radio"] {
  accent-color: #f59e0b;
}

.admin-reports-type-option-teal input[type="radio"] {
  accent-color: #14b8a6;
}

.admin-reports-type-option input[type="radio"] {
  margin-right: 12px;
  width: 18px;
  height: 18px;
  accent-color: #3b82f6;
  cursor: pointer;
}

.admin-reports-type-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.admin-reports-type-title {
  font-size: 1rem;
  font-weight: 700;
  color: #374151;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  text-align: center;
  line-height: 1.3;
}

/* Icon colors for different themes */
.admin-reports-type-option-blue .admin-reports-type-title i {
  color: #3b82f6;
}

.admin-reports-type-option-green .admin-reports-type-title i {
  color: #10b981;
}

.admin-reports-type-option-purple .admin-reports-type-title i {
  color: #8b5cf6;
}

.admin-reports-type-option-orange .admin-reports-type-title i {
  color: #f59e0b;
}

.admin-reports-type-option-teal .admin-reports-type-title i {
  color: #14b8a6;
}

.admin-reports-type-title i {
  color: #3b82f6;
  font-size: 1.25rem;
}

.admin-reports-type-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
  text-align: center;
  font-weight: 500;
}

/* Advanced options */
.admin-reports-advanced-options {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.admin-reports-advanced-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 12px;
  transition: color 0.2s ease;
}

.admin-reports-advanced-toggle:hover {
  color: #374151;
}

.admin-reports-advanced-toggle i {
  transition: transform 0.2s ease;
}

.admin-reports-advanced-toggle.expanded i {
  transform: rotate(90deg);
}

.admin-reports-advanced-content {
  display: none;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-top: 12px;
}

.admin-reports-advanced-content.visible {
  display: grid;
}

.admin-reports-option-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.admin-reports-option-group label {
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.admin-reports-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  background: white;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.admin-reports-checkbox-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.15s ease;
}

.admin-reports-checkbox-option:hover {
  background: #f9fafb;
}

.admin-reports-checkbox-option input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #3b82f6;
  cursor: pointer;
}

.admin-reports-checkbox-option span {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

/* Custom filters */
.admin-reports-custom-filters {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.admin-reports-filter-input {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  color: #374151;
  background: white;
  transition: all 0.2s ease;
}

.admin-reports-filter-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.admin-reports-filter-input::placeholder {
  color: #9ca3af;
}

/* Generate button */
.admin-reports-generate-section {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.admin-reports-generate-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 10px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  overflow: hidden;
}

.admin-reports-generate-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.admin-reports-generate-btn:hover::before {
  left: 100%;
}

.admin-reports-generate-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.admin-reports-generate-btn:active {
  transform: translateY(0);
}

.admin-reports-generate-btn:disabled {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.admin-reports-generate-btn.loading {
  cursor: wait;
}

.admin-reports-generate-btn.loading i {
  animation: admin-spin 1s linear infinite;
}

@keyframes admin-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 1024px) {
  .admin-reports-type-options {
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    max-width: 800px;
  }
}

@media (max-width: 768px) {
  .admin-reports-type-selector {
    padding: 16px;
  }

  .admin-reports-type-options {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    max-width: 600px;
  }

  .admin-reports-type-option {
    padding: 16px 12px;
  }

  .admin-reports-advanced-content {
    grid-template-columns: 1fr;
  }

  .admin-reports-generate-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 600px) {
  .admin-reports-type-options {
    grid-template-columns: 1fr;
    gap: 16px;
    max-width: 400px;
  }

  .admin-reports-type-option {
    padding: 20px 16px;
    aspect-ratio: auto;
    min-height: 120px;
  }

  .admin-reports-type-title {
    font-size: 0.875rem;
  }

  .admin-reports-type-description {
    font-size: 0.75rem;
  }

  .admin-reports-generate-btn {
    padding: 12px 20px;
  }
}
