/* Modern Hero Section - Professional Blue Theme for Parent Pages */

.hero.modern-hero,
.parent-hero {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 25%, #f1f5f9 50%, #e2e8f0 75%, #f8fafc 100%);
  min-height: 55vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  padding: 1.5rem 0;
  margin: 0;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(80px);
  opacity: 0.3;
  animation: float 6s ease-in-out infinite;
}

.orb-1 {
  width: 400px;
  height: 400px;
  background: linear-gradient(135deg, #88CDF6, #428CD4);
  top: 20%;
  right: 10%;
  animation-delay: 0s;
}

.orb-2 {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, #2D82B5, #BCE6FF);
  bottom: 30%;
  left: 5%;
  animation-delay: 2s;
}

.orb-3 {
  width: 250px;
  height: 250px;
  background: linear-gradient(135deg, #428CD4, #015C92);
  top: 60%;
  right: 30%;
  animation-delay: 4s;
}

.hero-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
  width: 100%;
  box-sizing: border-box;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: center;
  min-height: auto;
  padding: 1rem 0;
  width: 100%;
  box-sizing: border-box;
}

/* Left side - Text content */
.hero-text {
  color: #1f2937;
  animation: fadeInUp 0.8s ease-out forwards;
  text-align: center;
  width: 100%;
  box-sizing: border-box;
}

.hero-badge {
  display: inline-block;
  background: rgba(1, 92, 146, 0.1);
  color: #015C92;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(1, 92, 146, 0.2);
  animation: slideInLeft 0.8s ease-out 0.2s backwards;
}

.hero-title {
  font-size: 2.8rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  animation: slideInLeft 0.8s ease-out 0.4s backwards;
  text-align: center;
}

.gradient-text {
  background: linear-gradient(135deg, #FFD700, #FFA500, #FF8C00);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;

  display: inline-block; /* cần để margin-top hoạt động với inline text */
  margin-top: 15px;
}


.hero-description {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 1.2rem;
  color: #4b5563;
  max-width: 90%;
  animation: slideInLeft 0.8s ease-out 0.6s backwards;
  text-align: center;
  margin-left: auto;
  margin-right: auto;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.8rem;
  animation: slideInLeft 0.8s ease-out 0.8s backwards;
  justify-content: center;
}

.hero-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  text-decoration: none;
}

.primary-btn {
  background: linear-gradient(135deg, #015C92, #2D82B5);
  color: white;
  box-shadow: 0 4px 20px rgba(1, 92, 146, 0.25);
}

.primary-btn:hover {
  background: linear-gradient(135deg, #2D82B5, #428CD4);
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(1, 92, 146, 0.35);
  color: white;
}

.secondary-btn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.secondary-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.hero-stats {
  display: flex;
  gap: 1.5rem;
  animation: slideInLeft 0.8s ease-out 1s backwards;
  justify-content: center;
  flex-wrap: wrap; /* Allow wrapping on smaller screens */
}

.stat-item {
  text-align: left;
  position: relative;
  background: rgba(1, 92, 146, 0.05);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(1, 92, 146, 0.1);
  transition: all 0.3s ease;
  min-width: 110px;
  box-sizing: border-box;
  display: block;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-item:hover {
  background: rgba(1, 92, 146, 0.1);
  transform: translateY(-2px);
}

.stat-item:nth-child(1) .stat-number {
  color: #FFD700; /* Vàng cho học sinh */
}

.stat-item:nth-child(2) .stat-number {
  color: #FF6B7C; /* Hồng cho trường học */
}

.stat-item:nth-child(3) .stat-number {
  color: #34D399; /* Xanh lá cho độ tin cậy */
}

.stat-number {
  font-size: 1.8rem;
  font-weight: 800;
  line-height: 1;
  margin-bottom: 0.3rem;
  display: block;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: inherit;
}

.stat-label {
  font-size: 0.9rem;
  color: #6b7280;
  margin-top: 0.25rem;
  font-weight: 500;
}

/* Right side - Phone mockup with enhanced animations */
.hero-visual {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeInRight 0.8s ease-out 0.5s backwards;
  width: 100%;
  box-sizing: border-box;
}

.phone-mockup {
  position: relative;
  z-index: 3;
  width: 100%;
  max-width: 260px;
  margin: 0 auto;
  /* Add floating animation */
  animation: phoneFloat 4s ease-in-out infinite;
}

.phone-frame {
  width: 100%;
  max-width: 260px;
  height: 540px;
  background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
  border-radius: 35px;
  padding: 8px;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 15px 20px rgba(0, 0, 0, 0.2),
    0 5px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  margin: 0 auto;
  box-sizing: border-box;
  /* Add pulse animation */
  animation: phonePulse 3s ease-in-out infinite;
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: #0A2540;
  border-radius: 30px;
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  /* Add screen glow effect */
  box-shadow: inset 0 0 20px rgba(136, 205, 246, 0.2);
}

.phone-header {
  height: 30px;
  padding: 0 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 0, 0, 0.3);
}

.phone-time {
  color: white;
  font-size: 12px;
  font-weight: 500;
}

.phone-status {
  display: flex;
  align-items: center;
  gap: 5px;
}

.signal, .wifi, .battery {
  width: 15px;
  height: 15px;
  background: white;
  opacity: 0.8;
  border-radius: 2px;
}

.battery {
  width: 20px;
  height: 10px;
  border-radius: 2px;
  border: 1px solid white;
  position: relative;
  background: transparent;
}

.battery::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 14px;
  height: 6px;
  background: white;
  border-radius: 1px;
}

.app-content {
  padding: 15px;
  height: calc(100% - 30px);
  display: flex;
  flex-direction: column;
  gap: 15px;
  box-sizing: border-box;
  /* Add subtle content animation */
  animation: contentSlideIn 1s ease-out 1.2s backwards;
}

.welcome-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 5px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #88CDF6;
  box-shadow: 0 0 10px rgba(136, 205, 246, 0.5);
  /* Add avatar pulse */
  animation: avatarPulse 2s ease-in-out infinite;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.welcome-text {
  flex: 1;
  padding-left: 10px;
}

.welcome-label {
  display: block;
  font-size: 10px;
  color: #88CDF6;
  margin-bottom: 2px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.user-name {
  color: white;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.3px;
}

.notification-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  /* Add notification bounce */
  animation: notificationBounce 3s ease-in-out infinite;
}

.balance-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px;
  position: relative;
}

.balance-card {
  background: linear-gradient(135deg, rgba(1, 92, 146, 0.8), rgba(45, 130, 181, 0.8));
  border-radius: 20px;
  padding: 20px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  /* Add card hover effect */
  transition: transform 0.3s ease;
}

.balance-card:hover {
  transform: translateY(-2px);
}

.balance-icon {
  position: absolute;
  top: 15px;
  left: 15px;
  width: 30px;
  height: 30px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 16px;
}

.balance-amount {
  color: white;
  font-size: 17px;
  font-weight: 700;
  /* padding: auto; */
  padding-left: 30px;
  text-align: center;
}

.balance-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  text-align: center;
  margin-top: 5px;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 10px;
}

.action-btn {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 12px 5px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 5px;
  color: white;
  font-size: 10px;
  transition: all 0.3s ease;
  cursor: pointer;
  /* Add action button animation */
  animation: actionButtonSlide 0.5s ease-out backwards;
}

.action-btn:nth-child(1) { animation-delay: 1.4s; }
.action-btn:nth-child(2) { animation-delay: 1.5s; }
.action-btn:nth-child(3) { animation-delay: 1.6s; }
.action-btn:nth-child(4) { animation-delay: 1.7s; }

.action-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.action-btn i {
  font-size: 16px;
  color: #88CDF6;
  margin-bottom: 3px;
}

.recent-section {
  margin-top: 5px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 10px;
}

.see-all {
  color: #88CDF6;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
}

.recent-contacts {
  display: flex;
  gap: 15px;
  overflow-x: auto;
  padding-bottom: 5px;
  scrollbar-width: none;
}

.contact-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  color: white;
  font-size: 9px;
  min-width: 50px;
  text-align: center;
  /* Add contact item animation */
  animation: contactSlideUp 0.4s ease-out backwards;
}

.contact-item:nth-child(1) { animation-delay: 1.8s; }
.contact-item:nth-child(2) { animation-delay: 1.9s; }
.contact-item:nth-child(3) { animation-delay: 2.0s; }
.contact-item:nth-child(4) { animation-delay: 2.1s; }
.contact-item:nth-child(5) { animation-delay: 2.2s; }

.contact-item img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.transaction-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 15px;
  /* Add transaction card animation */
  animation: transactionSlideIn 0.6s ease-out 2.3s backwards;
}

.transaction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 10px;
}

.transaction-amount {
  color: #34D399;
  font-weight: 700;
}

.transaction-detail {
  display: flex;
  align-items: center;
  gap: 10px;
}

.transaction-detail img {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  object-fit: cover;
}

.transaction-name {
  color: white;
  font-size: 11px;
  flex: 1;
}

.transaction-growth {
  color: #34D399;
  font-size: 10px;
  font-weight: 700;
}

/* Enhanced Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(1deg);
  }
  66% {
    transform: translateY(10px) rotate(-1deg);
  }
}

/* New Phone Animations */
@keyframes phoneFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes phonePulse {
  0%, 100% {
    box-shadow: 
      0 20px 40px rgba(0, 0, 0, 0.3),
      0 15px 20px rgba(0, 0, 0, 0.2),
      0 5px 10px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 
      0 25px 50px rgba(0, 0, 0, 0.4),
      0 20px 30px rgba(0, 0, 0, 0.3),
      0 10px 15px rgba(0, 0, 0, 0.2);
  }
}

@keyframes contentSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes avatarPulse {
  0%, 100% {
    box-shadow: 0 0 10px rgba(136, 205, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(136, 205, 246, 0.8);
  }
}

@keyframes notificationBounce {
  0%, 100% {
    transform: scale(1);
  }
  10% {
    transform: scale(1.1);
  }
  20% {
    transform: scale(1);
  }
}

@keyframes actionButtonSlide {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes contactSlideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes transactionSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@media (max-width: 1200px) {
  .hero-content {
    gap: 1.5rem;
  }
  
  .phone-frame {
    max-width: 240px;
    height: 500px;
  }
  
  .hero-title {
    font-size: 2.4rem;
  }
}

@media (max-width: 992px) {
  .hero.modern-hero,
  .parent-hero {
    min-height: 70vh;
    padding: 2rem 0;
  }
  
  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .hero-title {
    font-size: 2.2rem;
  }
  
  .hero-description {
    max-width: 100%;
  }
  
  .hero-stats {
    justify-content: center;
  }
  
  .phone-frame {
    max-width: 220px;
    height: 460px;
  }
}

@media (max-width: 768px) {
  .hero-container {
    padding: 0 1rem;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-description {
    font-size: 1rem;
  }
  
  .phone-frame {
    max-width: 200px;
    height: 420px;
  }
  
  .app-content {
    padding: 12px;
  }
  
  .hero-stats {
    gap: 1rem;
  }
  
  .stat-number {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 1.8rem;
  }
  
  .hero-description {
    font-size: 0.95rem;
  }
  
  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .hero-btn {
    width: 100%;
    max-width: 280px;
  }
  
  .phone-frame {
    max-width: 180px;
    height: 380px;
  }
  
  .app-content {
    padding: 10px;
  }
  
  .balance-card {
    padding: 15px;
  }
}
