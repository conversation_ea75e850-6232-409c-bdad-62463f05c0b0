/* ViewDetails.css - Custom Modal Styles for ViewDetailsItem component - NO BOOTSTRAP DEPENDENCY */

/* Modal Overlay */
.vtu-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1050;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.vtu-modal-overlay.vtu-modal-show {
  opacity: 1;
  visibility: visible;
}

/* Modal Dialog */
.vtu-modal-dialog {
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  margin: 1rem;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.vtu-modal-show .vtu-modal-dialog {
  transform: scale(1);
}

/* Modal Content Container */
.vtu-modal-content {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: 90vh;
}

/* Modal Header */
.vtu-modal-header {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: none;
}

.vtu-modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.vtu-btn-close {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease;
}

.vtu-btn-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Modal Body */
.vtu-modal-body {
  padding: 1.5rem;
  background-color: #f8f9fa;
  flex: 1;
  overflow-y: auto;
}

/* Loading Spinner */
.vtu-loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem 0;
  gap: 1rem;
}

.vtu-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: vtu-spin 1s linear infinite;
}

@keyframes vtu-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.vtu-loading-text {
  color: #6c757d;
  font-size: 0.95rem;
}

/* Alert */
.vtu-alert {
  padding: 0.75rem 1rem;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.vtu-alert-danger {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

/* Content Grid */
.vtu-content-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Info Section */
.vtu-info-section {
  width: 100%;
}

/* Info Card */
.vtu-info-card {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.vtu-info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
}

/* Card Header */
.vtu-card-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 2px solid #007bff;
  padding: 0.75rem 1rem;
}

.vtu-card-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #007bff;
  display: flex;
  align-items: center;
}

/* Card Body */
.vtu-card-body {
  padding: 1rem;
}

/* Row and Columns */
.vtu-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.vtu-col-4 {
  flex: 0 0 calc(33.333% - 0.667rem);
  min-width: 200px;
}

.vtu-col-6 {
  flex: 0 0 calc(50% - 0.5rem);
  min-width: 250px;
}

@media (max-width: 768px) {
  .vtu-col-4,
  .vtu-col-6 {
    flex: 1 1 100%;
    min-width: unset;
  }
}

/* Field Container */
.vtu-field-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.vtu-field-label {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: #495057;
}

.vtu-field-value {
  padding-left: 1.5rem;
}

.vtu-field-text {
  font-size: 0.95rem;
  font-weight: 500;
  color: #212529;
}

/* Badges */
.vtu-badge {
  display: inline-block;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  text-align: center;
  white-space: nowrap;
}

.vtu-badge-primary {
  background-color: #007bff;
  color: white;
}

/* Type Badges */
.vtu-type-badge {
  display: inline-block;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  text-align: center;
  white-space: nowrap;
}

.vtu-type-primary {
  background-color: #007bff;
  color: white;
}

.vtu-type-secondary {
  background-color: #6c757d;
  color: white;
}

.vtu-type-info {
  background-color: #17a2b8;
  color: white;
}

/* Status Badges */
.vtu-status-badge {
  display: inline-block;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  text-align: center;
  white-space: nowrap;
}

.vtu-status-success {
  background-color: #28a745;
  color: white;
}

.vtu-status-warning {
  background-color: #ffc107;
  color: #212529;
}

.vtu-status-danger {
  background-color: #dc3545;
  color: white;
}

/* Quantity Display */
.vtu-quantity-text {
  font-size: 1.5rem;
  font-weight: bold;
  color: #007bff;
  margin-right: 0.5rem;
}

.vtu-unit-text {
  color: #6c757d;
  font-size: 0.95rem;
}

/* Description */
.vtu-description-text {
  margin: 0;
  color: #6c757d;
  line-height: 1.5;
}

/* Modal Footer */
.vtu-modal-footer {
  background-color: #f8f9fa;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

/* Buttons */
.vtu-btn {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.5;
  border-radius: 0.375rem;
  border: 1px solid transparent;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.2s ease;
}

.vtu-btn-secondary {
  background-color: #6c757d;
  border-color: #6c757d;
  color: white;
}

.vtu-btn-secondary:hover {
  background-color: #5a6268;
  border-color: #545b62;
}

/* Responsive Design */
@media (max-width: 576px) {
  .vtu-modal-dialog {
    width: 95%;
    margin: 0.5rem;
  }
  
  .vtu-modal-header {
    padding: 0.75rem 1rem;
  }
  
  .vtu-modal-body {
    padding: 1rem;
  }
  
  .vtu-modal-title {
    font-size: 1.1rem;
  }
}

/* Utility Classes */
.me-1 { margin-right: 0.25rem; }
.me-2 { margin-right: 0.5rem; }
.text-muted { color: #6c757d; }

.view-details-modal .text-primary {
  color: #007bff !important;
}

.view-details-modal .modal-footer {
  background-color: #f8f9fa !important;
  border-top: 1px solid #e9ecef !important;
}

.view-details-modal .btn-secondary {
  background-color: #6c757d !important;
  border-color: #6c757d !important;
  color: white !important;
}

.view-details-modal .btn-secondary:hover {
  background-color: #5a6268 !important;
  border-color: #545b62 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .view-details-modal .modal-dialog {
    margin: 0.5rem !important;
  }
  
  .view-details-modal .card-body {
    padding: 1rem !important;
  }
  
  .view-details-modal .row.g-3 {
    --bs-gutter-x: 0.5rem !important;
    --bs-gutter-y: 0.5rem !important;
  }
}

/* Animation for modal */
.view-details-modal.fade .modal-dialog {
  transition: transform 0.3s ease-out !important;
  transform: translate(0, -50px) !important;
}

.view-details-modal.show .modal-dialog {
  transform: none !important;
}

/* Custom spinner */
.view-details-modal .spinner-border {
  width: 2rem !important;
  height: 2rem !important;
}

.view-details-modal .alert {
  border: none !important;
  border-radius: 0.75rem !important;
}
