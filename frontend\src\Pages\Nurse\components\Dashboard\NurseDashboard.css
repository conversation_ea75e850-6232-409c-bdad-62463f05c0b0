/* Component specific styles for Nurse<PERSON>ashboard */
.nurse-dashboard {
  width: 100%;
  padding: 0;
  min-height: 100%;
  display: flex;
  flex-direction: column;
}

/* Page Title Container */
.page-title-container {
  background-color: #f8f9fa;
  padding: 15px 20px;
  border-bottom: 1px solid #ddd;
  margin-bottom: 20px;
}

.page-title-container h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #2c3e50;
}

/* Content Container */
.content-container {
  padding: 0 20px 20px 20px;
  flex: 1;
}

/* Dashboard Content */
.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Welcome Section */
.nurse-welcome {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

.nurse-welcome h2 {
  margin-top: 0;
  color: #2c3e50;
  font-size: 1.8rem;
}

/* Overview Cards */
.nurse-overview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.overview-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s, box-shadow 0.3s;
}

.overview-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.overview-icon {
  background-color: #f0f7ff;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-right: 15px;
}

.overview-card.today-visits .overview-icon {
  color: #3498db;
  background-color: #ebf5ff;
}

.overview-card.pending .overview-icon {
  color: #f39c12;
  background-color: #fff8e6;
}

.overview-card.reports .overview-icon {
  color: #2ecc71;
  background-color: #eafaf1;
}

.overview-card.alerts .overview-icon {
  color: #e74c3c;
  background-color: #feeaea;
}

.overview-info h3 {
  font-size: 1.8rem;
  margin: 0 0 5px 0;
  color: #2c3e50;
}

.overview-info p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* Nurse Sections */
.nurse-sections {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
}

@media (max-width: 992px) {
  .nurse-sections {
    grid-template-columns: 1fr;
  }
}

.nurse-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.nurse-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #2c3e50;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

/* Student List Table */
.student-list {
  overflow-x: auto;
}

.student-list table {
  width: 100%;
  border-collapse: collapse;
}

.student-list th,
.student-list td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.student-list thead th {
  background-color: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
}

.student-list tbody tr:hover {
  background-color: #f8f9fa;
}

.status {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 0.85rem;
}

.status.waiting {
  background-color: #fff8e6;
  color: #f39c12;
}

.status.in-progress {
  background-color: #ebf5ff;
  color: #3498db;
}

/* Notification List */
.notification-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.notification-list li {
  padding: 15px 0;
  border-bottom: 1px solid #eee;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.notification-list li:last-child {
  border-bottom: none;
}

.notification-time {
  color: #7f8c8d;
  font-size: 0.85rem;
}

.notification-content {
  color: #333;
  line-height: 1.5;
}

.notification-content strong {
  color: #2c3e50;
}

/* Placeholder Content */
.placeholder-content {
  text-align: center;
  padding: 50px 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.placeholder-content h3 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.placeholder-content p {
  color: #7f8c8d;
}
