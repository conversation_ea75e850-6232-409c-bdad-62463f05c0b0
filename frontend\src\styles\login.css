/* <PERSON><PERSON><PERSON><PERSON> lập chung */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Roboto', 'Segoe UI', sans-serif;
  background-color: #f5f8fa;
  color: #333;
}

/* B<PERSON> cục chính */
.login-container {
  display: flex;
  min-height: 100vh;
  width: 100%;
}

/* Phần banner trái */
.login-banner {
  display: block !important;
  flex: 1;
  position: relative;
  background-color: #0c4ca3;
  overflow: hidden;
  transition: all 0.5s ease;
  box-shadow: inset 0 0 100px rgba(0, 0, 0, 0.5);
}

.login-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(12, 76, 163, 0.2) 0%, rgba(3, 37, 88, 0.6) 100%);
  z-index: 2;
  opacity: 0.8;
  animation: pulse 8s infinite alternate;
}

@keyframes pulse {
  0% {
    opacity: 0.7;
  }
  100% {
    opacity: 0.9;
  }
}

.login-banner-image {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
  transition: all 10s ease;
  transform: scale(1);
  animation: slowZoom 30s infinite alternate;
}

@keyframes slowZoom {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.1);
  }
}

.login-banner-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 2rem;
  color: white;
  z-index: 3;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(1px);
}

.login-banner-content h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
  animation: fadeInUp 1.2s ease;
  transform: translateY(0);
  letter-spacing: 1px;
  color: #fff;
}

.login-banner-content h2 {
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 2px;
  animation: fadeInUp 1s ease;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
  color: #fff;
}

.login-banner-content p {
  font-size: 1.1rem;
  max-width: 80%;
  line-height: 1.6;
  animation: fadeInUp 1.4s ease;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.4);
  letter-spacing: 0.5px;
  color: #fff;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Phần form đăng nhập bên phải */
.login-form-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: #ffffff;
}

/* Cải thiện thêm các hiệu ứng nhỏ khác */
.login-form-wrapper {
  width: 100%;
  max-width: 420px;
  animation: fadeIn 0.8s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Header của form */
.login-form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-form-header h2 {
  font-size: 2rem;
  color: #154082;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.login-form-header p {
  color: #6c757d;
  font-size: 1rem;
}

/* Form đăng nhập */
.login-form {
  margin-bottom: 1rem;
}

/* Tài khoản thử nghiệm */
.test-accounts {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: #e8f4ff;
  border-left: 4px solid #154082;
  border-radius: 4px;
  font-size: 0.85rem;
}

.test-accounts p {
  margin-top: 0.5rem;
  margin-bottom: 0;
}

/* Thông báo lỗi - Login Error Styles */
.loginErrorMessage {
  display: flex;
  align-items: center;
  background-color: #fef2f2;
  color: #dc2626;
  padding: 0.875rem 1rem;
  border: 1px solid #fecaca;
  border-left: 4px solid #dc2626;
  margin-bottom: 1.5rem;
  border-radius: 6px;
  font-size: 0.9rem;
  box-shadow: 0 1px 3px rgba(220, 38, 38, 0.1);
  animation: loginErrorSlideIn 0.3s ease-out;
}

.loginErrorIcon {
  margin-right: 0.75rem;
  font-size: 1rem;
  color: #dc2626;
  flex-shrink: 0;
}

.loginErrorText {
  flex: 1;
  line-height: 1.4;
  font-weight: 500;
}

@keyframes loginErrorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover effect for better UX */
.loginErrorMessage:hover {
  background-color: #fef1f1;
  border-color: #f9a8a8;
}

/* Legacy error-alert class (keep for backward compatibility) */
.error-alert {
  background-color: #fef0f0;
  color: #e53e3e;
  padding: 0.8rem 1rem;
  border-left: 4px solid #e53e3e;
  margin-bottom: 1.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
}

/* Input groups */
.input-group {
  margin-bottom: 1.5rem;
}

.input-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  color: #495057;
}

.input-control {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.input-control i {
  position: absolute;
  left: 14px;
  color: #6B7280;
  font-size: 18px;
  z-index: 10;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.input-control input {
  padding-left: 44px !important;
  width: 100%;
  height: 44px;
  border: 1.5px solid #D1D5DB;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s, box-shadow 0.2s;
  background-color: #F9FAFB;
  box-sizing: border-box;
  display: block;
}

.input-control input:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.10);
}

.input-control input:not(:placeholder-shown):valid {
  border-color: #28a745;
  background-color: #f8fff8;
  box-shadow: 0 0 0 1.5px #28a74533;
}

.input-control input:not(:placeholder-shown):invalid {
  border-color: #dc3545;
  box-shadow: 0 0 0 1.5px #dc354533;
}

/* Form actions (remember me & forgot password) */
.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
}

/* Điều chỉnh phần remember-me */
.remember-me {
  display: flex;
  align-items: center;
  margin-right: auto; /* Đảm bảo luôn nằm bên trái */
  white-space: nowrap;
  user-select: none;
}

.remember-me input[type="checkbox"] {
  margin-right: 0.5rem;
  cursor: pointer;
  width: 16px;
  height: 16px;
  min-width: 16px;
  min-height: 16px;
  vertical-align: middle;
}

.remember-me span {
  vertical-align: middle;
}

/* Điều chỉnh forgot-password */
.forgot-password {
  color: #154082;
  text-decoration: none;
  font-weight: 500;
  margin-left: 1rem; /* Tạo khoảng cách với phần remember-me */
  white-space: nowrap;
}

/* Thay đổi nút primary-button để thêm hiệu ứng hover */
.primary-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 0.75rem;
  background: linear-gradient(to right, #154082, #1976d2);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 5px rgba(21, 64, 130, 0.2);
}

.primary-button:hover {
  background: linear-gradient(to right, #0d2c5b, #1565c0);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(21, 64, 130, 0.3);
}

.primary-button:active {
  transform: translateY(1px);
  box-shadow: 0 1px 3px rgba(21, 64, 130, 0.2);
}

.primary-button:disabled {
  background: linear-gradient(to right, #a0aec0, #cbd5e0);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.primary-button i {
  margin-right: 0.5rem;
}

/* Divider */
.divider {
  position: relative;
  text-align: center;
  margin: 1.5rem 0;
}

.divider::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #e2e8f0;
}

.divider span {
  position: relative;
  background-color: white;
  padding: 0 1rem;
  color: #718096;
  font-size: 0.9rem;
}

/* Hiệu ứng cho nút Google */
.social-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 0.75rem;
  background-color: white;
  color: #333;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.social-button:hover {
  background-color: #f8f9fa;
  border-color: #cfd8e3;
  transform: translateY(-1px);
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);
}

.social-button img {
  width: 20px;
  height: 20px;
  margin-right: 0.75rem;
}

/* Footer */
.login-footer {
  margin-top: 2rem;
  text-align: center;
  color: #6c757d;
  font-size: 0.875rem;
}

.login-footer a {
  color: #154082;
  text-decoration: none;
  font-weight: 500;
}

.login-footer a:hover {
  text-decoration: underline;
}

/* Responsive */
@media (min-width: 768px) {
  .login-banner {
    display: block;
    flex: 1;
  }
  
  .login-form-section {
    flex: 1;
  }
}

@media (min-width: 992px) {
  .login-banner {
    flex: 1.5;
  }
  
  .login-form-section {
    flex: 1;
  }
  
  .login-banner-content h1 {
    font-size: 2.75rem;
  }
  
  .login-banner-content h2 {
    font-size: 1.5rem;
  }
}

@media (min-width: 1200px) {
  .login-banner {
    flex: 2;
  }
  
  .login-form-section {
    flex: 1;
  }
}

/* Thêm vào phần cuối file CSS */

/* Animate.css inspired animations */
.animate .login-banner-content h1 {
  animation: fadeInDown 1.2s ease;
}

.animate .login-banner-content h2 {
  animation: fadeInDown 1s ease;
}

.animate .login-banner-content p {
  animation: fadeInDown 1.4s ease;
}

.animate .login-form-wrapper {
  animation: fadeInRight 0.8s ease;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Cải thiện trải nghiệm form */
.input-control input:not(:placeholder-shown):valid {
  border-color: #28a745;
  background-color: #f8fff8;
}

.input-control input:not(:placeholder-shown):invalid {
  border-color: #dc3545;
}

/* Đảm bảo font awesome hoạt động đúng */
.fas {
  font-family: "Font Awesome 5 Free" !important;
  font-weight: 900 !important;
}