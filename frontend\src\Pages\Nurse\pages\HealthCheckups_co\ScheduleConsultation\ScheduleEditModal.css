/* ===== MODAL CONTAINER ===== */
.schedule-edit-checkup-modal .modal-dialog {
  max-width: 1200px;
  margin: 1rem auto;
}

.schedule-edit-checkup-modal .modal-content {
  border-radius: 20px;
  border: none;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

/* ===== MODAL HEADER ===== */
.schedule-edit-checkup-modal .schedule-edit-modal-header {
  background: linear-gradient(135deg, #015C92 0%, #2D82B5 100%);
  color: white;
  padding: 24px 32px;
  border-bottom: none;
  position: relative;
}

.schedule-edit-checkup-modal .schedule-edit-modal-header::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #10b981, #3b82f6, #8b5cf6, #f59e0b);
}

.schedule-edit-checkup-modal .schedule-edit-modal-title {
  font-weight: 700;
  font-size: 24px;
  color: white;
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
}

.schedule-edit-checkup-modal .schedule-edit-modal-title::before {
  content: '✏️';
  font-size: 28px;
}

.schedule-edit-checkup-modal .btn-close {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  opacity: 1;
  transition: all 0.3s ease;
}

.schedule-edit-checkup-modal .btn-close:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: rotate(90deg);
}

/* ===== MODAL BODY ===== */
.schedule-edit-checkup-modal .schedule-edit-modal-body {
  padding: 32px;
  max-height: 75vh;
  overflow-y: auto;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.schedule-edit-checkup-modal .schedule-edit-modal-body::-webkit-scrollbar {
  width: 8px;
}

.schedule-edit-checkup-modal .schedule-edit-modal-body::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.schedule-edit-checkup-modal .schedule-edit-modal-body::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #015C92, #2D82B5);
  border-radius: 4px;
}

.schedule-edit-checkup-modal .schedule-edit-modal-body::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #014a7a, #2570a0);
}

/* ===== FORM SECTIONS ===== */
.schedule-edit-checkup-modal .schedule-form-section {
  background: white;
  padding: 28px;
  border-radius: 16px;
  margin-bottom: 24px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.schedule-edit-checkup-modal .schedule-form-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #10b981, #3b82f6, #8b5cf6);
}

.schedule-edit-checkup-modal .schedule-form-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.schedule-edit-checkup-modal .schedule-form-section h5 {
  font-weight: 700;
  font-size: 20px;
  color: #1e293b;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Icons for each section */
.schedule-edit-checkup-modal .schedule-form-section:nth-child(1) h5::before {
  content: '👤';
  font-size: 24px;
}

.schedule-edit-checkup-modal .schedule-form-section:nth-child(2) h5::before {
  content: '📋';
  font-size: 24px;
}

.schedule-edit-checkup-modal .schedule-form-section:nth-child(3) h5::before {
  content: '📊';
  font-size: 24px;
}

.schedule-edit-checkup-modal .schedule-form-section:nth-child(4) h5::before {
  content: '🩺';
  font-size: 24px;
}

.schedule-edit-checkup-modal .schedule-form-section:nth-child(5) h5::before {
  content: '📝';
  font-size: 24px;
}

/* ===== FORM CONTROLS ===== */
.schedule-edit-checkup-modal .form-label {
  font-weight: 600;
  font-size: 14px;
  color: #374151;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.schedule-edit-checkup-modal .form-control,
.schedule-edit-checkup-modal .form-select {
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 14px;
  transition: all 0.3s ease;
  background-color: #ffffff;
}

.schedule-edit-checkup-modal .form-control:focus,
.schedule-edit-checkup-modal .form-select:focus {
  border-color: #015C92;
  box-shadow: 0 0 0 3px rgba(1, 92, 146, 0.1);
  outline: none;
  transform: translateY(-1px);
}

.schedule-edit-checkup-modal .form-control::placeholder {
  color: #9ca3af;
  font-style: italic;
}

.schedule-edit-checkup-modal .form-control[readonly],
.schedule-edit-checkup-modal .form-control:disabled {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-color: #cbd5e1;
  cursor: not-allowed;
  color: #64748b;
}

.schedule-edit-checkup-modal .form-control.is-invalid {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.schedule-edit-checkup-modal .invalid-feedback {
  font-size: 12px;
  font-weight: 500;
  color: #ef4444;
  margin-top: 6px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* ===== MODAL FOOTER ===== */
.schedule-edit-checkup-modal .modal-footer {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-top: 1px solid #e2e8f0;
  padding: 24px 32px;
  gap: 12px;
}

.schedule-edit-checkup-modal .modal-footer .btn {
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  border: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  justify-content: center;
}

.schedule-edit-checkup-modal .modal-footer .btn-secondary {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: white;
}

.schedule-edit-checkup-modal .modal-footer .btn-secondary:hover {
  background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

.schedule-edit-checkup-modal .modal-footer .btn-primary {
  background: linear-gradient(135deg, #015C92 0%, #2D82B5 100%);
  color: white;
}

.schedule-edit-checkup-modal .modal-footer .btn-primary:hover {
  background: linear-gradient(135deg, #014a7a 0%, #2570a0 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(1, 92, 146, 0.3);
}

.schedule-edit-checkup-modal .modal-footer .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* ===== LOADING SPINNER ===== */
.schedule-edit-checkup-modal .spinner-border-sm {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .schedule-edit-checkup-modal .modal-dialog {
    max-width: 95%;
    margin: 0.5rem auto;
  }
}

@media (max-width: 768px) {
  .schedule-edit-checkup-modal .schedule-edit-modal-header,
  .schedule-edit-checkup-modal .schedule-edit-modal-body,
  .schedule-edit-checkup-modal .modal-footer {
    padding: 20px;
  }
  
  .schedule-edit-checkup-modal .schedule-form-section {
    padding: 20px;
  }
  
  .schedule-edit-checkup-modal .schedule-edit-modal-title {
    font-size: 20px;
  }
  
  .schedule-edit-checkup-modal .schedule-form-section h5 {
    font-size: 18px;
  }
}

/* ===== ANIMATIONS ===== */
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.schedule-edit-checkup-modal .modal-content {
  animation: slideInDown 0.4s ease-out;
}

.schedule-edit-checkup-modal .schedule-form-section {
  animation: fadeInUp 0.5s ease-out;
}

.schedule-edit-checkup-modal .schedule-form-section:nth-child(1) { animation-delay: 0.1s; }
.schedule-edit-checkup-modal .schedule-form-section:nth-child(2) { animation-delay: 0.2s; }
.schedule-edit-checkup-modal .schedule-form-section:nth-child(3) { animation-delay: 0.3s; }
.schedule-edit-checkup-modal .schedule-form-section:nth-child(4) { animation-delay: 0.4s; }
.schedule-edit-checkup-modal .schedule-form-section:nth-child(5) { animation-delay: 0.5s; }
