/* Email Management styling with school medical theme */

.email-management {
  padding: 30px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 70px);
}

.email-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 25px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(26, 123, 181, 0.1);
}

.header-title h1 {
  color: #1a7bb5;
  font-size: 1.8rem;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-title p {
  color: #666;
  margin: 0;
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
}

.btn-primary {
  background: linear-gradient(135deg, #1a7bb5, #50c9c3);
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0a5d8f, #21d4fd);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(26, 123, 181, 0.3);
}

.btn-secondary {
  background: #f8f9fa;
  color: #1a7bb5;
  border: 1px solid #dee2e6;
}

.btn-secondary:hover {
  background: #e9ecef;
  border-color: #1a7bb5;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Error message */
.error-message {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  padding: 15px 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 3px 10px rgba(255, 107, 107, 0.3);
}

.error-message span {
  display: flex;
  align-items: center;
  gap: 10px;
}

.error-message button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: background 0.3s ease;
}

.error-message button:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Success message */
.success-message {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.25);
  animation: slideInDown 0.3s ease-out;
  font-size: 0.9rem;
}

.success-message span {
  display: flex;
  align-items: center;
  gap: 8px;
}

.success-message button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
  font-size: 0.8rem;
}

.success-message button:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Success message compact - displayed above table */
.success-message-compact {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 6px rgba(40, 167, 69, 0.2);
  animation: slideInDown 0.3s ease-out;
  font-size: 0.85rem;
  max-width: fit-content;
}

.success-message-compact span {
  display: flex;
  align-items: center;
  gap: 6px;
}

.success-message-compact button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 3px;
  transition: background-color 0.2s;
  font-size: 0.7rem;
  margin-left: 8px;
}

.success-message-compact button:hover {
  background: rgba(255, 255, 255, 0.2);
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Statistics */
.email-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  display: flex;
  align-items: center;
  box-shadow: 0 3px 15px rgba(76, 193, 176, 0.12);
  transition: all 0.3s ease;
  border-left: 4px solid #50c9c3;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(76, 193, 176, 0.2);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 1.5rem;
}

.stat-icon.pending {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.stat-icon.sent {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.stat-icon.selected {
  background: rgba(26, 123, 181, 0.1);
  color: #1a7bb5;
}

.stat-info h3 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 5px 0;
  color: #333;
}

.stat-info p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

/* Filters */
.email-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(26, 123, 181, 0.08);
}

.search-box {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12px; /* Giảm từ 16px xuống 12px */
  padding: 0 8px; /* Giảm padding từ 10px xuống 8px */
  width: 120px; /* Giảm từ 180px xuống 160px để ngắn hơn nữa */
  max-width: 180%;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.search-box:focus-within {
  border-color: #1a7bb5;
  box-shadow: 0 0 0 3px rgba(26, 123, 181, 0.1);
}

.search-box i {
  color: #1a7bb5;
  margin-right: 5px; /* Giảm từ 6px xuống 5px */
  font-size: 0.75rem; /* Giảm từ 0.8rem xuống 0.75rem */
}

.search-box input {
  border: none;
  background: transparent;
  padding: 6px 0; /* Giảm từ 8px xuống 6px */
  font-size: 0.8rem; /* Giảm từ 0.85rem xuống 0.8rem */
  width: 100%;
  outline: none;
}

.search-box input::placeholder {
  color: #999;
}

.filter-controls {
  display: flex;
  gap: 15px;
  align-items: center;
}

.filter-select {
  padding: 10px 15px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  background: white;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #1a7bb5;
  box-shadow: 0 0 0 3px rgba(26, 123, 181, 0.1);
}

/* Responsive design */
@media (max-width: 1024px) {
  .email-management-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .email-filters {
    flex-direction: column;
    gap: 15px;
  }

  .search-box {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .email-management {
    padding: 15px;
  }

  .email-stats {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: 20px;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }

  .stat-info h3 {
    font-size: 1.5rem;
  }

  .header-actions {
    flex-direction: column;
    width: 100%;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .filter-controls {
    flex-direction: column;
    width: 100%;
  }

  .filter-select {
    width: 100%;
  }
}
