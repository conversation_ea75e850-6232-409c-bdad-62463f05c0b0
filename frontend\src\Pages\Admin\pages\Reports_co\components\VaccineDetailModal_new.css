/* Vaccine Detail Modal - Multi-Theme Support */
.admin-vaccine-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 24px;
  backdrop-filter: blur(12px);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.admin-vaccine-modal-container {
  background: white;
  border-radius: 20px;
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 25px 80px rgba(59, 130, 246, 0.25);
  border: 1px solid rgba(59, 130, 246, 0.1);
  display: flex;
  flex-direction: column;
  animation: modalSlideIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.85) translateY(-40px) rotateX(10deg);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02) translateY(-10px) rotateX(0deg);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0) rotateX(0deg);
  }
}

/* Header */
.admin-vaccine-modal-header h2 {
  color: white;
  font-size: 24px;
  font-weight: 700;
  margin: 0;
}

.admin-vaccine-modal-header {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
  padding: 32px 40px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

/* Theme-specific header backgrounds */
.theme-blue .admin-vaccine-modal-header {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
}

.theme-green .admin-vaccine-modal-header {
  background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
}

.theme-purple .admin-vaccine-modal-header {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%);
}

.theme-orange .admin-vaccine-modal-header {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
}

.theme-teal .admin-vaccine-modal-header {
  background: linear-gradient(135deg, #14b8a6 0%, #0d9488 50%, #0f766e 100%);
}

.admin-vaccine-modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.1) 50%, transparent 60%);
  animation: shimmer 3s infinite;
  pointer-events: none;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.admin-vaccine-modal-title {
  display: flex;
  align-items: center;
  gap: 15px;
  position: relative;
  z-index: 1;
  text-align: center;
}

.admin-vaccine-modal-title i {
  font-size: 28px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.admin-vaccine-modal-title h2 {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  text-align: center;
}

.admin-vaccine-modal-close-btn {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  width: 44px;
  height: 44px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  position: absolute;
  right: 35px;
  z-index: 1;
  backdrop-filter: blur(10px);
}

.admin-vaccine-modal-close-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: rotate(90deg) scale(1.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Modal Body */
.admin-vaccine-modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  max-height: calc(85vh - 160px);
}

.admin-vaccine-info-section {
  margin-bottom: 0;
  background: white;
  border-radius: 0;
  padding: 25px 30px;
  border: none;
  border-bottom: 1px solid #e2e8f0;
}

.admin-vaccine-info-section:last-child {
  border-bottom: none;
}

.admin-vaccine-info-section h3 {
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 10px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e9ecef;
}

.admin-vaccine-info-section h3 i {
  color: #4a90e2;
}

/* Basic Information */
.admin-vaccine-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.admin-vaccine-info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.admin-vaccine-info-item.admin-vaccine-full-width {
  grid-column: 1 / -1;
}

.admin-vaccine-info-item label {
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.admin-vaccine-name-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 15px;
  background: white;
  padding: 20px;
  border-radius: 10px;
  border: 1px solid #e2e8f0;
}

.admin-vaccine-name-display strong {
  font-size: 20px;
  color: #2c3e50;
  font-weight: 700;
}

.admin-status-indicator {
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-status-indicator.active {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.admin-status-indicator.inactive {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.admin-vaccine-description-box {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #4a90e2;
  font-style: italic;
  color: #495057;
  line-height: 1.6;
  min-height: 60px;
  display: flex;
  align-items: center;
}

/* Age Groups */
.admin-age-groups-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

.admin-age-group-card {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.admin-age-group-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  border-color: #4a90e2;
}

.admin-age-group-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4a90e2, #357abd);
}

.admin-age-group-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.admin-age-group-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #4a90e2, #357abd);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.admin-age-group-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.admin-age-range {
  font-size: 14px;
  color: #6c757d;
  margin: 0;
}

.admin-dosage-info {
  background: white;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  margin-top: 15px;
}

.admin-dosage-label {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  color: #6c757d;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.admin-dosage-value {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.admin-dosage-unit {
  font-size: 12px;
  color: #6c757d;
  font-weight: 400;
}

/* Manufacturer Information */
.admin-manufacturer-card {
  background: linear-gradient(135deg, #fff7ed, #fed7aa);
  border: 1px solid #fdba74;
  border-radius: 12px;
  padding: 25px;
  position: relative;
  overflow: hidden;
}

.admin-manufacturer-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #f97316, #ea580c);
}

.admin-manufacturer-name {
  font-size: 20px;
  font-weight: 700;
  color: #9a3412;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.admin-manufacturer-name i {
  color: #f97316;
}

.admin-manufacturer-country {
  background: rgba(255, 255, 255, 0.7);
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  color: #9a3412;
  display: inline-block;
  border: 1px solid rgba(154, 52, 18, 0.2);
}

/* Additional Details */
.admin-detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.admin-detail-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  position: relative;
}

.admin-detail-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  border-color: #4a90e2;
}

.admin-detail-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #e0f2fe, #b3e5fc);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  color: #0277bd;
  font-size: 20px;
}

.admin-detail-label {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  color: #6c757d;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.admin-detail-value {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

/* Storage Requirements */
.admin-storage-requirements {
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border: 1px solid #0ea5e9;
  border-radius: 12px;
  padding: 25px;
  position: relative;
}

.admin-storage-requirements::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #0ea5e9, #0284c7);
}

.admin-storage-title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
  color: #0c4a6e;
}

.admin-storage-title i {
  color: #0ea5e9;
}

.admin-storage-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.admin-storage-item {
  background: rgba(255, 255, 255, 0.8);
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  border: 1px solid rgba(14, 165, 233, 0.2);
}

.admin-storage-item i {
  font-size: 24px;
  color: #0ea5e9;
  margin-bottom: 8px;
  display: block;
}

.admin-storage-item strong {
  display: block;
  font-size: 16px;
  color: #0c4a6e;
  margin-bottom: 5px;
}

.admin-storage-item span {
  font-size: 14px;
  color: #64748b;
}

/* Dosage Information Card */
.admin-vaccine-dosage-info-card {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #0ea5e9;
  border-radius: 12px;
  padding: 25px;
  position: relative;
  overflow: hidden;
}

.admin-vaccine-dosage-info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #0ea5e9, #0284c7);
}

.admin-vaccine-dosage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(14, 165, 233, 0.2);
}

.admin-vaccine-dosage-type {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-vaccine-total-doses {
  background: rgba(255, 255, 255, 0.8);
  color: #0c4a6e;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  border: 1px solid rgba(14, 165, 233, 0.3);
}

.admin-vaccine-dosage-description {
  color: #0c4a6e;
  font-size: 15px;
  line-height: 1.6;
  margin-bottom: 20px;
  font-style: italic;
}

.admin-vaccine-dosage-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.admin-vaccine-dosage-detail-item {
  background: rgba(255, 255, 255, 0.8);
  padding: 15px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
  border: 1px solid rgba(14, 165, 233, 0.2);
  transition: all 0.3s ease;
}

.admin-vaccine-dosage-detail-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.15);
}

.admin-vaccine-dosage-detail-item i {
  color: #0ea5e9;
  font-size: 18px;
  width: 20px;
  text-align: center;
}

.admin-vaccine-dosage-detail-item div {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.admin-vaccine-label {
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-vaccine-value {
  font-size: 14px;
  font-weight: 600;
  color: #0c4a6e;
}

/* Status Information Card */
.admin-vaccine-status-info-card {
  border-radius: 12px;
  padding: 25px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.admin-vaccine-status-info-card.active {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border: 1px solid #10b981;
}

.admin-vaccine-status-info-card.inactive {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border: 1px solid #ef4444;
}

.admin-vaccine-status-info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.admin-vaccine-status-info-card.active::before {
  background: linear-gradient(90deg, #10b981, #059669);
}

.admin-vaccine-status-info-card.inactive::before {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.admin-vaccine-status-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.admin-vaccine-status-header i {
  font-size: 24px;
}

.admin-vaccine-status-info-card.active .admin-vaccine-status-header i {
  color: #059669;
}

.admin-vaccine-status-info-card.inactive .admin-vaccine-status-header i {
  color: #dc2626;
}

.admin-vaccine-status-text {
  font-size: 18px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-vaccine-status-info-card.active .admin-vaccine-status-text {
  color: #065f46;
}

.admin-vaccine-status-info-card.inactive .admin-vaccine-status-text {
  color: #991b1b;
}

.admin-vaccine-status-description {
  font-size: 15px;
  line-height: 1.6;
  margin: 0;
}

.admin-vaccine-status-info-card.active .admin-vaccine-status-description {
  color: #047857;
}

.admin-vaccine-status-info-card.inactive .admin-vaccine-status-description {
  color: #b91c1c;
}

/* Additional Information */
.admin-vaccine-additional-info {
  display: grid;
  gap: 15px;
}

.admin-vaccine-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 10px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.admin-vaccine-info-row:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #3b82f6;
}

.admin-vaccine-info-row .admin-vaccine-label {
  font-size: 14px;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-vaccine-info-row .admin-vaccine-value {
  font-size: 15px;
  font-weight: 600;
  color: #1e293b;
}

.admin-vaccine-value.admin-vaccine-status-active {
  color: #059669;
  background: linear-gradient(135deg, #d1fae5, #a7f3d0);
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-vaccine-value.admin-vaccine-status-inactive {
  color: #dc2626;
  background: linear-gradient(135deg, #fee2e2, #fecaca);
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Modal Footer */
.admin-vaccine-modal-footer {
  padding: 25px 30px;
  border-top: 1px solid #e9ecef;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 15px;
  border-radius: 0 0 16px 16px;
  position: relative;
  margin-top: 30px;
}

.admin-vaccine-modal-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #e9ecef, transparent);
}

.admin-vaccine-modal-info {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #64748b;
  font-size: 14px;
}

.admin-vaccine-modal-info i {
  color: #3b82f6;
}

.admin-vaccine-modal-actions {
  display: flex;
  gap: 12px;
}

.admin-vaccine-modal-footer-close-btn {
  background: linear-gradient(135deg, #64748b 0%, #475569 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 8px rgba(100, 116, 139, 0.3);
}

.admin-vaccine-modal-footer-close-btn:hover {
  background: linear-gradient(135deg, #475569 0%, #334155 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(100, 116, 139, 0.4);
}

.admin-vaccine-modal-footer-close-btn:active {
  transform: translateY(0);
}

.admin-vaccine-modal-print-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.admin-vaccine-modal-print-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.admin-vaccine-modal-print-btn:active {
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-vaccine-modal-container {
    margin: 10px;
    max-height: calc(100vh - 20px);
  }

  .admin-vaccine-modal-content {
    border-radius: 12px;
  }

  .admin-vaccine-modal-header {
    padding: 20px;
  }

  .admin-vaccine-modal-title h2 {
    font-size: 20px;
  }

  .admin-vaccine-modal-body {
    padding: 20px;
    max-height: calc(100vh - 200px);
  }

  .admin-vaccine-info-section {
    margin-bottom: 25px;
  }

  .admin-vaccine-info-section h3 {
    font-size: 16px;
  }

  .admin-detail-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .admin-storage-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .admin-manufacturer-card {
    padding: 20px;
  }

  .admin-manufacturer-name {
    font-size: 18px;
  }

  .admin-vaccine-modal-footer {
    flex-direction: column;
    gap: 15px;
    padding: 20px;
  }

  .admin-vaccine-modal-info {
    order: 2;
    text-align: center;
  }

  .admin-vaccine-modal-actions {
    order: 1;
    width: 100%;
    justify-content: center;
  }

  .admin-vaccine-modal-footer-close-btn,
  .admin-vaccine-modal-print-btn {
    flex: 1;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .admin-vaccine-modal-container {
    margin: 5px;
  }

  .admin-vaccine-modal-header {
    padding: 15px;
  }

  .admin-vaccine-modal-body {
    padding: 15px;
  }

  .admin-storage-grid {
    grid-template-columns: 1fr;
  }

  .admin-vaccine-modal-footer {
    padding: 15px;
  }

  .admin-vaccine-modal-actions {
    flex-direction: column;
  }
}
