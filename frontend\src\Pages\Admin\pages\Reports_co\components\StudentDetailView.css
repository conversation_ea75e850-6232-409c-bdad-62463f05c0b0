/* Student Detail View - Multi-Theme Support */
.reports-student-detail-page {
  max-width: 1200px;
  margin: 0 auto;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
  padding: 24px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Theme-specific backgrounds */
.reports-student-detail-page.theme-blue {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.reports-student-detail-page.theme-green {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

.reports-student-detail-page.theme-purple {
  background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
}

.reports-student-detail-page.theme-orange {
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

.reports-student-detail-page.theme-teal {
  background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
}

/* Back Button Outside Header */
.reports-student-detail-page > .reports-back-button-container {
  margin-bottom: 24px;
}

/* Header */
.reports-student-detail-page-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 32px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.08);
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
}

.reports-student-detail-page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #2563eb, #1d4ed8);
}

.reports-student-detail-page-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Body Content */
.reports-student-detail-content {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 32px;
}

/* Left Column */
.left-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

/* Left Column */
.reports-student-detail-left-column {
  background: white;
  border-radius: 16px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.08);
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
}

.reports-student-detail-left-column::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #10b981, #059669);
}

/* Student Photo */
.reports-student-detail-photo {
  width: 220px;
  height: 220px;
  border: 3px solid #e2e8f0;
  border-radius: 16px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
  position: relative;
}

.reports-student-detail-photo::before {
  content: '';
  position: absolute;
  inset: -3px;
  background: linear-gradient(135deg, #3b82f6, #2563eb, #1d4ed8);
  border-radius: 16px;
  z-index: -1;
}

.reports-student-detail-photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
  max-width: none;
  max-height: none;
  border-radius: 13px;
}

.reports-student-detail-photo-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #64748b;
  text-align: center;
}

.reports-student-detail-photo-placeholder p {
  margin: 8px 0 0 0;
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

/* Basic Info */
.reports-student-detail-basic-info {
  width: 100%;
  text-align: center;
}

.reports-student-detail-basic-info h2 {
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 20px 0;
  background: linear-gradient(135deg, #10b981, #059669);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.reports-student-detail-info-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 12px 16px;
  margin: 8px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.reports-student-detail-info-item:hover {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-color: #bfdbfe;
  transform: translateY(-1px);
}

.reports-student-detail-info-item svg {
  width: 16px;
  height: 16px;
  color: #3b82f6;
}

/* Right Column */
.reports-student-detail-right-column {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.08);
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
}

.reports-student-detail-right-column::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #2563eb, #1d4ed8);
}

.reports-student-detail-right-column h3 {
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 24px 0;
  padding-bottom: 12px;
  border-bottom: 2px solid #e2e8f0;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Detail Info */
.reports-student-detail-detail-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.reports-student-detail-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f3f4f6;
}

.reports-student-detail-info-row:last-child {
  border-bottom: none;
}

.reports-student-detail-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.reports-student-detail-label svg {
  width: 16px;
  height: 16px;
  color: #9ca3af;
}

.reports-student-detail-value {
  font-size: 0.875rem;
  color: #111827;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .reports-student-detail-page {
    padding: 16px;
  }

  .reports-student-detail-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .reports-student-detail-left-column {
    align-items: center;
  }

  .reports-student-detail-photo {
    width: 160px;
    height: 160px;
  }

  .reports-student-detail-page-header h1 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .reports-student-detail-page {
    padding: 12px;
  }

  .reports-student-detail-photo {
    width: 140px;
    height: 140px;
  }

  .reports-student-detail-page-header h1 {
    font-size: 1.25rem;
  }

  .reports-student-detail-basic-info h2 {
    font-size: 1.125rem;
  }
}

/* Gender Badge Styles */
.reports-student-gender-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
  transition: all 0.2s ease;
}

.reports-student-gender-badge.male {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1e40af;
  border: 1px solid #93c5fd;
}

.reports-student-gender-badge.female {
  background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
  color: #be185d;
  border: 1px solid #f9a8d4;
}

.reports-student-gender-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}