/* ============= HEALTH DECLARATION - MODERN BLUE & WHITE DESIGN ============= */

/* Namespace: hdm- (health-declaration-modern) để tránh xung đột CSS */

/* CSS Variables - Blue & White Theme - Scoped to Health Declaration */
.health-declaration-container {
  /* Primary Blue Palette */
  --hdm-primary-blue: #1e40af;
  --hdm-primary-blue-light: #3b82f6;
  --hdm-primary-blue-dark: #1e3a8a;
  --hdm-blue-50: #eff6ff;
  --hdm-blue-100: #dbeafe;
  --hdm-blue-200: #bfdbfe;
  --hdm-blue-300: #93c5fd;
  --hdm-blue-500: #3b82f6;
  --hdm-blue-600: #2563eb;
  --hdm-blue-700: #1d4ed8;
  --hdm-blue-800: #1e40af;
  --hdm-blue-900: #1e3a8a;
  
  /* Neutral Colors */
  --hdm-white: #ffffff;
  --hdm-gray-50: #f8fafc;
  --hdm-gray-100: #f1f5f9;
  --hdm-gray-200: #e2e8f0;
  --hdm-gray-300: #cbd5e1;
  --hdm-gray-400: #94a3b8;
  --hdm-gray-500: #64748b;
  --hdm-gray-600: #475569;
  --hdm-gray-700: #334155;
  --hdm-gray-800: #1e293b;
  --hdm-gray-900: #0f172a;
  
  /* Status Colors */
  --hdm-success: #10b981;
  --hdm-success-light: #34d399;
  --hdm-success-bg: #ecfdf5;
  --hdm-warning: #f59e0b;
  --hdm-warning-bg: #fffbeb;
  --hdm-error: #ef4444;
  --hdm-error-bg: #fef2f2;
  
  /* Spacing */
  --hdm-space-1: 0.25rem;
  --hdm-space-2: 0.5rem;
  --hdm-space-3: 0.75rem;
  --hdm-space-4: 1rem;
  --hdm-space-5: 1.25rem;
  --hdm-space-6: 1.5rem;
  --hdm-space-8: 2rem;
  --hdm-space-10: 2.5rem;
  --hdm-space-12: 3rem;
  
  /* Border Radius */
  --hdm-radius-sm: 0.375rem;
  --hdm-radius: 0.5rem;
  --hdm-radius-md: 0.75rem;
  --hdm-radius-lg: 1rem;
  --hdm-radius-xl: 1.5rem;
  
  /* Shadows */
  --hdm-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --hdm-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --hdm-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --hdm-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --hdm-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  /* Transitions */
  --hdm-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --hdm-transition-slow: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ============= MAIN CONTAINER ============= */
.health-declaration-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--hdm-space-6);
  background-color: var(--hdm-gray-50);
  min-height: calc(100vh - 130px);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  color: var(--hdm-gray-900);
  line-height: 1.6;
}

/* Override handled by global.css */

/* Override handled by global.css */

/* ============= HEADER HEALTH DECLARATION - Specific Classes ============= */
.health-declaration-container .headerofhealthdeclaration {
  background: linear-gradient(135deg, #015c92 0%, #2d82b5 50%, #428cd4 100%);
  color: var(--hdm-white);
  padding: var(--hdm-space-8) var(--hdm-space-6);
  border-radius: var(--hdm-radius-xl);
  margin-bottom: var(--hdm-space-8);
  box-shadow: var(--hdm-shadow-lg);
  position: relative;
  overflow: hidden;
  isolation: isolate; /* Tạo stacking context riêng */
}

.health-declaration-container .headerofhealthdeclaration__content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  gap: var(--hdm-space-6);
  position: relative;
  z-index: 2;
}

.health-declaration-container .headerofhealthdeclaration__text {
  text-align: left;
  flex: 1;
}

.health-declaration-container .headerofhealthdeclaration__text h1 {
  margin: 0 0 var(--hdm-space-2) 0;
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
  color: var(--hdm-white);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.health-declaration-container .headerofhealthdeclaration__text p {
  margin: 0;
  font-size: 1.125rem;
  opacity: 0.9;
  color: var(--hdm-white);
}

.health-declaration-container .headerofhealthdeclaration__actions {
  display: flex;
  align-items: center;
  gap: var(--hdm-space-4);
  flex-shrink: 0;
}

/* Nút làm mới - Specific styling */
.health-declaration-container .headerofhealthdeclaration__reload-button {
  display: flex;
  align-items: center;
  gap: var(--hdm-space-2);
  padding: var(--hdm-space-3) var(--hdm-space-5);
  background: rgba(255, 255, 255, 0.15);
  color: var(--hdm-white);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--hdm-radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--hdm-transition);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  white-space: nowrap;
  min-height: 44px;
  min-width: 120px;
  justify-content: center;
  text-decoration: none;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

/* Hover effect */
.health-declaration-container .headerofhealthdeclaration__reload-button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Active effect */
.health-declaration-container .headerofhealthdeclaration__reload-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* Disabled state */
.health-declaration-container .headerofhealthdeclaration__reload-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: none;
}

/* Loading animation cho khi disabled */
.health-declaration-container .headerofhealthdeclaration__reload-button:disabled svg {
  animation: spin 1s linear infinite;
}

/* SVG icon styling */
.health-declaration-container .headerofhealthdeclaration__reload-button svg {
  transition: transform 0.3s ease;
  flex-shrink: 0;
}

/* Hover rotate effect */
.health-declaration-container .headerofhealthdeclaration__reload-button:hover:not(:disabled) svg {
  transform: rotate(180deg);
}

/* Keyframe cho spin animation */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive design cho header */
@media (max-width: 768px) {
  .health-declaration-container .headerofhealthdeclaration {
    padding: var(--hdm-space-6) var(--hdm-space-4);
  }
  
  .health-declaration-container .headerofhealthdeclaration__content {
    flex-direction: column;
    text-align: center;
    gap: var(--hdm-space-4);
  }
  
  .health-declaration-container .headerofhealthdeclaration__text {
    text-align: center;
  }
  
  .health-declaration-container .headerofhealthdeclaration__text h1 {
    font-size: 2rem;
  }
  
  .health-declaration-container .headerofhealthdeclaration__reload-button {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .health-declaration-container .headerofhealthdeclaration__text h1 {
    font-size: 1.75rem;
  }
  
  .health-declaration-container .headerofhealthdeclaration__text p {
    font-size: 1rem;
  }
}

/* ============= FORM CONTAINER ============= */
.health-declaration-form {
  background-color: var(--hdm-white);
  border-radius: var(--hdm-radius-xl);
  box-shadow: var(--hdm-shadow-lg);
  overflow: hidden;
  border: 1px solid var(--hdm-gray-200);
}

/* ============= FORM SECTIONS ============= */
.health-declaration-form .form-section {
  padding: var(--hdm-space-8);
  border-bottom: 1px solid var(--hdm-gray-200);
  position: relative;
}

.health-declaration-form .form-section:last-of-type {
  border-bottom: none;
}

.health-declaration-form .form-section h3 {
  font-size: 1.375rem;
  font-weight: 600;
  color: var(--hdm-gray-900);
  margin: 0 0 var(--hdm-space-6) 0;
  display: flex;
  align-items: center;
  gap: var(--hdm-space-3);
  position: relative;
}

.health-declaration-form .form-section h3::before {
  content: '';
  width: 4px;
  height: 28px;
  background: linear-gradient(135deg, var(--hdm-primary-blue), var(--hdm-primary-blue-light));
  border-radius: var(--hdm-radius-sm);
}

/* ============= STUDENT SELECTOR ============= */
.health-declaration-form .student-selector {
  background-color: var(--hdm-blue-50);
  border: 1px solid var(--hdm-blue-200);
  border-radius: var(--hdm-radius-lg);
  padding: var(--hdm-space-6);
  margin-bottom: var(--hdm-space-6);
  transition: var(--hdm-transition);
}

.health-declaration-form .student-selector:hover {
  border-color: var(--hdm-blue-300);
  box-shadow: 0 0 0 3px var(--hdm-blue-100);
}

.health-declaration-form .student-selector label {
  display: block;
  font-weight: 600;
  color: var(--hdm-gray-700);
  margin-bottom: var(--hdm-space-3);
  font-size: 0.9rem;
}

.health-declaration-form .student-selector select,
.health-declaration-form .student-selector .selectstudentfix {
  width: 100%;
  padding: var(--hdm-space-4);
  border: 1px solid var(--hdm-gray-300);
  border-radius: var(--hdm-radius);
  font-size: 1rem;
  background-color: var(--hdm-white);
  transition: var(--hdm-transition);
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--hdm-space-3) center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: var(--hdm-space-10);
}

.health-declaration-form .student-selector select:focus,
.health-declaration-form .student-selector .selectstudentfix:focus {
  outline: none;
  border-color: var(--hdm-primary-blue);
  box-shadow: 0 0 0 3px var(--hdm-blue-100);
}

/* ============= FORM LAYOUTS ============= */
.health-declaration-form .form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--hdm-space-6);
  margin-bottom: var(--hdm-space-6);
}

.health-declaration-form .form-group {
  display: flex;
  flex-direction: column;
  position: relative;
}

.health-declaration-form .form-group label {
  font-weight: 500;
  color: var(--hdm-gray-700);
  margin-bottom: var(--hdm-space-2);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: var(--hdm-space-2);
}

.health-declaration-form .form-group .required {
  color: var(--hdm-error);
  font-weight: 600;
}

/* ============= FORM INPUTS ============= */
.health-declaration-form input,
.health-declaration-form select,
.health-declaration-form textarea {
  padding: var(--hdm-space-3) var(--hdm-space-4);
  border: 1px solid var(--hdm-gray-300);
  border-radius: var(--hdm-radius);
  font-size: 0.9rem;
  transition: var(--hdm-transition);
  background-color: var(--hdm-white);
  font-family: inherit;
}

.health-declaration-form input:focus,
.health-declaration-form select:focus,
.health-declaration-form textarea:focus {
  outline: none;
  border-color: var(--hdm-primary-blue);
  box-shadow: 0 0 0 3px var(--hdm-blue-100);
}

.health-declaration-form select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--hdm-space-3) center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: var(--hdm-space-10);
}

.health-declaration-form textarea {
  resize: vertical;
  min-height: 100px;
  line-height: 1.5;
}

/* Error States */
.health-declaration-form input.error,
.health-declaration-form select.error,
.health-declaration-form textarea.error {
  border-color: var(--hdm-error);
  box-shadow: 0 0 0 3px rgb(239 68 68 / 0.1);
}

.health-declaration-form .error-text {
  color: var(--hdm-error);
  font-size: 0.8rem;
  margin-top: var(--hdm-space-1);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: var(--hdm-space-1);
}

.health-declaration-form .error-text::before {
  content: '⚠';
  font-size: 0.7rem;
}

/* ============= VACCINES SECTION ============= */
.health-declaration-form .vaccines-section {
  padding: var(--hdm-space-8);
  margin-top: 0;
  border: none;
  background-color: transparent;
  border-radius: 0;
}

.health-declaration-form .vaccines-section h3 {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--hdm-primary-blue);
  margin-bottom: var(--hdm-space-6);
  padding-bottom: var(--hdm-space-4);
  border-bottom: 3px solid var(--hdm-blue-200);
  text-align: center;
  background: linear-gradient(135deg, var(--hdm-primary-blue) 0%, var(--hdm-primary-blue-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.health-declaration-form .vaccines-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--hdm-space-5);
  margin-top: var(--hdm-space-8);
}

/* ============= VACCINE CARD STYLES ============= */
.health-declaration-form .vaccine-card {
  background: var(--hdm-white);
  border: 2px solid var(--hdm-gray-200);
  border-radius: var(--hdm-radius-lg);
  overflow: hidden;
  transition: var(--hdm-transition-slow);
  cursor: pointer;
  box-shadow: var(--hdm-shadow-sm);
  position: relative;
}

.health-declaration-form .vaccine-card:hover {
  border-color: var(--hdm-primary-blue-light);
  box-shadow: var(--hdm-shadow-md);
  transform: translateY(-2px);
}

.health-declaration-form .vaccine-card.selected {
  border-color: var(--hdm-primary-blue);
  box-shadow: var(--hdm-shadow-lg);
  background: linear-gradient(145deg, var(--hdm-blue-50), var(--hdm-white));
}

.health-declaration-form .vaccine-card.vaccinated-from-server {
  background: linear-gradient(145deg, var(--hdm-success-bg), var(--hdm-white));
  border-color: var(--hdm-success);
}

.health-declaration-form .vaccine-card.selected::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--hdm-primary-blue), var(--hdm-primary-blue-light));
}

.health-declaration-form .vaccine-card.vaccinated-from-server::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--hdm-success), var(--hdm-success-light));
}

/* ============= VACCINE CARD HEADER ============= */
.health-declaration-form .vaccine-card-header {
  display: flex;
  align-items: center;
  gap: var(--hdm-space-4);
  padding: var(--hdm-space-5);
  background: var(--hdm-gray-50);
  border-bottom: 1px solid var(--hdm-gray-200);
  height: 87px;
  box-sizing: border-box;
}

.health-declaration-form .vaccine-card.selected .vaccine-card-header {
  background: var(--hdm-blue-50);
  border-bottom-color: var(--hdm-blue-200);
}

.health-declaration-form .vaccine-card.vaccinated-from-server .vaccine-card-header {
  background: var(--hdm-success-bg);
  border-bottom-color: var(--hdm-success);
}

/* ============= CUSTOM CHECKBOX ============= */
.health-declaration-form .vaccine-checkbox-container {
  position: relative;
  flex-shrink: 0;
}

.health-declaration-form .vaccine-checkbox {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.health-declaration-form .checkbox-label {
  position: relative;
  cursor: pointer;
  display: block;
  width: 24px;
  height: 24px;
}

.health-declaration-form .checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 24px;
  width: 24px;
  background-color: var(--hdm-white);
  border: 2px solid var(--hdm-gray-300);
  border-radius: var(--hdm-radius-sm);
  transition: var(--hdm-transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.health-declaration-form .vaccine-checkbox:checked + .checkbox-label .checkmark {
  background-color: var(--hdm-primary-blue);
  border-color: var(--hdm-primary-blue);
}

.health-declaration-form .vaccine-checkbox:checked + .checkbox-label .checkmark::after {
  content: '✓';
  color: var(--hdm-white);
  font-weight: bold;
  font-size: 14px;
}

.health-declaration-form .vaccine-checkbox:disabled + .checkbox-label .checkmark {
  background-color: var(--hdm-gray-100);
  border-color: var(--hdm-gray-300);
  cursor: not-allowed;
}

.health-declaration-form .vaccine-checkbox:disabled + .checkbox-label {
  cursor: not-allowed;
}

/* ============= VACCINE NAME CONTAINER ============= */
.health-declaration-form .vaccine-name-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--hdm-space-2);
}

.health-declaration-form .vaccine-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--hdm-gray-800);
  margin: 0;
  line-height: 1.3;
}

.health-declaration-form .vaccine-card.selected .vaccine-name {
  color: var(--hdm-primary-blue);
}

.health-declaration-form .vaccine-card.vaccinated-from-server .vaccine-name {
  color: var(--hdm-success);
}

.health-declaration-form .vaccinated-badge {
  background: linear-gradient(135deg, var(--hdm-success), var(--hdm-success-light));
  color: var(--hdm-white);
  font-size: 0.75rem;
  font-weight: 600;
  padding: var(--hdm-space-1) var(--hdm-space-3);
  border-radius: var(--hdm-radius-xl);
  display: inline-flex;
  align-items: center;
  gap: var(--hdm-space-1);
  width: fit-content;
  box-shadow: var(--hdm-shadow-sm);
  animation: pulse 2s infinite;
}

.health-declaration-form .fully-vaccinated-badge {
  background: linear-gradient(135deg, var(--hdm-primary-blue), var(--hdm-primary-blue-light));
  color: var(--hdm-white);
  font-size: 0.75rem;
  font-weight: 600;
  padding: var(--hdm-space-1) var(--hdm-space-3);
  border-radius: var(--hdm-radius-xl);
  display: inline-flex;
  align-items: center;
  gap: var(--hdm-space-1);
  width: fit-content;
  box-shadow: var(--hdm-shadow-sm);
  animation: pulse 2s infinite;
}

/* ============= ANIMATIONS ============= */
@keyframes pulse {
  0%, 100% { 
    transform: scale(1); 
    box-shadow: var(--hdm-shadow-sm);
  }
  50% { 
    transform: scale(1.05); 
    box-shadow: var(--hdm-shadow-md);
  }
}

@keyframes expandIn {
  from {
    opacity: 0;
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
  }
  to {
    opacity: 1;
    max-height: 500px;
    padding-top: var(--hdm-space-5);
    padding-bottom: var(--hdm-space-5);
  }
}

/* ============= VACCINE CARD EXPANDED SECTION ============= */
.health-declaration-form .vaccine-card-expanded {
  padding: var(--hdm-space-5);
  background: var(--hdm-white);
  border-top: 1px solid var(--hdm-gray-200);
  animation: expandIn 0.3s ease-out;
}

.health-declaration-form .vaccine-card.selected .vaccine-card-expanded {
  background: var(--hdm-blue-50);
  border-top-color: var(--hdm-blue-200);
}

/* ============= VACCINE FORM SECTION ============= */
.health-declaration-form .vaccine-form-section {
  display: flex;
  flex-direction: column;
  gap: var(--hdm-space-4);
}

.health-declaration-form .vaccine-form-section .form-group {
  display: flex;
  flex-direction: column;
  gap: var(--hdm-space-2);
}

.health-declaration-form .vaccine-form-section label {
  font-weight: 600;
  color: var(--hdm-gray-700);
  font-size: 0.9rem;
}

.health-declaration-form .vaccine-card.selected .vaccine-form-section label {
  color: var(--hdm-primary-blue);
}

.health-declaration-form .location-select {
  padding: var(--hdm-space-3);
  border: 2px solid var(--hdm-gray-200);
  border-radius: var(--hdm-radius);
  font-size: 0.9rem;
  background-color: var(--hdm-white);
  color: var(--hdm-gray-800);
  transition: var(--hdm-transition);
  cursor: pointer;
}

.health-declaration-form .location-select:focus {
  outline: none;
  border-color: var(--hdm-primary-blue);
  box-shadow: 0 0 0 3px var(--hdm-blue-200);
}

.health-declaration-form .location-select:disabled {
  background-color: var(--hdm-gray-100);
  cursor: not-allowed;
  opacity: 0.7;
}

.health-declaration-form .notes-textarea {
  padding: var(--hdm-space-3);
  border: 2px solid var(--hdm-gray-200);
  border-radius: var(--hdm-radius);
  font-size: 0.9rem;
  font-family: inherit;
  background-color: var(--hdm-white);
  color: var(--hdm-gray-800);
  transition: var(--hdm-transition);
  resize: vertical;
  min-height: 80px;
}

.health-declaration-form .notes-textarea:focus {
  outline: none;
  border-color: var(--hdm-primary-blue);
  box-shadow: 0 0 0 3px var(--hdm-blue-200);
}

.health-declaration-form .notes-textarea:disabled {
  background-color: var(--hdm-gray-100);
  cursor: not-allowed;
  opacity: 0.7;
}

.health-declaration-form .notes-textarea::placeholder {
  color: var(--hdm-gray-400);
  font-style: italic;
}

/* ============= FORM ACTIONS ============= */
.health-declaration-form .form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--hdm-space-4);
  padding: var(--hdm-space-8);
  background-color: var(--hdm-gray-50);
  border-top: 1px solid var(--hdm-gray-200);
}

.health-declaration-form .submit-button {
  background: linear-gradient(135deg, var(--hdm-primary-blue) 0%, var(--hdm-primary-blue-light) 100%);
  color: var(--hdm-white);
  border: none;
  padding: var(--hdm-space-4) var(--hdm-space-8);
  border-radius: var(--hdm-radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--hdm-transition);
  box-shadow: var(--hdm-shadow);
  position: relative;
  overflow: hidden;
}

.health-declaration-form .submit-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--hdm-shadow-md);
}

.health-declaration-form .submit-button:disabled {
  background: var(--hdm-gray-400);
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--hdm-shadow-sm);
}

/* ============= ALERTS & MESSAGES ============= */
.health-declaration-container .server-error-alert {
  background-color: var(--hdm-error-bg);
  border: 1px solid var(--hdm-error);
  border-left: 4px solid var(--hdm-error);
  border-radius: var(--hdm-radius);
  padding: var(--hdm-space-4);
  margin-bottom: var(--hdm-space-6);
  display: flex;
  align-items: start;
  gap: var(--hdm-space-3);
}

.health-declaration-container .server-error-alert .warning-icon {
  color: var(--hdm-error);
  font-size: 1.25rem;
  margin-top: 2px;
}

.health-declaration-container .server-error-alert .warning-content h4 {
  margin: 0 0 var(--hdm-space-2) 0;
  color: var(--hdm-error);
  font-weight: 600;
}

.health-declaration-container .server-error-alert .warning-content p {
  margin: 0;
  color: #991b1b;
  line-height: 1.5;
}

.health-declaration-container .success-message {
  background-color: var(--hdm-success-bg);
  border: 1px solid var(--hdm-success);
  border-left: 4px solid var(--hdm-success);
  border-radius: var(--hdm-radius);
  padding: var(--hdm-space-4);
  margin-bottom: var(--hdm-space-6);
  display: flex;
  align-items: start;
  gap: var(--hdm-space-3);
}

.health-declaration-container .success-message .success-icon {
  color: var(--hdm-success);
  font-size: 1.25rem;
  margin-top: 2px;
}

.health-declaration-container .success-message h3 {
  margin: 0 0 var(--hdm-space-2) 0;
  color: var(--hdm-success);
  font-weight: 600;
}

.health-declaration-container .success-message p {
  margin: 0;
  color: #047857;
  line-height: 1.5;
}

.health-declaration-container .validation-summary {
  background-color: var(--hdm-warning-bg);
  border: 1px solid var(--hdm-warning);
  border-left: 4px solid var(--hdm-warning);
  border-radius: var(--hdm-radius);
  padding: var(--hdm-space-4);
  margin-bottom: var(--hdm-space-6);
}

.health-declaration-container .validation-summary h3 {
  margin: 0 0 var(--hdm-space-3) 0;
  color: #92400e;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--hdm-space-2);
}

.health-declaration-container .validation-summary ul {
  margin: 0;
  padding-left: var(--hdm-space-5);
  color: #92400e;
}

.health-declaration-container .validation-summary li {
  margin-bottom: var(--hdm-space-1);
  line-height: 1.5;
}

/* ============= HELP TEXT STYLES ============= */
.health-declaration-form .vaccines-section .help-text {
  color: var(--hdm-gray-700);
  font-size: 0.95rem;
  line-height: 1.7;
  margin-bottom: var(--hdm-space-8);
  background: linear-gradient(145deg, var(--hdm-blue-50), #f0f7ff);
  padding: var(--hdm-space-6);
  border-radius: var(--hdm-radius-lg);
  border-left: 6px solid var(--hdm-primary-blue);
  box-shadow: var(--hdm-shadow-sm);
  position: relative;
}

.health-declaration-form .vaccines-section .help-text::before {
  content: 'ℹ️';
  position: absolute;
  top: var(--hdm-space-4);
  left: var(--hdm-space-4);
  font-size: 1.2rem;
  opacity: 0.8;
}

.health-declaration-form .vaccines-section .help-text {
  padding-left: calc(var(--hdm-space-6) + 2rem);
}

.health-declaration-form .vaccines-section .help-text strong {
  color: var(--hdm-primary-blue);
  font-weight: 700;
  background: linear-gradient(135deg, var(--hdm-primary-blue) 0%, var(--hdm-primary-blue-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* ============= MODAL STYLES ============= */
/* Modal styles */
.hdm-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.hdm-modal-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalFadeIn 0.3s ease;
}

.hdm-modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.hdm-modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.hdm-modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  line-height: 1;
}

.hdm-modal-body {
  padding: 20px;
}

.hdm-modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
}

.hdm-button {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.hdm-button.primary {
  background-color: #1976d2;
  color: white;
}

.hdm-button.primary:hover {
  background-color: #1565c0;
}

/* Vaccine info in modal */
.vaccine-info-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.vaccine-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.vaccine-info-header h4 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.vaccine-info-details {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
}

.vaccine-info-details p {
  margin: 8px 0;
}

.vaccine-info-message {
  border-left: 4px solid #ff9800;
  padding: 10px 15px;
  background-color: #fff3e0;
  border-radius: 4px;
}

.vaccine-warning {
  color: #e65100;
  display: flex;
  align-items: center;
  font-weight: 500;
}

.warning-icon {
  margin-right: 8px;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ============= VACCINE MODAL SPECIFIC STYLES ============= */
.hdm-modal-content .vaccine-info-display {
  margin-bottom: var(--hdm-space-6);
}

.hdm-modal-content .vaccine-info-display h4 {
  color: var(--hdm-gray-900);
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 var(--hdm-space-3) 0;
}

.hdm-modal-content .vaccine-description {
  color: var(--hdm-gray-600);
  margin-bottom: var(--hdm-space-4);
  line-height: 1.5;
}

.hdm-modal-content .vaccine-details-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--hdm-space-3);
  margin-bottom: var(--hdm-space-6);
}

.hdm-modal-content .vaccine-detail-item {
  padding: var(--hdm-space-3);
  background-color: var(--hdm-gray-100);
  border-radius: var(--hdm-radius);
  border: 1px solid var(--hdm-gray-200);
}

.hdm-modal-content .vaccine-detail-item:last-child {
  grid-column: 1 / -1;
}

.hdm-modal-content .vaccine-detail-item .label {
  font-weight: 600;
  color: var(--hdm-gray-800);
}

.hdm-modal-content .vaccine-detail-item .value {
  margin-left: var(--hdm-space-2);
  color: var(--hdm-gray-600);
}

.hdm-modal-content .notification-message {
  background-color: var(--hdm-warning-bg);
  border: 1px solid var(--hdm-warning);
  border-radius: var(--hdm-radius);
  padding: var(--hdm-space-4);
  display: flex;
  align-items: flex-start;
  gap: var(--hdm-space-3);
  margin-bottom: var(--hdm-space-6);
}

.hdm-modal-content .notification-message .warning-icon {
  font-size: 1.5rem;
  margin-top: 2px;
}

.hdm-modal-content .notification-message .message-text p {
  margin: 0 0 var(--hdm-space-2) 0;
  color: #92400e;
  line-height: 1.5;
}

.hdm-modal-content .notification-message .message-text p:last-child {
  margin-bottom: 0;
}

/* ============= TOOLTIPS ============= */
.hdm-tooltip {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background-color: var(--hdm-gray-400);
  color: var(--hdm-white);
  border-radius: 50%;
  font-size: 0.7rem;
  font-weight: 600;
  cursor: help;
  margin-left: var(--hdm-space-1);
}

.hdm-tooltip:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 150%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--hdm-gray-900);
  color: var(--hdm-white);
  padding: var(--hdm-space-2) var(--hdm-space-3);
  border-radius: var(--hdm-radius);
  font-size: 0.75rem;
  white-space: nowrap;
  z-index: 10;
  box-shadow: var(--hdm-shadow-lg);
  animation: hdm-tooltip-appear 0.2s ease-out;
}

.hdm-tooltip:hover::before {
  content: '';
  position: absolute;
  bottom: 135%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: var(--hdm-gray-900);
  z-index: 10;
}

@keyframes hdm-tooltip-appear {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* ============= LOADING STATES ============= */
.hdm-loading {
  display: inline-flex;
  align-items: center;
  gap: var(--hdm-space-2);
  color: var(--hdm-gray-600);
  font-size: 0.9rem;
}

.hdm-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--hdm-gray-300);
  border-top-color: var(--hdm-primary-blue);
  border-radius: 50%;
  animation: hdm-spin 1s linear infinite;
}

@keyframes hdm-spin {
  to {
    transform: rotate(360deg);
  }
}

/* ============= RESPONSIVE MODAL STYLES ============= */
@media (max-width: 768px) {
  .hdm-modal-content .vaccine-details-grid {
    grid-template-columns: 1fr;
  }
}

/* ============= RESPONSIVE DESIGN ============= */
@media (max-width: 768px) {
  .health-declaration-container {
    padding: var(--hdm-space-4);
  }
  
  .health-declaration-container .page-header {
    padding: var(--hdm-space-6) var(--hdm-space-4);
    margin-bottom: var(--hdm-space-6);
  }
  
  .health-declaration-container .page-header h1 {
    font-size: 2rem;
  }
  
  .health-declaration-container .page-header p {
    font-size: 1rem;
  }
  
  .health-declaration-form .form-section {
    padding: var(--hdm-space-6) var(--hdm-space-4);
  }
  
  .health-declaration-form .form-row {
    grid-template-columns: 1fr;
    gap: var(--hdm-space-4);
  }
  
  .health-declaration-form .vaccines-section {
    padding: var(--hdm-space-6) var(--hdm-space-4);
  }
  
  .health-declaration-form .vaccines-section h3 {
    font-size: 1.5rem;
  }
  
  .health-declaration-form .vaccines-grid {
    grid-template-columns: 1fr;
    gap: var(--hdm-space-4);
  }
  
  .health-declaration-form .vaccine-card {
    margin-bottom: var(--hdm-space-3);
  }
  
  .health-declaration-form .vaccine-card:hover {
    transform: translateY(-2px);
  }
  
  .health-declaration-form .vaccine-card.selected {
    transform: none;
  }
  
  .health-declaration-form .vaccine-card-header {
    padding: var(--hdm-space-4);
  }
  
  .health-declaration-form .vaccine-card-expanded {
    padding: var(--hdm-space-4);
  }
  
  .health-declaration-form .vaccine-name {
    font-size: 1rem;
  }
  
  .health-declaration-form .form-actions {
    flex-direction: column;
    gap: var(--hdm-space-3);
  }
  
  .health-declaration-form .submit-button {
    width: 100%;
  }
  
  .hdm-modal-content {
    margin: var(--hdm-space-4);
    padding: var(--hdm-space-6);
  }
  
  .hdm-modal-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .health-declaration-container {
    padding: var(--hdm-space-3);
  }
  
  .health-declaration-container .page-header {
    padding: var(--hdm-space-5);
  }
  
  .health-declaration-container .page-header h1 {
    font-size: 1.75rem;
  }
  
  .health-declaration-container .page-header p {
    font-size: 0.95rem;
  }
  
  .health-declaration-form .form-section {
    padding: var(--hdm-space-4);
  }
  
  .health-declaration-form .vaccines-section {
    padding: var(--hdm-space-4);
  }
  
  .health-declaration-form .vaccines-section h3 {
    font-size: 1.25rem;
  }
  
  .health-declaration-form .vaccines-grid {
    grid-template-columns: 1fr;
    gap: var(--hdm-space-3);
  }
  
  .health-declaration-form .vaccine-card {
    margin-bottom: var(--hdm-space-2);
  }
  
  .health-declaration-form .vaccine-card-header {
    padding: var(--hdm-space-3);
    gap: var(--hdm-space-3);
  }
  
  .health-declaration-form .vaccine-card-expanded {
    padding: var(--hdm-space-3);
  }
  
  .health-declaration-form .checkmark {
    width: 20px;
    height: 20px;
  }
  
  .health-declaration-form .vaccine-name {
    font-size: 0.95rem;
  }
  
  .health-declaration-form .vaccinated-badge {
    font-size: 0.7rem;
    padding: var(--hdm-space-1) var(--hdm-space-2);
  }
  
  .health-declaration-form .vaccine-card.fully-vaccinated {
    border-width: 1px;
  }
  
  .health-declaration-form .vaccine-card.vaccinated-from-server .vaccine-name {
    color: var(--hdm-success);
  }
  
  .health-declaration-form .vaccinated-badge {
    background: linear-gradient(135deg, var(--hdm-success), var(--hdm-success-light));
    color: var(--hdm-white);
    font-size: 0.7rem;
    font-weight: 600;
    padding: var(--hdm-space-1) var(--hdm-space-2);
    border-radius: var(--hdm-radius-xl);
    display: inline-flex;
    align-items: center;
    gap: var(--hdm-space-1);
    width: fit-content;
    box-shadow: var(--hdm-shadow-sm);
    animation: pulse 2s infinite;
  }
  
  .health-declaration-form .fully-vaccinated-badge {
    background: linear-gradient(135deg, var(--hdm-primary-blue), var(--hdm-primary-blue-light));
    color: var(--hdm-white);
    font-size: 0.7rem;
    font-weight: 600;
    padding: var(--hdm-space-1) var(--hdm-space-2);
    border-radius: var(--hdm-radius-xl);
    display: inline-flex;
    align-items: center;
    gap: var(--hdm-space-1);
    width: fit-content;
    box-shadow: var(--hdm-shadow-sm);
    animation: pulse 2s infinite;
  }
}

/* ============= PRINT STYLES ============= */
@media print {
  .health-declaration-container {
    background-color: white;
    box-shadow: none;
    padding: 0;
  }
  
  .health-declaration-form .form-actions,
  .health-declaration-form .submit-button {
    display: none;
  }
  
  .health-declaration-form .form-section {
    break-inside: avoid;
    box-shadow: none;
  }
  
  .health-declaration-form .vaccine-item {
    break-inside: avoid;
  }
}

/* ============= FOCUS MANAGEMENT ============= */
.health-declaration-form *:focus {
  outline: 2px solid var(--hdm-primary-blue);
  outline-offset: 2px;
}

.health-declaration-form input:focus,
.health-declaration-form select:focus,
.health-declaration-form textarea:focus {
  outline: none;
}

/* ============= HIGH CONTRAST MODE ============= */
@media (prefers-contrast: high) {
  :root {
    --hdm-primary-blue: #0056b3;
    --hdm-gray-300: #666666;
    --hdm-gray-600: #333333;
  }
}

/* ============= REDUCED MOTION ============= */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Add styles for the vaccine already taken notice */
.vaccine-already-taken-notice {
  background-color: #fff3e0;
  border-left: 4px solid #ff9800;
  padding: 10px 15px;
  margin-bottom: 15px;
  border-radius: 4px;
}

.vaccine-already-taken-notice p {
  margin: 0;
  color: #e65100;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.info-icon {
  margin-right: 8px;
  font-style: normal;
}

/* Toast styles */
.toast-close-button {
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s;
  padding: 0;
  margin: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.toast-close-button:hover {
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.2);
}

.Toastify__toast {
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.Toastify__toast-body {
  font-family: var(--hdm-font-family);
  font-size: 14px;
}

.Toastify__toast--success {
  background-color: #4caf50 !important;
}

.Toastify__toast--error {
  background-color: #f44336 !important;
}

.Toastify__toast--warning {
  background-color: #ff9800 !important;
}

.Toastify__toast--info {
  background-color: #2196f3 !important;
}

/* Header with reload button */
.hdm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-spacer {
  flex: 1;
}

.hdm-header h2 {
  margin: 0;
  color: var(--hdm-primary-blue);
  font-size: 1.8rem;
}

.reload-button {
  background-color: #f0f8ff;
  color: #1976d2;
  border: 1px solid #1976d2;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
}

.reload-button:hover {
  background-color: #e3f2fd;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.reload-button:active {
  transform: translateY(1px);
}

.reload-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.reload-icon {
  font-size: 16px;
  margin-right: 6px;
  display: inline-block;
}

.reload-button:hover .reload-icon {
  animation: spin 1s linear;
}

.reload-icon.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}