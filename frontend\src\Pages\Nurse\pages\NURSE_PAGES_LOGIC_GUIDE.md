# 🏥 Logic Guide - Nurse Pages Folders

Tài liệu này giải thích logic và chức năng của từng folder trong `Pages/Nurse/pages/`

---

## 📂 **1. Blog_co** - Qu<PERSON>n lý Blog & Bài viết Y tế

### 🎯 **Mục đích:**
Quản lý nội dung giáo dục y tế và bài viết cộng đồng

### 🏗️ **Cấu trúc:**
```
Blog_co/
├── BlogManagement.jsx     # Main component với tab navigation
├── posts/                 # Quản lý bài viết cộng đồng
│   ├── Posts.jsx         # Danh sách posts
│   ├── AddPost.jsx       # Thêm post mới
│   ├── EditPost.jsx      # Chỉnh sửa post
│   └── PostDetail.jsx    # Chi tiết post
├── health_articles/       # Quản lý bài viết y tế
│   ├── HealthArticles.jsx # Danh sách articles
│   ├── AddHealthArticle.jsx # Thêm article
│   └── EditHealthArticle.jsx # Chỉnh sửa article
└── index.jsx             # Export point
```

### 🔄 **Logic hoạt động:**
1. **Tab Navigation:** 2 tab chính - Posts và Health Articles
2. **CRUD Operations:** Create, Read, Update, Delete cho cả 2 loại content
3. **Image Upload:** Xử lý upload hình ảnh cho bài viết
4. **Category Management:** Phân loại bài viết theo chủ đề
5. **Context Integration:** Sử dụng `BlogContext` để quản lý state

### 📊 **Routes:**
- `/nurse/blog-management/posts` - Quản lý posts
- `/nurse/blog-management/health-articles` - Quản lý articles

---

## 📂 **2. HealthCheckups_co** - Quản lý Khám sức khỏe

### 🎯 **Mục đích:**
Quản lý các đợt khám sức khỏe và lịch hẹn khám

### 🏗️ **Cấu trúc:**
```
HealthCheckups_co/
├── HealthCheckupsMain.jsx    # Main component với routing
├── CheckupList/              # Quản lý đợt khám
│   ├── CheckupList.jsx      # Danh sách campaigns
│   ├── CampaignDetailPage.jsx # Chi tiết campaign
│   └── CreateCheckupFormModal.jsx # Tạo checkup mới
├── ScheduleConsultation/     # Quản lý lịch hẹn
│   ├── ScheduleConsultation.jsx # Danh sách appointments
│   ├── CheckupDetailModal.jsx   # Chi tiết appointment
│   └── ScheduleEditModal.jsx    # Chỉnh sửa lịch hẹn
└── index.js                  # Export point
```

### 🔄 **Logic hoạt động:**
1. **Campaign Management:** Tạo và quản lý các đợt khám sức khỏe
2. **Appointment Scheduling:** Lên lịch và quản lý các cuộc hẹn khám
3. **Student Assignment:** Gán học sinh vào các đợt khám
4. **Parent Notification:** Gửi thông báo cho phụ huynh
5. **Status Tracking:** Theo dõi trạng thái khám của từng học sinh

### 📊 **Routes:**
- `/nurse/health-checkups/campaign-list` - Danh sách đợt khám
- `/nurse/health-checkups/schedule-consultation` - Lịch hẹn khám

---

## 📂 **3. Inventory_co** - Quản lý Kho Y tế

### 🎯 **Mục đích:**
Quản lý thuốc men và vật tư y tế trong kho

### 🏗️ **Cấu trúc:**
```
Inventory_co/
├── InventoryMain.jsx      # Main component với table view
├── AddItem/               # Thêm vật phẩm mới
│   └── AddItem.jsx
├── EditItem/              # Chỉnh sửa vật phẩm
│   └── EditItem.jsx
├── DeleteItem/            # Xóa vật phẩm
│   └── DeleteItem.jsx
├── ViewDetails/           # Xem chi tiết vật phẩm
│   └── ViewDetailsItem.jsx
└── index.js               # Export point
```

### 🔄 **Logic hoạt động:**
1. **Inventory Management:** CRUD operations cho vật phẩm y tế
2. **Stock Tracking:** Theo dõi số lượng tồn kho
3. **Expiry Management:** Quản lý hạn sử dụng
4. **Category Classification:** Phân loại theo danh mục
5. **Low Stock Alerts:** Cảnh báo hết hàng
6. **Search & Filter:** Tìm kiếm và lọc vật phẩm

### 📊 **Features:**
- Thêm/sửa/xóa vật phẩm
- Theo dõi hạn sử dụng
- Báo cáo tồn kho
- Tìm kiếm nâng cao

---

## 📂 **4. MedicalEvents_co** - Quản lý Sự kiện Y tế

### 🎯 **Mục đích:**
Ghi nhận và quản lý các sự cố y tế xảy ra tại trường

### 🏗️ **Cấu trúc:**
```
MedicalEvents_co/
├── MedicalEventsMain.jsx     # Main wrapper component
├── MedicalIncidents/         # Quản lý sự cố y tế
│   ├── MedicalIncidentsList.jsx    # Danh sách sự cố
│   ├── MedicalIncidentDetailModal.jsx # Chi tiết sự cố
│   ├── MedicalIncidentAddModal.jsx    # Thêm sự cố mới
│   ├── MedicalIncidentUpdateModal.jsx # Cập nhật sự cố
│   └── DeleteConfirmModal.jsx         # Xác nhận xóa
└── index.js                  # Export point
```

### 🔄 **Logic hoạt động:**
1. **Incident Recording:** Ghi nhận sự cố y tế chi tiết
2. **Student Association:** Liên kết sự cố với học sinh
3. **Medication Tracking:** Theo dõi thuốc đã sử dụng
4. **Severity Classification:** Phân loại mức độ nghiêm trọng
5. **Follow-up Management:** Quản lý theo dõi sau sự cố
6. **Report Generation:** Tạo báo cáo sự cố

### 📊 **Data Fields:**
- Thông tin học sinh
- Loại sự cố
- Mức độ nghiêm trọng
- Thuốc đã sử dụng
- Hành động đã thực hiện

---

## 📂 **5. ReceiveMedicine_co** - Quản lý Duyệt Thuốc

### 🎯 **Mục đích:**
Duyệt và quản lý đơn thuốc từ phụ huynh

### 🏗️ **Cấu trúc:**
```
ReceiveMedicine_co/
├── ReceiveMedicine.jsx       # Main component với tab navigation
├── MedicineReceipts/         # Tab duyệt đơn thuốc
│   └── MedicineReceipts.jsx
├── MedicationHistory/        # Tab lịch sử dùng thuốc
│   └── MedicationHistory.jsx
└── index.js                  # Export point
```

### 🔄 **Logic hoạt động:**
1. **Request Review:** Xem xét đơn thuốc từ phụ huynh
2. **Approval Process:** Duyệt hoặc từ chối đơn thuốc
3. **Medication Administration:** Ghi nhận việc cho thuốc
4. **History Tracking:** Theo dõi lịch sử dùng thuốc
5. **Parent Communication:** Giao tiếp với phụ huynh về đơn thuốc
6. **Safety Checks:** Kiểm tra an toàn thuốc

### 📊 **Workflow:**
1. Phụ huynh gửi đơn thuốc
2. Y tá xem xét và duyệt
3. Cho thuốc theo đúng liều lượng
4. Ghi nhận vào lịch sử

---

## 📂 **6. StudentRecords_co** - Quản lý Hồ sơ Học sinh

### 🎯 **Mục đích:**
Quản lý thông tin y tế và hồ sơ sức khỏe của học sinh

### 🏗️ **Cấu trúc:**
```
StudentRecords_co/
├── StudentRecordsMain.jsx    # Main component
├── StudentList/              # Danh sách học sinh
├── StudentDetail/            # Chi tiết học sinh
└── HealthReports/            # Báo cáo sức khỏe
```

### 🔄 **Logic hoạt động:**
1. **Student Database:** Quản lý database học sinh
2. **Health Profile:** Hồ sơ sức khỏe chi tiết
3. **Medical History:** Lịch sử y tế
4. **Growth Tracking:** Theo dõi phát triển
5. **Report Generation:** Tạo báo cáo sức khỏe
6. **Search & Filter:** Tìm kiếm học sinh

---

## 📂 **7. Vaccination_co** - Quản lý Tiêm chủng

### 🎯 **Mục đích:**
Quản lý kế hoạch tiêm chủng và theo dõi sau tiêm

### 🏗️ **Cấu trúc:**
```
Vaccination_co/
├── VaccinationMain.jsx       # Main component với routing
├── CreateRecord/             # Tạo hồ sơ tiêm chủng
│   ├── CreateVaccinationRecord.jsx
│   ├── VaccinationPlanDetailPage.jsx
│   └── CreateRecordModal.jsx
├── PostMonitoring/           # Theo dõi sau tiêm
│   ├── PostVaccinationMonitoring.jsx
│   └── PostVaccinationDetailPage.jsx
└── index.js                  # Export point
```

### 🔄 **Logic hoạt động:**
1. **Vaccination Planning:** Lập kế hoạch tiêm chủng
2. **Record Creation:** Tạo hồ sơ tiêm cho học sinh
3. **Schedule Management:** Quản lý lịch tiêm
4. **Post-Vaccination Monitoring:** Theo dõi sau tiêm
5. **Adverse Event Tracking:** Theo dõi tác dụng phụ
6. **Completion Tracking:** Theo dõi hoàn thành tiêm chủng

### 📊 **Routes:**
- `/nurse/vaccination/create-record` - Tạo hồ sơ tiêm
- `/nurse/vaccination/monitoring` - Theo dõi sau tiêm

---

## 🔄 **Common Patterns Across All Folders:**

### **1. Main Component Pattern:**
- Mỗi folder có 1 main component làm entry point
- Main component handle routing và layout
- Export qua index.js file

### **2. Context Integration:**
- Tất cả đều sử dụng corresponding Context
- State management centralized
- API calls thông qua Context

### **3. Modal Pattern:**
- Sử dụng modals cho CRUD operations
- Consistent UI/UX across modules
- Form validation và error handling

### **4. Responsive Design:**
- Mobile-first approach
- Bootstrap components
- Custom CSS với namespace

### **5. Error Handling:**
- Try-catch blocks
- User-friendly error messages
- Loading states management
