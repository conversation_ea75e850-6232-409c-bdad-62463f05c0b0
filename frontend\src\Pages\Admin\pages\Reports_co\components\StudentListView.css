/* Student List View - Multi-Theme Support */
.reports-student-list-container {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
  padding: 24px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Theme-specific backgrounds */
.reports-student-list-container.theme-blue {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.reports-student-list-container.theme-green {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

.reports-student-list-container.theme-purple {
  background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
}

.reports-student-list-container.theme-orange {
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

.reports-student-list-container.theme-teal {
  background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
}

.reports-student-list-header-section {
  background: white;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.08);
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
}

.reports-student-list-header-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #2563eb, #1d4ed8);
}

/* Theme-specific header section styling */
.theme-blue .reports-student-list-header-section::before {
  background: linear-gradient(90deg, #3b82f6, #2563eb, #1d4ed8);
}

.theme-green .reports-student-list-header-section::before {
  background: linear-gradient(90deg, #10b981, #059669, #047857);
}

.theme-purple .reports-student-list-header-section::before {
  background: linear-gradient(90deg, #8b5cf6, #7c3aed, #6d28d9);
}

.theme-orange .reports-student-list-header-section::before {
  background: linear-gradient(90deg, #f59e0b, #d97706, #b45309);
}

.theme-teal .reports-student-list-header-section::before {
  background: linear-gradient(90deg, #14b8a6, #0d9488, #0f766e);
}

.reports-student-header-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.reports-student-list-title {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Theme-specific title gradients */
.theme-blue .reports-student-list-title {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.theme-green .reports-student-list-title {
  background: linear-gradient(135deg, #10b981, #047857);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.theme-purple .reports-student-list-title {
  background: linear-gradient(135deg, #8b5cf6, #6d28d9);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.theme-orange .reports-student-list-title {
  background: linear-gradient(135deg, #f59e0b, #b45309);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.theme-teal .reports-student-list-title {
  background: linear-gradient(135deg, #14b8a6, #0f766e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.reports-student-list-subtitle {
  color: #64748b;
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.reports-student-stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.reports-student-stat-item {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.08);
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.reports-student-stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.reports-student-stat-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(59, 130, 246, 0.15);
  border-color: #bfdbfe;
}

.reports-student-stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #007bff;
  color: white;
  font-size: 20px;
}

.reports-student-stat-content {
  flex: 1;
}

.reports-student-stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
}

.reports-student-stat-label {
  color: #6c757d;
  font-size: 14px;
}

.reports-student-list-controls-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.reports-student-list-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.reports-student-search-section {
  flex: 1;
}

.reports-student-search {
  position: relative;
  max-width: 400px;
}

.reports-student-search i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
}

.reports-student-search input {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.3s ease;
}

.reports-student-search input:focus {
  border-color: #007bff;
}

.reports-student-count {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
}

.reports-student-list-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Table Styles */
.reports-student-table-container {
  overflow-x: auto;
}

.reports-student-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  min-width: 800px;
}

.reports-student-table thead {
  background: #007bff;
  color: white;
}

.reports-student-table th,
.reports-student-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.reports-student-table th {
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.reports-student-table-row:hover {
  background: #f8f9fa;
}

.reports-student-table-stt {
  width: 60px;
  text-align: center;
  font-weight: 600;
  color: #6c757d;
}

.reports-student-table-id {
  min-width: 120px;
}

.reports-student-id-badge {
  background: #e9ecef;
  color: #495057;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.reports-student-table-name {
  min-width: 200px;
}

.reports-student-name-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reports-student-table-icon {
  color: #007bff;
  font-size: 14px;
}

.reports-student-name {
  font-weight: 500;
  color: #2c3e50;
}

.reports-student-table-class {
  min-width: 120px;
}

.reports-student-class-badge {
  background: #d4edda;
  color: #155724;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.reports-student-table-grade {
  min-width: 100px;
}

.reports-student-grade-badge {
  background: #fff3cd;
  color: #856404;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.reports-student-table-gender {
  min-width: 100px;
}

.reports-student-gender-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.reports-student-gender-badge.male {
  background: #cce5ff;
  color: #0056b3;
}

.reports-student-gender-badge.female {
  background: #fce4ec;
  color: #c2185b;
}

.reports-student-table-actions {
  min-width: 120px;
}

.reports-student-action-buttons {
  display: flex;
  gap: 8px;
}

.reports-student-action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 14px;
}

.reports-student-view-btn {
  background: #007bff;
  color: white;
}

.reports-student-view-btn:hover {
  background: #0056b3;
}

.reports-student-edit-btn {
  background: #28a745;
  color: white;
}

.reports-student-edit-btn:hover {
  background: #1e7e34;
}

.reports-student-delete-btn {
  background: #dc3545;
  color: white;
}

.reports-student-delete-btn:hover {
  background: #c82333;
}

/* Loading States */
.reports-student-list-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.reports-student-loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: reports-student-spin 1s linear infinite;
}

@keyframes reports-student-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.reports-student-loading-text {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin-top: 16px;
}

.reports-student-loading-subtext {
  font-size: 14px;
  color: #6c757d;
  margin-top: 8px;
}

.reports-student-list-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.reports-student-list-empty i {
  font-size: 48px;
  color: #6c757d;
  margin-bottom: 16px;
}

.reports-student-list-empty h3 {
  font-size: 20px;
  color: #2c3e50;
  margin-bottom: 8px;
}

.reports-student-list-empty p {
  color: #6c757d;
  margin: 0;
}

/* Responsive */
@media (max-width: 1024px) {
  .reports-student-stats-section {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
  
  .reports-student-list-controls {
    flex-direction: column;
    gap: 16px;
  }
  
  .reports-student-search {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .reports-student-list-container {
    padding: 16px;
  }
  
  .reports-student-list-header-section {
    padding: 16px;
  }
  
  .reports-student-list-title {
    font-size: 24px;
  }
  
  .reports-student-stats-section {
    grid-template-columns: 1fr;
  }
  
  .reports-student-table th,
  .reports-student-table td {
    padding: 8px 12px;
  }
  
  .reports-student-table-container {
    font-size: 14px;
  }
}
.student-list-header-section {
  background: #f8fafc;
  padding: 16px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.header-content-enhanced {
  display: flex;
  align-items: center;
  gap: 16px;
}

.student-list-title-enhanced {
  font-size: 1.5rem;
  font-weight: 600;
  font-family: var(--font-heading);
  color: #0ea5e9;
  margin: 0;
  letter-spacing: -0.025em;
}

.student-list-subtitle-enhanced {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
  font-weight: 400;
}

/* Stats Section */
.student-stats-section {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-left: 4px solid #3b82f6;
  padding-left: 20px;
  background: white;
  position: relative;
}

.stat-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: #f1f5f9;
}

.stat-item:last-child::after {
  display: none;
}

.stat-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  margin-right: 16px;
  font-size: 1.125rem;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  font-family: var(--font-heading);
  color: #1e293b;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 400;
  line-height: 1.4;
}

/* Controls Section */
.student-list-controls-section {
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #f1f5f9;
}

.student-list-controls-enhanced {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.search-section-enhanced {
  flex: 1;
  max-width: 300px;
}

.student-search-enhanced {
  position: relative;
}

.student-search-enhanced input {
  width: 100%;
  padding: 8px 12px 8px 32px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  transition: border-color 0.2s ease;
  font-weight: 400;
}

.student-search-enhanced input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.student-search-enhanced input::placeholder {
  color: #9ca3af;
}

.student-search-enhanced i {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  font-size: 0.8rem;
  pointer-events: none;
}

.stats-section-enhanced {
  display: flex;
  align-items: center;
}

.student-count-enhanced {
  background: #f8fafc;
  color: #374151;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* Student List Section */
.student-list-section {
  padding: 0 24px 24px 24px;
}

.student-list-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.student-list-item {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.student-list-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.student-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.student-name {
  font-size: 1rem;
  font-weight: 600;
  font-family: var(--font-heading);
  color: #1e293b;
}

.student-id-badge {
  background: #f1f5f9;
  color: #64748b;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.student-item-details {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 12px;
}

.student-detail-row {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
}

.student-detail-row i {
  width: 14px;
  color: #6b7280;
  font-size: 0.75rem;
  text-align: center;
  flex-shrink: 0;
}

.student-detail-text {
  font-size: 0.875rem;
  color: #475569;
  font-weight: 400;
}

.student-item-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.view-btn {
  background: #f0f9ff;
  color: #0ea5e9;
  border: 1px solid #e0f2fe;
}

.view-btn:hover {
  background: #e0f2fe;
  color: #0284c7;
}

.edit-btn {
  background: #fefce8;
  color: #ca8a04;
  border: 1px solid #fef3c7;
}

.edit-btn:hover {
  background: #fef3c7;
  color: #a16207;
}

.delete-btn {
  background: #fef2f2;
  color: #ef4444;
  border: 1px solid #fecaca;
}

.delete-btn:hover {
  background: #fecaca;
  color: #dc2626;
}

/* Loading and Empty States */
.student-list-loading-enhanced {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  text-align: center;
  padding: 32px;
  background: white;
}

.student-loading-spinner-enhanced {
  width: 32px;
  height: 32px;
  border: 2px solid #f3f4f6;
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.loading-text-enhanced {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
  margin-bottom: 2px;
}

.loading-subtext {
  font-size: 0.75rem;
  color: #9ca3af;
}

.student-list-empty-enhanced {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 160px;
  text-align: center;
  padding: 32px;
  background: #f8fafc;
  border: 1px dashed #cbd5e1;
  border-radius: 6px;
  margin: 20px 24px;
}

.student-list-empty-enhanced i {
  font-size: 2rem;
  color: #cbd5e1;
  margin-bottom: 12px;
}

.student-list-empty-enhanced h3 {
  font-size: 1rem;
  font-weight: 600;
  font-family: var(--font-heading);
  color: #64748b;
  margin: 0 0 6px 0;
}

.student-list-empty-enhanced p {
  margin: 0;
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.4;
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .student-list-header-section {
    padding: 12px 16px;
  }

  .header-content-enhanced {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .student-list-title-enhanced {
    font-size: 1.25rem;
  }

  .student-stats-section {
    padding: 16px;
    gap: 12px;
  }

  .stat-item {
    padding: 12px 0 12px 16px;
  }

  .stat-number {
    font-size: 1.75rem;
  }
  
  .student-list-controls-section {
    padding: 12px 16px;
  }
  
  .student-list-controls-enhanced {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .search-section-enhanced {
    max-width: none;
  }
  
  .stats-section-enhanced {
    justify-content: center;
  }

  .student-list-section {
    padding: 0 16px 16px 16px;
  }
  
  .student-list-items {
    gap: 8px;
  }
  
  .student-list-item {
    padding: 12px;
  }

  .student-item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
    margin-bottom: 8px;
  }

  .student-item-details {
    gap: 4px;
    margin-bottom: 8px;
  }

  .student-item-actions {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .student-list-header-section {
    padding: 12px;
  }
  
  .student-list-title-enhanced {
    font-size: 1.125rem;
  }
  
  .student-list-subtitle-enhanced {
    font-size: 0.75rem;
  }

  .student-stats-section {
    padding: 12px;
    gap: 8px;
  }

  .stat-item {
    padding: 8px 0 8px 12px;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .stat-label {
    font-size: 0.75rem;
  }
  
  .student-list-controls-section {
    padding: 12px;
  }
  
  .student-search-enhanced input {
    padding: 6px 10px 6px 28px;
    font-size: 0.8rem;
  }
  
  .student-search-enhanced i {
    left: 8px;
    font-size: 0.75rem;
  }

  .student-list-section {
    padding: 0 12px 12px 12px;
  }
  
  .student-list-item {
    padding: 10px;
  }

  .student-name {
    font-size: 0.875rem;
  }

  .student-id-badge {
    font-size: 0.7rem;
    padding: 2px 6px;
  }

  .student-detail-text {
    font-size: 0.75rem;
  }

  .action-btn {
    width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }
}

/* Admin History Toolbar */
.admin-history-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  gap: 24px;
  flex-wrap: wrap;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 24px 28px;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.admin-search-filter-group {
  display: flex;
  gap: 20px;
  flex: 1;
  min-width: 320px;
}

.admin-search-box {
  position: relative;
  flex: 2;
  max-width: 420px;
}

.admin-search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #3b82f6;
  font-size: 1.1rem;
  z-index: 1;
}

.admin-search-box input {
  width: 100%;
  padding: 14px 20px 14px 44px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  background: #ffffff;
  color: #1e293b;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-search-box input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.admin-search-box input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.admin-filter-dropdown {
  position: relative;
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 200px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-filter-dropdown:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.admin-filter-icon {
  position: absolute;
  left: 16px;
  color: #3b82f6;
  font-size: 1.1rem;
  z-index: 1;
}

.admin-filter-dropdown select {
  padding: 14px 20px 14px 44px;
  border: none;
  border-radius: 12px;
  background: transparent;
  font-size: 0.875rem;
  min-width: 200px;
  cursor: pointer;
  color: #1e293b;
  font-weight: 600;
}

.admin-filter-dropdown select:focus {
  outline: none;
}

.admin-toolbar-buttons {
  display: flex;
  gap: 16px;
  align-items: center;
}

.admin-results-count {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.admin-refresh-button {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 14px 24px;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  font-size: 0.875rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.admin-refresh-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
}

.admin-refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Pagination Styles */
.admin-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
  margin-top: 0;
}

.admin-pagination-info {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.admin-pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.admin-pagination-btn {
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  background: white;
  color: #64748b;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
  min-width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.admin-pagination-btn:hover:not(:disabled) {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #475569;
}

.admin-pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.admin-pagination-btn.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.admin-pagination-btn.active:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.admin-pagination-ellipsis {
  padding: 8px 4px;
  color: #9ca3af;
  font-size: 0.875rem;
}

@media (max-width: 768px) {
  .admin-pagination {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
  }

  .admin-pagination-controls {
    flex-wrap: wrap;
    justify-content: center;
  }
}