/* Back Button Component */
.reports-back-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #475569;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.reports-back-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s;
}

.reports-back-button:hover::before {
  left: 100%;
}

.reports-back-button:hover {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-color: #cbd5e1;
  color: #334155;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.reports-back-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.reports-back-button i {
  font-size: 1rem;
  transition: transform 0.2s ease;
}

.reports-back-button:hover i {
  transform: translateX(-2px);
}

/* Variants */
.reports-back-button.primary {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border-color: #3b82f6;
  color: white;
}

.reports-back-button.primary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  border-color: #2563eb;
  color: white;
}

.reports-back-button.secondary {
  background: linear-gradient(135deg, #64748b 0%, #475569 100%);
  border-color: #64748b;
  color: white;
}

.reports-back-button.secondary:hover {
  background: linear-gradient(135deg, #475569 0%, #334155 100%);
  border-color: #475569;
  color: white;
}

.reports-back-button.minimal {
  background: transparent;
  border: none;
  color: #64748b;
  padding: 8px 12px;
}

.reports-back-button.minimal:hover {
  background: #f8fafc;
  color: #334155;
}

/* Size variants */
.reports-back-button.small {
  padding: 6px 12px;
  font-size: 0.75rem;
}

.reports-back-button.large {
  padding: 12px 20px;
  font-size: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
  .reports-back-button {
    padding: 10px 14px;
    font-size: 0.875rem;
  }
  
  .reports-back-button.small {
    padding: 8px 12px;
    font-size: 0.75rem;
  }
  
  .reports-back-button.large {
    padding: 12px 18px;
    font-size: 0.875rem;
  }
}
