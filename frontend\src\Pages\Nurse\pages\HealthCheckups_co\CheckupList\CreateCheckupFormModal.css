/* ===== MODAL CONTAINER ===== */
.create-checkup-modal .modal-dialog {
  max-width: 1200px;
  margin: 1rem auto;
}

.create-checkup-modal .modal-content {
  border-radius: 20px;
  border: none;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

/* ===== MODAL HEADER ===== */
.create-checkup-modal .modal-header {
  background: linear-gradient(135deg, #015C92 0%, #2D82B5 100%);
  color: white;
  padding: 24px 32px;
  border-bottom: none;
  position: relative;
}

.create-checkup-modal .modal-header::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #10b981, #3b82f6, #8b5cf6, #f59e0b);
}

.create-checkup-modal .modal-title {
  font-weight: 700;
  font-size: 24px;
  color: white;
  display: flex;
  align-items: center;
  gap: 12px;
}

.create-checkup-modal .modal-title::before {
  content: '🏥';
  font-size: 28px;
}

.create-checkup-modal .btn-close {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  opacity: 1;
  transition: all 0.3s ease;
}

.create-checkup-modal .btn-close:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: rotate(90deg);
}

/* ===== MODAL BODY ===== */
.create-checkup-modal .modal-body {
  padding: 32px;
  max-height: 75vh;
  overflow-y: auto;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.create-checkup-modal .modal-body::-webkit-scrollbar {
  width: 8px;
}

.create-checkup-modal .modal-body::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.create-checkup-modal .modal-body::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #015C92, #2D82B5);
  border-radius: 4px;
}

.create-checkup-modal .modal-body::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #014a7a, #2570a0);
}

/* ===== FORM SECTIONS ===== */
.create-checkup-modal .form-section {
  background: white;
  padding: 28px;
  border-radius: 16px;
  margin-bottom: 24px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.create-checkup-modal .form-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #10b981, #3b82f6, #8b5cf6);
}

.create-checkup-modal .form-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.create-checkup-modal .form-section h5 {
  font-weight: 700;
  font-size: 20px;
  color: #1e293b;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Icons for each section */
.create-checkup-modal .form-section:nth-child(1) h5::before {
  content: '📋';
  font-size: 24px;
}

.create-checkup-modal .form-section:nth-child(2) h5::before {
  content: '📊';
  font-size: 24px;
}

.create-checkup-modal .form-section:nth-child(3) h5::before {
  content: '🔬';
  font-size: 24px;
}

.create-checkup-modal .form-section:nth-child(4) h5::before {
  content: '📝';
  font-size: 24px;
}

/* ===== FORM CONTROLS ===== */
.create-checkup-modal .form-label {
  font-weight: 600;
  font-size: 14px;
  color: #374151;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.create-checkup-modal .form-control,
.create-checkup-modal .form-select {
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 14px;
  transition: all 0.3s ease;
  background-color: #ffffff;
}

.create-checkup-modal .form-control:focus,
.create-checkup-modal .form-select:focus {
  border-color: #015C92;
  box-shadow: 0 0 0 3px rgba(1, 92, 146, 0.1);
  outline: none;
  transform: translateY(-1px);
}

.create-checkup-modal .form-control::placeholder {
  color: #9ca3af;
  font-style: italic;
}

.create-checkup-modal .form-control[readonly],
.create-checkup-modal .form-control:disabled {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-color: #cbd5e1;
  cursor: not-allowed;
  color: #64748b;
}

.create-checkup-modal .form-control.is-invalid {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.create-checkup-modal .invalid-feedback {
  font-size: 12px;
  font-weight: 500;
  color: #ef4444;
  margin-top: 6px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.create-checkup-modal .invalid-feedback::before {
  content: '';
  font-size: 14px;
}

/* ===== INFO TEXT STYLING ===== */
.create-checkup-modal .info-text p {
  margin: 0;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  color: #0f172a;
  border-left: 4px solid #015C92;
  font-size: 14px;
  transition: all 0.3s ease;
}

.create-checkup-modal .info-text p:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(1, 92, 146, 0.15);
}

.create-checkup-modal .info-text strong {
  color: #015C92;
  font-weight: 700;
}

/* ===== SPECIAL CHECKUP ITEMS ===== */
.create-checkup-modal .list-group-horizontal {
  gap: 8px;
  flex-wrap: wrap;
}

.create-checkup-modal .list-group-item.badge {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.create-checkup-modal .list-group-item.badge::before {
  content: '🔬';
  font-size: 14px;
}

.create-checkup-modal .list-group-item.badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.create-checkup-modal .text-muted {
  color: #64748b !important;
  font-style: italic;
  padding: 20px;
  text-align: center;
  background: #f8fafc;
  border-radius: 12px;
  border: 2px dashed #cbd5e1;
}

/* ===== MODAL FOOTER ===== */
.create-checkup-modal .modal-footer {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-top: 1px solid #e2e8f0;
  padding: 24px 32px;
  gap: 12px;
}

.create-checkup-modal .modal-footer .btn {
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  border: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  justify-content: center;
}

.create-checkup-modal .modal-footer .btn-secondary {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: white;
}

.create-checkup-modal .modal-footer .btn-secondary:hover {
  background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

.create-checkup-modal .modal-footer .btn-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.create-checkup-modal .modal-footer .btn-warning:hover {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.create-checkup-modal .modal-footer .btn-primary {
  background: linear-gradient(135deg, #015C92 0%, #2D82B5 100%);
  color: white;
}

.create-checkup-modal .modal-footer .btn-primary:hover {
  background: linear-gradient(135deg, #014a7a 0%, #2570a0 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(1, 92, 146, 0.3);
}

.create-checkup-modal .modal-footer .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* ===== LOADING SPINNER ===== */
.create-checkup-modal .spinner-border-sm {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .create-checkup-modal .modal-dialog {
    max-width: 95%;
    margin: 0.5rem auto;
  }
}

@media (max-width: 768px) {
  .create-checkup-modal .modal-header,
  .create-checkup-modal .modal-body,
  .create-checkup-modal .modal-footer {
    padding: 20px;
  }

  .create-checkup-modal .form-section {
    padding: 20px;
  }

  .create-checkup-modal .modal-title {
    font-size: 20px;
  }

  .create-checkup-modal .form-section h5 {
    font-size: 18px;
  }
}

/* ===== ANIMATIONS ===== */
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.create-checkup-modal .modal-content {
  animation: slideInDown 0.4s ease-out;
}

.create-checkup-modal .form-section {
  animation: fadeInUp 0.5s ease-out;
}

.create-checkup-modal .form-section:nth-child(1) { animation-delay: 0.1s; }
.create-checkup-modal .form-section:nth-child(2) { animation-delay: 0.2s; }
.create-checkup-modal .form-section:nth-child(3) { animation-delay: 0.3s; }
.create-checkup-modal .form-section:nth-child(4) { animation-delay: 0.4s; }

/* ===== FOCUS INDICATORS ===== */
.create-checkup-modal .form-control:focus + .form-label,
.create-checkup-modal .form-select:focus + .form-label {
  color: #015C92;
  transform: translateY(-2px);
}

/* ===== CUSTOM SCROLLBAR FOR TEXTAREA ===== */
.create-checkup-modal textarea.form-control {
  resize: vertical;
  min-height: 100px;
}

.create-checkup-modal textarea.form-control::-webkit-scrollbar {
  width: 6px;
}

.create-checkup-modal textarea.form-control::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.create-checkup-modal textarea.form-control::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.create-checkup-modal textarea.form-control::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}