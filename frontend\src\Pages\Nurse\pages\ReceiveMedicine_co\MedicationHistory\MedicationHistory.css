.medication-history-container {
  padding: 20px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.history-header h2 {
  font-size: 1.5rem;
  color: #2c3e50;
  margin: 0;
}

.search-filter-container {
  display: flex;
  gap: 15px;
}

.search-box {
  position: relative;
  width: 350px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #7f8c8d;
}

.search-input {
  width: 100%;
  padding: 10px 10px 10px 35px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.95rem;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.filter-section {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  color: #2c3e50;
  font-size: 0.9rem;
  font-weight: 500;
  white-space: nowrap;
}

.filter-input {
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  min-width: 120px;
}

.filter-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.btn-reset-filter {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  margin-left: auto;
  transition: background-color 0.2s;
}

.btn-reset-filter:hover {
  background-color: #c0392b;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 15px;
  border-radius: 4px;
  margin: 20px 0;
  text-align: center;
}

.no-data {
  padding: 30px;
  text-align: center;
  color: #7f8c8d;
  font-style: italic;
}

.history-table-container {
  overflow-x: auto;
}

.history-table {
  width: 100%;
  border-collapse: collapse;
}

.history-table th {
  background-color: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
  padding: 12px;
  text-align: left;
  border-bottom: 2px solid #e0e0e0;
  white-space: nowrap;
}

.history-table td {
  padding: 12px;
  border-bottom: 1px solid #e0e0e0;
  color: #2c3e50;
  vertical-align: middle;
}

.history-table tr:hover {
  background-color: #f8f9fa;
}

.scheduled-row {
  background-color: #e3f2fd;
}

.scheduled-row:hover {
  background-color: #bbdefb !important;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
  white-space: nowrap;
}

.status-badge.completed {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.scheduled {
  background-color: #cce5ff;
  color: #004085;
}

.btn-view {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-view:hover {
  background-color: #2980b9;
}

/* Custom styles to enhance Bootstrap */
.table th {
  font-weight: 600;
  white-space: nowrap;
}

.table td {
  vertical-align: middle;
}

.badge {
  font-weight: 500;
  font-size: 0.85rem;
}

/* Override Bootstrap pagination styles for better aesthetics */
.pagination .page-link {
  color: #0d6efd;
  border-radius: 0;
  transition: all 0.2s;
}

.pagination .page-link:focus {
  box-shadow: none;
}

.pagination .page-item.active .page-link {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

/* Improve form styles */
.form-control:focus,
.form-select:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Card hover effect */
.card {
  transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Button animations */
.btn {
  transition: all 0.2s;
}

.btn:hover {
  transform: translateY(-1px);
}

/* Status badge customizations */
.badge.bg-success {
  background-color: #198754 !important;
}

.badge.bg-danger {
  background-color: #dc3545 !important;
}

.badge.bg-warning {
  background-color: #fd7e14 !important; 
  color: white !important;
}

.badge.bg-dark {
  background-color: #212529 !important;
}

/* Image viewing styles */
.image-column {
  min-width: 120px;
  text-align: center;
}

.image-btn {
  font-size: 0.8rem;
  padding: 0.375rem 0.75rem;
  white-space: nowrap;
}

.no-image-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
}

.image-modal .modal-body img {
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
}

.image-modal .modal-body img:hover {
  transform: scale(1.02);
}

.image-error-placeholder {
  background-color: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 0.375rem;
  padding: 2rem;
  text-align: center;
  color: #6c757d;
}

/* Numbered Pagination Styles - Matching the image style */
.medication-history-container .pagination-container {
  margin-top: 1.5rem;
  padding: 1rem 0;
  border-top: 1px solid #dee2e6;
}

.medication-history-container .pagination-wrapper {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.medication-history-container .pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #dee2e6;
  background-color: #fff;
  color: #495057;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.medication-history-container .pagination-btn:not(:disabled):hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
  color: #495057;
}

.medication-history-container .pagination-btn:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.medication-history-container .pagination-numbers {
  margin: 0 0.5rem;
}

.medication-history-container .pagination-page {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #dee2e6;
  background-color: #fff;
  color: #495057;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  font-weight: 500;
}

.medication-history-container .pagination-page:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
  color: #495057;
}

.medication-history-container .pagination-page.active {
  background-color: #007bff;
  border-color: #007bff;
  color: #fff;
  font-weight: 600;
}

.medication-history-container .pagination-page.active:hover {
  background-color: #0056b3;
  border-color: #0056b3;
  color: #fff;
}

.medication-history-container .pagination-ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  color: #6c757d;
  font-size: 0.9rem;
  font-weight: bold;
}

/* Responsive pagination */
@media (max-width: 768px) {
  .medication-history-container .pagination-wrapper {
    padding: 0.25rem;
    gap: 1px !important;
  }

  .medication-history-container .pagination-btn,
  .medication-history-container .pagination-page {
    width: 35px;
    height: 35px;
    font-size: 0.8rem;
  }

  .medication-history-container .pagination-ellipsis {
    width: 35px;
    height: 35px;
    font-size: 0.8rem;
  }
}

@media (max-width: 576px) {
  .medication-history-container .pagination-btn,
  .medication-history-container .pagination-page {
    width: 32px;
    height: 32px;
    font-size: 0.75rem;
  }

  .medication-history-container .pagination-ellipsis {
    width: 32px;
    height: 32px;
    font-size: 0.75rem;
  }
}

/* Medication History Image Modal Styles - Bootstrap Integration */
.medication-history-image-modal .modal-dialog {
  max-width: 900px;
  margin: 2rem auto;
  transition: transform 0.3s ease-out;
}

.medication-history-image-modal .modal-content {
  border: none;
  border-radius: 15px;
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  background: #ffffff;
}

.medication-history-image-modal .modal-header {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 1.75rem 2rem;
  border-bottom: none;
  position: relative;
}

.medication-history-image-modal .modal-title {
  font-weight: 700;
  font-size: 1.4rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0;
}

.medication-history-image-modal .modal-title::before {
  content: "💊";
  font-size: 1.6rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.medication-history-image-modal .btn-close {
  filter: brightness(0) invert(1);
  opacity: 0.9;
  transition: all 0.2s ease;
  font-size: 1.1rem;
}

.medication-history-image-modal .btn-close:hover {
  opacity: 1;
  transform: scale(1.1);
}

.medication-history-image-modal .modal-body {
  padding: 2.5rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  text-align: center;
  min-height: 450px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
}

.medication-history-image-modal .confirmation-image-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 100%;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
  background: white;
  padding: 20px;
  margin: 0 auto;
  transition: transform 0.3s ease;
}

.medication-history-image-modal .confirmation-image-container:hover {
  transform: scale(1.02);
}

.medication-history-image-modal .confirmation-medicine-image {
  max-height: 450px;
  max-width: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 10px;
  transition: all 0.3s ease;
  display: block;
  margin: 0 auto;
  cursor: zoom-in;
}

.medication-history-image-modal .confirmation-medicine-image:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.medication-history-image-modal .image-description-text {
  margin-top: 1.5rem;
  padding: 1.25rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  color: #495057;
  font-size: 1rem;
  font-weight: 500;
  border: 1px solid #e9ecef;
}

.medication-history-image-modal .image-description-text::before {
  content: "📋";
  font-size: 1.2rem;
}

.medication-history-image-modal .no-confirmation-image-placeholder {
  padding: 4rem 2.5rem;
  color: #6c757d;
  background: white;
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 3px dashed #dee2e6;
  transition: all 0.3s ease;
}

.medication-history-image-modal .no-confirmation-image-placeholder:hover {
  border-color: #adb5bd;
  transform: translateY(-2px);
}

.medication-history-image-modal .no-confirmation-image-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  opacity: 0.6;
  color: #adb5bd;
}

.medication-history-image-modal .modal-footer {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-top: none;
  padding: 1.75rem 2rem;
  gap: 1.25rem;
  justify-content: center;
}

.medication-history-image-modal .btn {
  border-radius: 30px;
  padding: 0.875rem 2.5rem;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border: none;
  min-width: 120px;
}

.medication-history-image-modal .btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.medication-history-image-modal .btn-secondary {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  color: white;
}

.medication-history-image-modal .btn-secondary:hover {
  background: linear-gradient(135deg, #5a6268 0%, #343a40 100%);
}

.medication-history-image-modal .btn-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
}

.medication-history-image-modal .btn-primary:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
}

/* Loading animation for images */
.medication-history-image-modal .image-loading-state {
  position: relative;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.medication-history-image-modal .image-loading-state::after {
  content: "";
  position: absolute;
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #28a745;
  border-radius: 50%;
  animation: medication-history-spinner 1s linear infinite;
}

@keyframes medication-history-spinner {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error state for failed image loads */
.medication-history-image-modal .image-error-state {
  color: #dc3545;
  background: linear-gradient(135deg, #f8d7da 0%, #f1aeb5 100%);
  border: 2px solid #f5c6cb;
  padding: 1.5rem;
  border-radius: 12px;
  margin-top: 1rem;
  font-weight: 500;
}

.medication-history-image-modal .image-error-state::before {
  content: "⚠️";
  margin-right: 0.5rem;
}

/* Modal entrance animation */
.medication-history-image-modal.show .modal-dialog {
  animation: medication-history-modal-fade-in 0.4s ease-out;
}

@keyframes medication-history-modal-fade-in {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive design optimizations */
@media (max-width: 768px) {
  .medication-history-image-modal .modal-dialog {
    margin: 1rem;
    max-width: none;
  }
  
  .medication-history-image-modal .modal-header,
  .medication-history-image-modal .modal-body,
  .medication-history-image-modal .modal-footer {
    padding: 1.5rem;
  }
  
  .medication-history-image-modal .modal-title {
    font-size: 1.2rem;
  }
  
  .medication-history-image-modal .confirmation-medicine-image {
    max-height: 300px;
  }
  
  .medication-history-image-modal .confirmation-image-container {
    padding: 15px;
  }
  
  .medication-history-image-modal .no-confirmation-image-placeholder {
    padding: 3rem 2rem;
  }
}

@media (max-width: 576px) {
  .medication-history-image-modal .modal-dialog {
    margin: 0.75rem;
  }
  
  .medication-history-image-modal .modal-header,
  .medication-history-image-modal .modal-body,
  .medication-history-image-modal .modal-footer {
    padding: 1.25rem;
  }
  
  .medication-history-image-modal .btn {
    padding: 0.75rem 2rem;
    font-size: 0.9rem;
    min-width: 100px;
  }
  
  .medication-history-image-modal .confirmation-image-container {
    padding: 12px;
  }
  
  .medication-history-image-modal .confirmation-medicine-image {
    max-height: 250px;
  }
  
  .medication-history-image-modal .modal-title {
    font-size: 1.1rem;
  }
  
  .medication-history-image-modal .modal-title::before {
    font-size: 1.3rem;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .medication-history-image-modal .confirmation-medicine-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}
