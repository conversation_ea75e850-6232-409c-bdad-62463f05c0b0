/* ================================================
   MEDICAL RECORDS - SIMPLE INTEGRATION
   Import All Styles & Image Modal
   ================================================ */

/* Import Modern Minimalist Styles */
@import './main.css';        /* Variables, reset, base styles */
@import './layout.css';      /* Layout, header, sections */
@import './components.css';  /* Cards, buttons, tabs, forms */

/* ================================================
   IMAGE MODAL - SIMPLE & CLEAN
   ================================================ */
.image-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-4);
  background: rgba(0, 0, 0, 0.75);
  animation: fadeIn 0.2s ease-out;
}

.image-modal .modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
}

.image-modal .modal-content {
  position: relative;
  background: var(--color-white);
  border-radius: var(--radius-lg);
  max-width: 90%;
  max-height: 90%;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  animation: slideUp 0.3s ease-out;
}

.image-modal .modal-close {
  position: absolute;
  top: var(--space-3);
  right: var(--space-3);
  background: rgba(0, 0, 0, 0.7);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--color-white);
  z-index: 10;
  transition: all var(--transition);
}

.image-modal .modal-close:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}

.image-modal .modal-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  display: block;
}

.image-modal .image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-12);
  color: var(--color-gray-500);
}

.image-modal .image-error svg {
  font-size: var(--text-3xl);
  margin-bottom: var(--space-4);
}

/* ================================================
   PRINT STYLES - CLEAN
   ================================================ */
@media print {
  .medical-header,
  .print-section,
  .tab-navigation,
  .back-btn,
  .refresh-btn,
  .modal-close {
    display: none !important;
  }
  
  .medical-container {
    background: white !important;
  }
  
  .content-section,
  .chonhocsinhtabparent,
  .stat-card,
  .info-card {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
    break-inside: avoid;
  }
  
  body {
    font-size: 12px !important;
    color: #000 !important;
  }
  
  h1, h2, h3, h4, h5, h6 {
    color: #000 !important;
  }
}