/* Modern About Section - Professional Blue Theme */

:root {
  --primary-blue: #015C92;
  --secondary-blue: #2D82B5;
  --accent-blue: #428CD4;
  --light-blue: #88CDF6;
  --lightest-blue: #BCE6FF;
  --blue-gradient: linear-gradient(135deg, #015C92 0%, #2D82B5 25%, #428CD4 50%, #88CDF6 75%, #BCE6FF 100%);
  --blue-gradient-secondary: linear-gradient(135deg, #428CD4 0%, #88CDF6 50%, #BCE6FF 100%);
}

.about-section {
  padding: 120px 0;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #F0F9FF 0%, #E0F2FE 100%);
}

/* Container để tránh xung đột với global.css */
.about-container {
  max-width: 1360px;
  width: 100%;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

/* Background elements */
.about-bg-element {
  position: absolute;
  z-index: 1;
  pointer-events: none;
  opacity: 0.3;
}

.about-bg-circle {
  width: 500px;
  height: 500px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(1, 92, 146, 0.05) 0%, rgba(1, 92, 146, 0) 70%);
  bottom: -250px;
  left: -150px;
}

.about-bg-square {
  top: 10%;
  right: -100px;
  width: 300px;
  height: 300px;
  background-image: linear-gradient(45deg, rgba(1, 92, 146, 0.02) 25%, transparent 25%),
                    linear-gradient(-45deg, rgba(1, 92, 146, 0.02) 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, rgba(1, 92, 146, 0.02) 75%),
                    linear-gradient(-45deg, transparent 75%, rgba(1, 92, 146, 0.02) 75%);
  background-size: 20px 20px;
  transform: rotate(15deg);
}

.about-bg-dots {
  bottom: 20%;
  right: 15%;
  width: 180px;
  height: 180px;
  background-image: radial-gradient(circle, rgba(45, 130, 181, 0.15) 2px, transparent 2px);
  background-size: 18px 18px;
  border-radius: 50%;
}

/* Header styling */
.about-header {
  text-align: center;
  margin-bottom: 80px;
  position: relative;
}

.about-pre-title {
  display: inline-block;
  padding: 8px 20px;
  background-color: rgba(1, 92, 146, 0.1);
  color: var(--primary-blue);
  border-radius: 30px;
  font-size: 0.95rem;
  font-weight: 600;
  margin-bottom: 20px;
  letter-spacing: 1px;
  transform: translateY(20px);
  opacity: 0;
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.about-title {
  font-size: 2.75rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 25px;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.title-highlight {
  color: var(--primary-blue);
  position: relative;
  display: inline-block;
}

.title-highlight::after {
  content: '';
  position: absolute;
  bottom: 5px;
  left: 0;
  width: 100%;
  height: 8px;
  background-color: rgba(1, 92, 146, 0.2);
  z-index: -1;
  transform: skewX(-5deg);
}

.about-title-divider {
  height: 4px;
  width: 80px;
  background: var(--blue-gradient-secondary);
  margin: 0 auto 30px;
  border-radius: 2px;
  opacity: 0;
  transform: scaleX(0);
  transition: transform 1s ease;
}

/* Animation classes */
.about-animate.animated {
  opacity: 1;
  transform: translateY(0) scaleX(1);
}

/* Content layout */
.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 50px;
  align-items: center;
}

/* Image styling */
.about-image-column {
  position: relative;
  opacity: 0;
  transform: translateX(-30px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.about-image-wrapper {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(1, 92, 146, 0.15);
}

.about-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom right, rgba(1, 92, 146, 0.2), rgba(45, 130, 181, 0.1));
  z-index: 1;
  border-radius: 20px;
}

.about-img {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 20px;
  transition: transform 0.7s ease;
}

.about-image-wrapper:hover .about-img {
  transform: scale(1.05);
}

/* Image decoration */
.about-image-shape {
  position: absolute;
  z-index: 2;
}

.about-image-shape-1 {
  width: 120px;
  height: 120px;
  top: -30px;
  left: -30px;
  border-radius: 20px;
  background: var(--blue-gradient);
  opacity: 0.6;
  transform: rotate(-15deg);
}

.about-image-shape-2 {
  width: 80px;
  height: 80px;
  bottom: -20px;
  right: -20px;
  border-radius: 15px;
  background: var(--blue-gradient-secondary);
  opacity: 0.6;
  transform: rotate(15deg);
}

/* Experience badge */
.experience-badge {
  position: absolute;
  bottom: 30px;
  left: 30px;
  background: var(--blue-gradient);
  color: white;
  padding: 15px 25px;
  border-radius: 10px;
  box-shadow: 0 10px 20px rgba(1, 92, 146, 0.25);
  z-index: 3;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.experience-badge .years {
  font-size: 1.8rem;
  font-weight: 700;
  line-height: 1.2;
}

.experience-badge .text {
  font-size: 0.9rem;
  opacity: 0.9;
}

/* Achievements styling */
.achievements-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-top: 40px;
}

.achievement-item {
  background-color: #ffffff;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(1, 92, 146, 0.08);
  text-align: center;
  transition: transform 0.4s ease, box-shadow 0.4s ease;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
  border: 1px solid rgba(1, 92, 146, 0.1);
}

.achievement-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(1, 92, 146, 0.15);
  border-color: var(--accent-blue);
}

.achievement-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-blue);
  margin-bottom: 5px;
}

.achievement-label {
  font-size: 0.85rem;
  color: #64748b;
  line-height: 1.4;
}

/* Text content styling */
.about-text-column {
  color: #334155;
  position: relative;
}

.about-headline {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 25px;
  line-height: 1.3;
  position: relative;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.about-headline::after {
  content: '';
  position: absolute;
  top: -15px;
  left: 0;
  width: 50px;
  height: 3px;
  background: var(--blue-gradient-secondary);
  border-radius: 3px;
}

.about-paragraph {
  margin-bottom: 20px;
  line-height: 1.8;
  color: #475569;
  font-size: 1.05rem;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.text-highlight {
  color: var(--primary-blue);
  font-weight: 600;
}

/* Highlights styling with Professional Blue variations */
.about-highlights {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 25px;
  margin: 35px 0;
}

.highlight-item {
  display: flex;
  align-items: flex-start;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.highlight-item:nth-child(1) {
  --highlight-color: var(--primary-blue);
}

.highlight-item:nth-child(2) {
  --highlight-color: var(--secondary-blue);
}

.highlight-item:nth-child(3) {
  --highlight-color: var(--accent-blue);
}

.highlight-item:nth-child(4) {
  --highlight-color: var(--light-blue);
}

.highlight-icon-wrapper {
  width: 50px;
  height: 50px;
  min-width: 50px;
  border-radius: 10px;
  background-color: var(--highlight-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 15px rgba(1, 92, 146, 0.15);
}

.highlight-icon-wrapper::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  width: 70px;
  height: 70px;
  background: radial-gradient(circle at 10% 10%, rgba(255, 255, 255, 0.5), transparent 70%);
  opacity: 0.5;
}

.highlight-icon-wrapper i {
  color: white;
  font-size: 1.3rem;
}

.highlight-item:nth-child(4) .highlight-icon-wrapper i {
  color: var(--primary-blue);
}

.highlight-content {
  flex: 1;
}

.highlight-title {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 10px;
}

.highlight-description {
  font-size: 0.95rem;
  line-height: 1.5;
  color: #64748b;
}

/* Conclusion */
.about-conclusion {
  font-size: 1.1rem;
  color: #334155;
  font-style: italic;
  margin: 30px 0;
  padding-left: 20px;
  border-left: 3px solid var(--primary-blue);
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

/* Buttons */
.about-actions {
  display: flex;
  gap: 20px;
  margin-top: 35px;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.about-button {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 25px;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.about-button.primary {
  background: var(--blue-gradient);
  color: white;
  box-shadow: 0 8px 15px rgba(1, 92, 146, 0.25);
}

.about-button.primary:hover {
  box-shadow: 0 12px 20px rgba(1, 92, 146, 0.35);
  transform: translateY(-3px);
}

.about-button.secondary {
  background: rgba(1, 92, 146, 0.1);
  color: var(--primary-blue);
}

.about-button.secondary:hover {
  background: rgba(1, 92, 146, 0.15);
  transform: translateY(-3px);
}

.about-button i {
  font-size: 0.9rem;
  transition: transform 0.3s ease;
}

.about-button:hover i {
  transform: translateX(5px);
}

/* Certifications */
.about-certifications {
  margin-top: 50px;
  padding-top: 25px;
  border-top: 1px solid #e2e8f0;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.cert-label {
  display: block;
  margin-bottom: 15px;
  font-size: 0.9rem;
  color: #64748b;
}

.cert-logos {
  display: flex;
  gap: 25px;
  align-items: center;
  opacity: 0.7;
}

.cert-logo {
  max-width: 120px;
}

.cert-logo img {
  width: 100%;
  height: auto;
  transition: opacity 0.3s ease;
}

.cert-logo:hover img {
  opacity: 1;
}

/* Background shapes */
.about-shape {
  position: absolute;
  z-index: 0;
  opacity: 0.15;
  pointer-events: none;
}

.shape-1 {
  width: 600px;
  height: 600px;
  background: var(--blue-gradient);
  border-radius: 50%;
  filter: blur(80px);
  bottom: -300px;
  right: -200px;
}

.shape-2 {
  width: 400px;
  height: 400px;
  background: var(--blue-gradient-secondary);
  border-radius: 50%;
  filter: blur(60px);
  top: -200px;
  left: -100px;
}

/* Responsive design */
@media (max-width: 1200px) {
  .about-content {
    gap: 40px;
  }
  
  .about-highlights {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .about-title {
    font-size: 2.5rem;
  }
  
  .about-headline {
    font-size: 1.6rem;
  }
}

@media (max-width: 992px) {
  .about-section {
    padding: 100px 0;
  }
  
  .about-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  .about-image-column {
    order: 1;
    transform: translateY(-30px);
  }
  
  .about-text-column {
    order: 2;
  }
  
  .about-title {
    font-size: 2.2rem;
  }
  
  .about-header {
    margin-bottom: 60px;
  }
  
  .achievement-value {
    font-size: 1.6rem;
  }
}

@media (max-width: 768px) {
  .about-section {
    padding: 80px 0;
  }
  
  .about-header {
    margin-bottom: 50px;
  }
  
  .about-title {
    font-size: 2rem;
  }
  
  .about-highlights {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .about-headline {
    font-size: 1.5rem;
  }
  
  .about-paragraph {
    font-size: 1rem;
  }
  
  .about-actions {
    flex-direction: column;
  }
  
  .about-button {
    justify-content: center;
  }
  
  .cert-logos {
    flex-wrap: wrap;
    gap: 15px;
  }
}

@media (max-width: 576px) {
  .about-section {
    padding: 60px 0;
  }
  
  .about-pre-title {
    padding: 6px 16px;
    font-size: 0.85rem;
  }
  
  .about-title {
    font-size: 1.8rem;
  }
  
  .achievements-container {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .highlight-icon-wrapper {
    width: 45px;
    height: 45px;
    min-width: 45px;
  }
  
  .highlight-icon-wrapper i {
    font-size: 1.1rem;
  }
  
  .highlight-title {
    font-size: 1rem;
  }
  
  .highlight-description {
    font-size: 0.9rem;
  }
  
  .about-conclusion {
    font-size: 1rem;
  }
}