/* Parent Home Page Styles */

.parent-home {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* Remove any gaps between sections */
  gap: 0;
  margin: 0;
  padding: 0;
}

.parent-home-section {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  display: flex;
  justify-content: center;
  /* Remove margins to make sections adjacent */
  margin: 0;
  padding: 0;
}

.parent-home-section-vision {
  background: linear-gradient(135deg, #F0F9FF 0%, #E0F2FE 100%);
  position: relative;
}

.parent-home-section-vision::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(135deg, #428CD4 0%, #88CDF6 50%, #BCE6FF 100%);
}

/* Hero section styling - remove margins and padding */
.hero.modern-hero {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

/* Container for hero content */
.hero-container {
  width: 100%;
  max-width: 1520px; /* Match simple-parent-content max-width */
  margin: 0 auto;
  padding: 0 2rem;
  box-sizing: border-box;
}

/* Grid layout for hero content */
.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

/* Features section styling - remove padding and margin gaps */
.features-section {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
  position: relative;
  padding: 80px 0;
  margin: 0;
}

.features-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(135deg, #428CD4 0%, #88CDF6 50%, #BCE6FF 100%);
}

/* Container for section content */
.section-container {
  width: 100%;
  max-width: 1520px; /* Match simple-parent-content max-width */
  margin: 0 auto;
  padding: 0 2rem;
  box-sizing: border-box;
}

/* About section styling - remove gaps */
.about-section {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  background: #ffffff;
  padding: 80px 0;
  margin: 0;
}

/* Vision section styling - remove gaps */
.vision-section {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  padding: 80px 0;
  margin: 0;
}

/* CTA section styling - remove gaps */
.cta-section {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  background: linear-gradient(135deg, #015C92 0%, #2D82B5 25%, #428CD4 50%, #88CDF6 75%, #BCE6FF 100%);
  color: white;
  text-align: center;
  position: relative;
  padding: 80px 0;
  margin: 0;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .section-container {
    padding: 0 1.5rem;
  }
  
  .features-section,
  .about-section,
  .vision-section,
  .cta-section {
    padding: 60px 0;
  }
}

@media (max-width: 768px) {
  .section-container {
    padding: 0 1rem;
  }
  
  .features-section,
  .about-section,
  .vision-section,
  .cta-section {
    padding: 40px 0;
  }
}

@media (max-width: 480px) {
  .section-container {
    padding: 0 0.75rem;
  }
  
  .features-section,
  .about-section,
  .vision-section,
  .cta-section {
    padding: 30px 0;
  }
}