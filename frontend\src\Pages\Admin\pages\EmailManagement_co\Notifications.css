/* Notifications Manager Styles */
.notifications-manager {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  min-height: calc(100vh - 120px);
}

/* Header */
.notifications-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.notifications-header .header-icon {
  font-size: 2.5em;
  margin-right: 20px;
  background: rgba(255, 255, 255, 0.2);
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.notifications-header .header-content h2 {
  margin: 0 0 8px 0;
  font-size: 2.2em;
  font-weight: 600;
}

.notifications-header .header-content p {
  margin: 0;
  font-size: 1.1em;
  opacity: 0.9;
}

/* Alert Messages */
.notification-alert {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-radius: 12px;
  margin-bottom: 20px;
  animation: slideDown 0.3s ease-out;
}

.notification-alert.success {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border: 1px solid #c3e6cb;
  color: #155724;
}

.notification-alert.error {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.notification-alert .alert-icon {
  font-size: 1.5em;
  margin-right: 15px;
  flex-shrink: 0;
}

.notification-alert .alert-content h4 {
  margin: 0 0 4px 0;
  font-weight: 600;
}

.notification-alert .alert-content p {
  margin: 0;
  font-size: 0.95em;
}

/* Form Sections */
.form-section {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.form-section h3 {
  display: flex;
  align-items: center;
  margin: 0 0 20px 0;
  font-size: 1.3em;
  font-weight: 600;
  color: #2c3e50;
  padding-bottom: 10px;
  border-bottom: 2px solid #f8f9fa;
}

.form-section .section-icon {
  margin-right: 10px;
  color: #667eea;
  font-size: 1.1em;
}

/* Form Grid */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 500;
  color: #34495e;
  font-size: 0.95em;
}

.form-group .label-icon {
  margin-right: 8px;
  color: #667eea;
  font-size: 0.9em;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1em;
  transition: all 0.3s ease;
  background: #ffffff;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
  font-family: inherit;
}

.char-count {
  text-align: right;
  color: #6c757d;
  font-size: 0.85em;
  margin-top: 4px;
}

/* Type Icons */
.type-icon {
  margin-right: 8px !important;
}

.type-icon.info {
  color: #17a2b8;
}

.type-icon.warning {
  color: #ffc107;
}

.type-icon.urgent {
  color: #dc3545;
}

/* Recipients */
.recipients-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  align-items: start;
}

.delivery-methods h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 1em;
  font-weight: 600;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 10px;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
}

.checkbox-label:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #667eea;
  border-radius: 4px;
  margin-right: 10px;
  position: relative;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: #667eea;
  border-color: #667eea;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  font-size: 12px;
}

.method-icon {
  margin-right: 8px;
  color: #667eea;
}

/* Scheduling */
.scheduling-options {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.radio-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 12px 16px;
  border-radius: 8px;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
  background: #ffffff;
}

.radio-label:hover {
  border-color: #667eea;
  transform: translateY(-1px);
}

.radio-label input[type="radio"] {
  display: none;
}

.radio-mark {
  width: 20px;
  height: 20px;
  border: 2px solid #667eea;
  border-radius: 50%;
  margin-right: 10px;
  position: relative;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.radio-label input[type="radio"]:checked + .radio-mark {
  border-color: #667eea;
}

.radio-label input[type="radio"]:checked + .radio-mark::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 10px;
  height: 10px;
  background: #667eea;
  border-radius: 50%;
}

.schedule-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

/* Submit Button */
.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.submit-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1em;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.submit-button svg {
  margin-right: 8px;
  font-size: 1em;
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .notifications-manager {
    padding: 15px;
    margin: 10px;
  }

  .notifications-header {
    flex-direction: column;
    text-align: center;
    padding: 15px;
  }

  .notifications-header .header-icon {
    margin-right: 0;
    margin-bottom: 15px;
    width: 60px;
    height: 60px;
    font-size: 2em;
  }

  .form-grid,
  .recipients-grid,
  .schedule-inputs {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .scheduling-options {
    flex-direction: column;
    gap: 10px;
  }

  .form-section {
    padding: 20px 15px;
  }

  .submit-button {
    padding: 14px 30px;
    font-size: 1em;
    min-width: auto;
    width: 100%;
  }
}
