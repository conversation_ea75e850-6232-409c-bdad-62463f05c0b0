/* .admin-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #ffffff;
  height: 72px;
  padding: 0 32px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid #e2e8f0;
  width: 100%;
  z-index: 1000;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
}

.admin-header-left {
  display: flex;
  align-items: center;
}

.admin-header-logo {
  display: flex;
  align-items: center;
  margin-right: 30px;
}

.logo-icon {
  margin-right: 12px;
  color: #6366f1;
  font-size: 28px;
}

.logo-icon i {
  color: #6366f1;
}

.logo-text {
  display: flex;
  flex-direction: column;
}

.logo-text h1 {
  font-size: 1.5rem;
  color: #1a202c;
  margin: 0;
  font-weight: 700;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.logo-text span {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.admin-header-right {
  display: flex;
  align-items: center;
}

.header-notifications {
  position: relative;
  margin-right: 20px;
}

.notification-btn {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  color: #64748b;
  font-size: 1.25rem;
  position: relative;
  cursor: pointer;
  padding: 10px;
  border-radius: 12px;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.notification-btn:hover {
  background: #6366f1;
  color: white;
  border-color: #6366f1;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
}

.notification-btn.active {
  background: #6366f1;
  color: white;
  border-color: #6366f1;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.25);
}

.notification-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background: #ef4444;
  color: white;
  font-size: 11px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-weight: 600;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.notifications-dropdown {
  position: absolute;
  top: 55px;
  right: -10px;
  width: 320px;
  background-color: #fff;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  overflow: hidden;
  z-index: 100;
  border: 1px solid #eef0f2;
}

.dropdown-header {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dropdown-header h4 {
  margin: 0;
  font-size: 1rem;
  color: #333;
}

.notification-count {
  background-color: rgba(42, 127, 184, 0.1);
  color: #2A7FB8;
  padding: 3px 10px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
}

.notifications-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.notification-item:hover {
  background-color: #f5f9fc;
}

.notification-item.urgent .notification-icon {
  color: #FF5252;
}

.notification-item.info .notification-icon {
  color: #2196F3;
}

.notification-item.normal .notification-icon {
  color: #4CAF50;
}

.notification-icon {
  margin-right: 15px;
  font-size: 20px;
  display: flex;
  align-items: center;
}

.notification-content {
  flex: 1;
}

.notification-content h5 {
  margin: 0 0 5px;
  font-size: 0.9rem;
  color: #333;
}

.notification-content p {
  margin: 0;
  font-size: 0.8rem;
  color: #666;
  line-height: 1.4;
}

.notification-time {
  display: block;
  font-size: 0.7rem;
  color: #999;
  margin-top: 5px;
}

.dropdown-footer {
  padding: 12px;
  text-align: center;
  border-top: 1px solid #f0f0f0;
}

.dropdown-footer button {
  background: none;
  border: none;
  color: #2A7FB8;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.3s ease;
}

.dropdown-footer button:hover {
  color: #1A5E8C;
  text-decoration: underline;
}

.admin-header-dropdown-content {
  position: absolute;
  top: 60px;
  right: 0;
  width: 280px;
  background-color: #fff;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  overflow: hidden;
  z-index: 100;
  border: 1px solid #eef0f2;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #555;
  width: 100%;
  text-align: left;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  background-color: rgba(52, 152, 219, 0.08);
}

.dropdown-item i {
  color: #3498db;
  width: 20px;
  text-align: center;
  margin-right: 12px;
  font-size: 0.9rem;
}

.dropdown-item.logout {
  color: #e74c3c;
}

.dropdown-item.logout i {
  color: #e74c3c;
}

.notification-item.info .notification-icon {
  color: #3498db;
}

.notification-item.normal .notification-icon {
  color: #2ecc71;
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  margin-right: 15px;
  flex-shrink: 0;
}

.notification-content {
  flex: 1;
}

.notification-content h5 {
  margin: 0 0 5px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.admin-header-user {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-right: 10px;
}

.user-greeting {
  font-size: 0.7rem;
  color: #E6F3FA;
}

.user-name {
  font-weight: 600;
  color: #FFFFFF;
  font-size: 0.9rem;
}

.admin-header-profile {
  display: flex;
  align-items: center;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
}

.profile-avatar {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.4);
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-avatar i {
  color: white;
  font-size: 20px;
}

.dropdown-arrow {
  color: white;
  font-size: 12px;
  transition: transform 0.3s ease;
}

.notification-content p {
  margin: 0 0 5px 0;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.notification-time {
  font-size: 11px;
  color: #999;
}

.dropdown-footer {
  padding: 15px 20px;
  text-align: center;
  border-top: 1px solid #f0f0f0;
  background: #f8f9fa;
}

.dropdown-footer button {
  background: none;
  border: none;
  color: #667eea;
  font-weight: 600;
  cursor: pointer;
  font-size: 14px;
}

.admin-header-user {
  display: flex;
  align-items: center;
  gap: 15px;
  position: relative;
}

.user-info {
  text-align: right;
  color: #333;
}

.user-greeting {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.user-name {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #2a7fb8;
}

.admin-header-profile {
  display: flex;
  align-items: center;
  gap: 8px;
  background: transparent;
  border: none;
  font-size: 1.2rem;
  color: #555;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 5px;
  border-radius: 50px;
}

.admin-header-profile:hover,
.admin-header-profile.active {
  background: rgba(52, 152, 219, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(52, 152, 219, 0.3);
}

.profile-avatar {
  width: 46px;
  height: 46px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3498db, #2980b9);
  box-shadow: 0 2px 10px rgba(52, 152, 219, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.4);
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-avatar i {
  font-size: 22px;
  color: white;
}

.dropdown-arrow {
  color: #666;
  font-size: 12px;
  transition: transform 0.3s ease;
}

.admin-header-profile.active .dropdown-arrow {
  transform: rotate(180deg);
}

.admin-header-dropdown-content {
  position: absolute;
  top: 75px;
  right: 0;
  background-color: #ffffff;
  min-width: 240px;
  box-shadow: 0px 8px 20px rgba(0, 0, 0, 0.15);
  z-index: 1;
  border-radius: 10px;
  overflow: hidden;
  display: block;
  flex-direction: column;
  border: 1px solid rgba(233, 236, 239, 0.8);
}

.dropdown-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.dropdown-avatar i {
  font-size: 24px;
  color: white;
}

.dropdown-user-details h4 {
  margin: 0 0 5px 0;
  font-size: 16px;
  font-weight: 700;
  color: #2c3e50;
}

.dropdown-user-details p {
  margin: 0 0 5px 0;
  font-size: 13px;
  color: #666;
}

.user-role {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  display: inline-block;
}

.dropdown-user-info {
  display: flex;
  align-items: center;
  padding: 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.dropdown-avatar {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 15px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.dropdown-divider {
  height: 1px;
  background: #e9ecef;
  margin: 0;
}

.dropdown-item {
  width: 100%;
  padding: 12px 20px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  color: #495057;
  display: flex;
  align-items: center;
}

.admin-header-dropdown-content button:hover {
  background-color: #f1f1f1;
}

.admin-header-dropdown:hover .admin-header-dropdown-content {
  display: block;
}

.dropdown-item span {
  font-size: 14px;
  font-weight: 500;
} */