/* Combined CSS for MedicineReceipts with Bootstrap */

/* Custom styling for medicine receipts */
.medicine-receipts-container {
  padding: 1.25rem;
  position: relative;
  z-index: 1;
}

/* Ensure card and card-body allow dropdown overflow */
.medicine-receipts-container .card {
  overflow: visible !important;
}

.medicine-receipts-container .card-body {
  overflow: visible !important;
  position: relative;
  z-index: 1;
}

/* Fix dropdown z-index and table overflow issues */
.medicine-receipts-container .table-responsive {
  overflow: visible !important;
  position: relative;
  z-index: 1;
}

.medicine-receipts-container .table {
  overflow: visible !important;
  position: relative;
  z-index: 1;
}

.medicine-receipts-container .dropdown {
  position: relative;
  z-index: 1000;
}

.medicine-receipts-container .dropdown-menu {
  position: absolute !important;
  z-index: 9999 !important;
  top: 100% !important;
  left: auto !important;
  right: 0 !important;
  transform: none !important;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
  border: 1px solid rgba(0, 0, 0, 0.15);
  margin-top: 0.125rem;
  min-width: 10rem;
  background-color: white !important;
}

.medicine-receipts-container .dropdown-toggle {
  z-index: 1001;
}

/* Ensure table cell doesn't clip dropdown */
.medicine-receipts-container td {
  overflow: visible !important;
  position: relative;
}

/* Ensure the actions column has proper positioning */
.medicine-receipts-container tbody td:last-child {
  position: relative;
  z-index: 1000;
}

/* Fix for rows to allow dropdown to show above */
.medicine-receipts-container tbody tr {
  position: relative;
  z-index: 1;
}

/* When dropdown is open, increase z-index of the row */
.medicine-receipts-container tbody tr:has(.dropdown.show) {
  z-index: 1001;
}

/* Alternative fallback for browsers that don't support :has() */
.medicine-receipts-container tbody tr.dropdown-active {
  z-index: 1001;
}

/* Ensure dropdown menu always appears on top */
.medicine-receipts-container .dropdown-menu.show {
  display: block !important;
  position: absolute !important;
  z-index: 9999 !important;
  background-color: white !important;
  border: 1px solid rgba(0, 0, 0, 0.15) !important;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175) !important;
}

/* Prevent dropdown from being cut off by table boundaries */
.medicine-receipts-container .table thead,
.medicine-receipts-container .table tbody,
.medicine-receipts-container .table tfoot {
  position: relative;
  z-index: 1;
}

/* Ensure modal and other overlays don't interfere */
.medicine-receipts-container .dropdown-menu {
  pointer-events: auto !important;
}

/* Fix for Bootstrap table striped rows interfering with dropdown */
.medicine-receipts-container .table-striped > tbody > tr.dropdown-active {
  background-color: rgba(0, 123, 255, 0.05) !important;
}

/* Additional fix for Bootstrap table responsive behavior */
.medicine-receipts-container .table-responsive {
  -webkit-overflow-scrolling: touch;
  overflow-x: auto;
  overflow-y: visible !important;
}

/* Additional dropdown positioning fixes */
.medicine-receipts-container .dropdown.show .dropdown-menu {
  display: block !important;
  position: absolute !important;
  z-index: 1050 !important;
}

/* Handle dropdown in table row context */
.medicine-receipts-container tr {
  position: relative;
}

.medicine-receipts-container .dropdown-menu.show {
  animation: dropdownFadeIn 0.15s ease-in-out;
}

@keyframes dropdownFadeIn {
  0% {
    opacity: 0;
    transform: translateY(-5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Ensure proper stacking context */
.medicine-receipts-container {
  position: relative;
  z-index: 1;
}

/* Compact Date Range Filter Styles */
.medicine-receipts-container .date-range-filter {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.medicine-receipts-container .date-inputs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #f8f9fa;
  padding: 0.5rem;
  border-radius: 0.375rem;
  border: 1px solid #dee2e6;
}

.medicine-receipts-container .date-input {
  width: 150px;
  font-size: 0.875rem;
}

.medicine-receipts-container .date-separator {
  color: #6c757d;
  font-weight: 500;
  padding: 0 0.25rem;
}

.medicine-receipts-container .clear-dates-btn {
  width: 30px;
  height: 30px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  line-height: 1;
  border-radius: 50%;
}

.medicine-receipts-container .clear-dates-btn:hover {
  background-color: #dc3545;
  border-color: #dc3545;
  color: white;
}

/* Responsive date filter */
@media (max-width: 768px) {
  .medicine-receipts-container .date-inputs {
    flex-direction: column;
    gap: 0.25rem;
    padding: 0.375rem;
  }
  
  .medicine-receipts-container .date-input {
    width: 100%;
  }
  
  .medicine-receipts-container .date-separator {
    display: none;
  }
}

/* Status badge styling - using Bootstrap colors */
.status-badge {
  border-radius: 30px;
  font-size: 0.85rem;
  font-weight: 600;
  text-align: center;
  min-width: 120px;
}

/* Row status highlighting with Bootstrap-compatible colors */
tr.status-pending-row {
  background-color: rgba(255, 193, 7, 0.05);
}

tr.status-approved-row {
  background-color: rgba(19, 225, 67, 0.05);
}

tr.status-rejected-row {
  background-color: rgba(220, 53, 69, 0.05);
}

tr.status-cancelled-row {
  background-color: rgba(108, 117, 125, 0.05);
}



/* Cập nhật CSS cho badge */
.badge.bg-warning {
  background-color: #ffc107 !important;
  color: #212529;
}

.badge.bg-success {
  background-color: #28a745 !important;
  color: white;
}

.badge.bg-danger {
  background-color: #dc3545 !important;
  color: white;
}

.badge.bg-secondary {
  background-color: #6c757d !important;
  color: white;
}

.badge.bg-light {
  background-color: #f8f9fa !important;
  color: #212529;
  border: 1px solid #dee2e6;
}

/* Modal rộng hơn */
.modal-90w {
  max-width: 90%;
  width: 1200px;
}

/* Cải thiện hiển thị cho ListGroup */
.list-group-item {
  padding: 0.75rem 1.25rem;
}

/* Đảm bảo text không bị overflow và wrap đúng */
.table td {
  white-space: normal;
  vertical-align: middle;
  word-wrap: break-word;
  max-width: 300px;
}



/* Cải thiện hiển thị nội dung */ 
.card-body p {
  margin-bottom: 0;
  line-height: 1.6;
}

/* Tăng kích thước cho các tiêu đề card */
.card-header h5 {
  font-size: 1.1rem;
  margin-bottom: 0;
}

/* Cải thiện ListGroup responsive */
.list-group-item {
  display: flex;
  flex-wrap: wrap;
}

/* Arrow Navigation Pagination Styles */
.medicine-receipts-container .pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
  padding: 1rem 0;
  border-top: 1px solid #dee2e6;
}

.medicine-receipts-container .pagination-controls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.medicine-receipts-container .pagination-nav {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.medicine-receipts-container .pagination-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #dee2e6;
  background-color: #fff;
  color: #495057;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1rem;
}

.medicine-receipts-container .pagination-arrow:not(:disabled):hover {
  background-color: #0d6efd;
  border-color: #0d6efd;
  color: #fff;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.medicine-receipts-container .pagination-arrow:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.medicine-receipts-container .pagination-arrow:disabled:hover {
  transform: none;
  box-shadow: none;
}

.medicine-receipts-container .pagination-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #495057;
  font-size: 0.9rem;
  font-weight: 500;
}

.medicine-receipts-container .page-size-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #495057;
  font-size: 0.9rem;
}

.medicine-receipts-container .page-size-selector select {
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
  padding: 0.375rem 0.75rem;
  font-size: 0.9rem;
  color: #495057;
  background-color: #fff;
  min-width: 70px;
}

.medicine-receipts-container .page-size-selector select:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  outline: none;
}

.medicine-receipts-container .current-page-indicator {
  background-color: #e3f2fd;
  color: #0277bd;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 600;
  font-size: 0.9rem;
  min-width: 80px;
  text-align: center;
}

/* First/Last page buttons */
.medicine-receipts-container .pagination-arrow.first-last {
  font-size: 0.85rem;
  font-weight: 500;
  width: auto;
  padding: 0 0.75rem;
  min-width: 50px;
}

/* Responsive pagination */
@media (max-width: 768px) {
  .medicine-receipts-container .pagination-container {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .medicine-receipts-container .pagination-controls {
    justify-content: center;
    flex-wrap: wrap;
  }

  .medicine-receipts-container .pagination-info {
    text-align: center;
    font-size: 0.85rem;
  }

  .medicine-receipts-container .page-size-selector {
    justify-content: center;
  }

  .medicine-receipts-container .pagination-arrow {
    font-size: 0.9rem;
  }
}

@media (max-width: 576px) {
  .medicine-receipts-container .pagination-nav {
    gap: 0.25rem;
  }

  .medicine-receipts-container .pagination-arrow {
    width: 35px;
    height: 35px;
    font-size: 0.85rem;
  }

  .medicine-receipts-container .current-page-indicator {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    min-width: 70px;
  }
}

/* Medicine Image Column Styles */
.medicine-image-column {
  min-width: 120px;
  text-align: center;
}

.medicine-image-btn {
  font-size: 0.8rem;
  padding: 0.375rem 0.75rem;
  white-space: nowrap;
}

.no-medicine-image-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
}

.medicine-image-modal .modal-body img {
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
}

.medicine-image-modal .modal-body img:hover {
  transform: scale(1.02);
}

/* Responsive adjustments for medicine image column */
@media (max-width: 768px) {
  .medicine-image-column {
    min-width: 100px;
  }
  
  .medicine-image-btn {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
  }
  
  .no-medicine-image-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
    min-width: 70px;
  }
}

/* Medicine Parent Image Modal Styles - Bootstrap Integration */
.medicine-parent-image-modal .modal-dialog {
  max-width: 900px;
  margin: 2rem auto;
}

.medicine-parent-image-modal .modal-content {
  border: none;
  border-radius: 15px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.25);
  overflow: hidden;
}

.medicine-parent-image-modal .modal-header {
  background: linear-gradient(135deg, #17a2b8 0%, #3498db 100%);
  color: white;
  padding: 1.5rem 2rem;
  border-bottom: none;
}

.medicine-parent-image-modal .modal-title {
  font-weight: 600;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.medicine-parent-image-modal .modal-title::before {
  content: "📸";
  font-size: 1.5rem;
}

.medicine-parent-image-modal .btn-close {
  filter: brightness(0) invert(1);
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.medicine-parent-image-modal .btn-close:hover {
  opacity: 1;
}

.medicine-parent-image-modal .modal-body {
  padding: 2rem;
  background: #f8f9fa;
  text-align: center;
  min-height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1rem;
}

.medicine-parent-image-modal .parent-image-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 100%;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  background: white;
  padding: 15px;
  margin: 0 auto;
}

.medicine-parent-image-modal .parent-medicine-image {
  max-height: 500px;
  max-width: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
  transition: transform 0.3s ease;
  display: block;
  margin: 0 auto;
}

.medicine-parent-image-modal .parent-medicine-image:hover {
  transform: scale(1.02);
}

.medicine-parent-image-modal .image-info-text {
  margin-top: 1.5rem;
  padding: 1rem;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #6c757d;
  font-size: 0.95rem;
}

.medicine-parent-image-modal .image-info-text::before {
  content: "ℹ️";
}

.medicine-parent-image-modal .no-image-placeholder {
  padding: 3rem 2rem;
  color: #6c757d;
  background: white;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 2px dashed #dee2e6;
}

.medicine-parent-image-modal .no-image-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.medicine-parent-image-modal .modal-footer {
  background: #f8f9fa;
  border-top: none;
  padding: 1.5rem 2rem;
  gap: 1rem;
}

.medicine-parent-image-modal .btn {
  border-radius: 25px;
  padding: 0.75rem 2rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.medicine-parent-image-modal .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.medicine-parent-image-modal .btn-secondary {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  border: none;
}

.medicine-parent-image-modal .btn-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border: none;
}

/* Loading state */
.medicine-parent-image-modal .image-loading {
  position: relative;
}

.medicine-parent-image-modal .image-loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  margin: -20px 0 0 -20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: medicine-image-spin 1s linear infinite;
}

@keyframes medicine-image-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error state styling */
.medicine-parent-image-modal .image-error {
  color: #dc3545;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .medicine-parent-image-modal .modal-dialog {
    margin: 1rem;
    max-width: none;
  }
  
  .medicine-parent-image-modal .modal-header,
  .medicine-parent-image-modal .modal-body,
  .medicine-parent-image-modal .modal-footer {
    padding: 1rem 1.5rem;
  }
  
  .medicine-parent-image-modal .modal-title {
    font-size: 1.1rem;
  }
  
  .medicine-parent-image-modal .parent-medicine-image {
    max-height: 300px;
  }
  
  .medicine-parent-image-modal .parent-image-container {
    padding: 10px;
  }
}

@media (max-width: 576px) {
  .medicine-parent-image-modal .modal-dialog {
    margin: 0.5rem;
  }
  
  .medicine-parent-image-modal .modal-header,
  .medicine-parent-image-modal .modal-body,
  .medicine-parent-image-modal .modal-footer {
    padding: 1rem;
  }
  
  .medicine-parent-image-modal .btn {
    padding: 0.5rem 1.5rem;
    font-size: 0.9rem;
  }
  
  .medicine-parent-image-modal .parent-image-container {
    padding: 8px;
  }
  
  .medicine-parent-image-modal .parent-medicine-image {
    max-height: 250px;
  }
}
