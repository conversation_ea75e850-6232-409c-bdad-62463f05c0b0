/* CSS cho component DeleteItem */
.delete-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.delete-modal-container {
  background-color: white;
  border-radius: 8px;
  width: 450px;
  max-width: 90%;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  animation: shakeAnimation 0.5s;
}

@keyframes shakeAnimation {
  0% { transform: translateX(0); }
  10% { transform: translateX(-5px); }
  20% { transform: translateX(5px); }
  30% { transform: translateX(-5px); }
  40% { transform: translateX(5px); }
  50% { transform: translateX(0); }
}

.delete-modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffebee;
  border-radius: 8px 8px 0 0;
}

.delete-modal-header h3 {
  margin: 0;
  color: #d32f2f;
  font-size: 18px;
  font-weight: 600;
}

.delete-close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
  transition: color 0.2s;
}

.delete-close-btn:hover {
  color: #d32f2f;
}

.delete-modal-body {
  padding: 20px;
}

.delete-warning {
  background-color: #fff8e1;
  color: #ff8f00;
  padding: 12px;
  border-radius: 4px;
  margin: 15px 0;
  display: flex;
  align-items: center;
  font-weight: 500;
}

.delete-warning i {
  margin-right: 8px;
  font-size: 18px;
}

.item-details {
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 15px;
  margin: 15px 0;
}

.item-detail {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.item-detail:last-child {
  margin-bottom: 0;
}

.item-detail span:first-child {
  font-weight: 500;
  color: #333;
}

.delete-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.delete-cancel-btn,
.delete-confirm-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.delete-cancel-btn {
  background-color: #f5f5f5;
  color: #333;
}

.delete-cancel-btn:hover {
  background-color: #e5e5e5;
}

.delete-confirm-btn {
  background-color: #d32f2f;
  color: white;
}

.delete-confirm-btn:hover {
  background-color: #b71c1c;
}

/* Disabled button styles */
.delete-cancel-btn:disabled,
.delete-confirm-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}
