.schedule-consultation-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.schedule-consultation-container h2 {
  margin-bottom: 20px;
  color: #2c3e50;
  border-bottom: 2px solid #eaeaea;
  padding-bottom: 10px;
}

.required {
  color: #e74c3c;
}

.student-search-container {
  position: relative;
}

.student-search-results {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  background-color: white;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  z-index: 1000;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.student-search-item {
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #f0f0f0;
}

.student-search-item:hover {
  background-color: #f5f5f5;
}

.student-search-item:last-child {
  border-bottom: none;
}

.student-id {
  font-weight: bold;
  color: #666;
  width: 15%;
}

.student-name {
  flex-grow: 1;
}

.student-class {
  color: #888;
  width: 20%;
  text-align: right;
}

.selected-student-info {
  margin-top: 8px;
  padding: 8px;
  background-color: #e8f4fd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.consultations-table-container {
  max-height: 500px;
  overflow-y: auto;
}

.consultation-actions {
  display: flex;
  gap: 5px;
  justify-content: center;
}

.status-badge {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 500;
}

.status-scheduled {
  background-color: #e3f2fd;
  color: #1976d2;
}

.status-completed {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-cancelled {
  background-color: #ffebee;
  color: #c62828;
}

.status-no-show {
  background-color: #fafafa;
  color: #757575;
}

.medical-checkup-list-container {
  padding: 20px;
}

.medical-checkup-list-container h2 {
  margin-bottom: 20px;
  color: #2c3e50;
}

.filter-container {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
}

/* Status badge styles */
.badge {
  font-size: 0.85rem;
  padding: 6px 10px;
}

/* Table styles */
.table th {
  background-color: #f0f7ff;
  font-weight: 600;
}

.table td {
  vertical-align: middle;
}

/* Detail modal styles */
.checkup-detail h5 {
  color: #3498db;
  margin-bottom: 15px;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.checkup-detail p {
  margin-bottom: 8px;
}

.checkup-detail .table {
  margin-bottom: 0;
}

.checkup-detail .table th {
  width: 20%;
  background-color: #f8f9fa;
}

/* Edit form styles */
.edit-form .form-label {
  font-weight: 500;
}

.edit-form .form-control:focus,
.edit-form .form-select:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 0.25rem rgba(52, 152, 219, 0.25);
}

/* Action buttons */
.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .medical-checkup-list-container {
    padding: 10px;
  }
  
  .table-responsive {
    font-size: 0.9rem;
  }
  
  .btn-sm {
    padding: 0.2rem 0.4rem;
    font-size: 0.8rem;
  }
}

/* ========================================================================= */
/* CSS for ScheduleEditModal.jsx - Schedule Edit Modal Specific Styles */
/* ========================================================================= */

.schedule-edit-checkup-modal {
  z-index: 1055 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.schedule-edit-checkup-modal .modal-dialog {
  max-width: 90vw !important;
  width: 90vw !important;
  margin: 0 auto !important;
  position: relative !important;
  transform: none !important;
  left: auto !important;
  max-height: 90vh !important;
  height: auto !important;
}

.schedule-edit-checkup-modal.modal {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.schedule-edit-checkup-modal .modal-content {
  margin: 0 auto !important;
  width: 100% !important;
  position: relative !important;
  max-height: 90vh !important;
  display: flex !important;
  flex-direction: column !important;
}

.schedule-edit-modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  padding: 1rem 1.5rem;
}

.schedule-edit-modal-title {
  font-size: 1.25rem;
  font-weight: 500;
  margin: 0;
}

.schedule-edit-modal-body {
  padding: 1.5rem;
  max-height: 60vh !important;
  overflow-y: auto;
  flex: 1 1 auto !important;
}

.schedule-form-section {
  margin-bottom: 2rem;
  padding: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  background-color: #f8f9fa;
}

.schedule-form-section h5 {
  color: #495057;
  margin-bottom: 1rem;
  font-weight: 600;
}

.schedule-edit-modal-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
  padding: 1rem 1.5rem;
  justify-content: flex-end;
  flex-shrink: 0 !important;
  display: flex !important;
  align-items: center !important;
  min-height: 60px !important;
}

.schedule-save-btn {
  min-width: 140px;
  background-color: #28a745 !important;
  border-color: #28a745 !important;
  color: white !important;
  padding: 0.6rem 1.25rem !important;
  font-size: 0.9rem !important;
  font-weight: 500 !important;
  border-radius: 0.375rem !important;
  transition: all 0.2s ease !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.schedule-save-btn:hover {
  background-color: #218838 !important;
  border-color: #1e7e34 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3) !important;
}

.schedule-save-btn:focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

.schedule-save-btn:active {
  transform: translateY(0) !important;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3) !important;
}

/* Force modal to be visible */
#schedule-edit-modal.modal {
  display: block !important;
}

#schedule-edit-modal.modal.show {
  display: block !important;
}

#schedule-edit-modal .modal-backdrop {
  z-index: 1040 !important;
}

/* Ensure form fields are properly sized */
.schedule-edit-form .form-control,
.schedule-edit-form .form-select {
  margin-bottom: 1rem;
}

/* Responsive adjustments for Schedule Edit Modal */
@media (max-width: 768px) {
  .schedule-edit-checkup-modal .modal-dialog {
    max-width: 95vw !important;
    width: 95vw !important;
    margin: 1rem auto !important;
  }

  .schedule-edit-modal-body {
    padding: 1rem;
    max-height: 60vh;
  }
}

/* Additional centering for all screen sizes */
@media (min-width: 576px) {
  .schedule-edit-checkup-modal .modal-dialog {
    max-width: 90vw !important;
    width: 90vw !important;
    margin: 1.75rem auto !important;
  }
}

@media (min-width: 992px) {
  .schedule-edit-checkup-modal .modal-dialog {
    max-width: 80vw !important;
    width: 80vw !important;
  }
}

@media (min-width: 1200px) {
  .schedule-edit-checkup-modal .modal-dialog {
    max-width: 70vw !important;
    width: 70vw !important;
  }
}

/* Force center alignment with higher specificity */
.modal.schedule-edit-checkup-modal {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.modal.schedule-edit-checkup-modal .modal-dialog {
  margin: 0 !important;
  position: relative !important;
  max-height: none !important;
}

/* ========================================================================= */
/* CSS for CheckupDetailModal.jsx - Styles for CheckupDetailModal */
/* ========================================================================= */

.checkup-detail-modal .modal-header {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  color: #343a40;
}

.checkup-detail-modal .modal-title {
  font-weight: 600;
}

.checkup-detail-modal .modal-body {
  background-color: #f8f9fa;
  padding: 1.5rem;
}

.detail-card {
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  height: 100%;
}

.detail-card .card-header {
  background-color: #ffffff;
  font-weight: 600;
  color: #495057;
  border-bottom: 1px solid #e9ecef;
}

.detail-card .list-group-item {
  background-color: #ffffff;
  border-bottom: 1px solid #f1f3f5;
}

.detail-card .list-group-item:last-child {
  border-bottom: none;
}

.detail-badge {
  font-size: 0.85rem;
  padding: 0.4em 0.8em;
}

.checkup-detail-modal-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
}