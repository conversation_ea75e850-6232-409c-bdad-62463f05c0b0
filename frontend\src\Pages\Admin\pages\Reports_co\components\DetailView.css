/* Reports Detail View - Multi-Theme Support */
.reports-detail-view-container {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
  padding: 24px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Theme-specific backgrounds */
.reports-detail-view-container.theme-blue {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.reports-detail-view-container.theme-green {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

.reports-detail-view-container.theme-purple {
  background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
}

.reports-detail-view-container.theme-orange {
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

.reports-detail-view-container.theme-teal {
  background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
}

/* Loading State */
.detail-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.08);
  border: 1px solid #e2e8f0;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #f1f5f9;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: reports-detail-spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes reports-detail-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.detail-loading p {
  color: #64748b;
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

/* No Data State */
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.08);
  border: 1px solid #e2e8f0;
}

.no-data i {
  font-size: 64px;
  margin-bottom: 20px;
  color: #cbd5e1;
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.no-data p {
  color: #64748b;
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

/* Stats Header */
.reports-detail-stats-header {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.reports-detail-stats-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.08);
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.reports-detail-stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #2563eb);
}

/* Theme-specific stats card styling */
.theme-blue .reports-detail-stats-card::before {
  background: linear-gradient(90deg, #3b82f6, #2563eb);
}

.theme-green .reports-detail-stats-card::before {
  background: linear-gradient(90deg, #10b981, #059669);
}

.theme-purple .reports-detail-stats-card::before {
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.theme-orange .reports-detail-stats-card::before {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.theme-teal .reports-detail-stats-card::before {
  background: linear-gradient(90deg, #14b8a6, #0d9488);
}

.reports-detail-stats-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(59, 130, 246, 0.15);
  border-color: #bfdbfe;
}

/* Theme-specific hover effects */
.theme-blue .reports-detail-stats-card:hover {
  box-shadow: 0 8px 30px rgba(59, 130, 246, 0.15);
  border-color: #bfdbfe;
}

.theme-green .reports-detail-stats-card:hover {
  box-shadow: 0 8px 30px rgba(16, 185, 129, 0.15);
  border-color: #a7f3d0;
}

.theme-purple .reports-detail-stats-card:hover {
  box-shadow: 0 8px 30px rgba(139, 92, 246, 0.15);
  border-color: #ddd6fe;
}

.theme-orange .reports-detail-stats-card:hover {
  box-shadow: 0 8px 30px rgba(245, 158, 11, 0.15);
  border-color: #fde68a;
}

.theme-teal .reports-detail-stats-card:hover {
  box-shadow: 0 8px 30px rgba(20, 184, 166, 0.15);
  border-color: #99f6e4;
}

.reports-detail-stats-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.reports-detail-stats-icon.reports-detail-document-icon {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.reports-detail-stats-icon.reports-detail-user-icon {
  background: linear-gradient(135deg, #64748b, #475569);
}

.reports-detail-stats-icon.reports-detail-accept-icon {
  background: linear-gradient(135deg, #10b981, #059669);
}

.reports-detail-stats-icon.reports-detail-pending-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.reports-detail-stats-icon.reports-detail-reject-icon {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.reports-detail-stats-content {
  flex: 1;
}

.reports-detail-stats-number {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1;
  margin-bottom: 4px;
}

.reports-detail-stats-label {
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Notification Container */
.reports-detail-notification-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.08);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.reports-detail-table-title {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #1e293b;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  padding: 24px;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.reports-detail-table-title svg {
  color: #3b82f6;
  font-size: 18px;
}

/* Table Styles */
.reports-detail-table-container {
  overflow-x: auto;
  background: white;
}

.reports-detail-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  min-width: 1200px;
}

.reports-detail-table thead {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
}

.reports-detail-table th,
.reports-detail-table td {
  padding: 16px 20px;
  text-align: left;
  border-bottom: 1px solid #f1f5f9;
  vertical-align: middle;
}

.reports-detail-table th {
  font-weight: 600;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  white-space: nowrap;
}

.reports-detail-table-row {
  transition: all 0.2s ease;
}

.reports-detail-table-row:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  transform: scale(1.001);
}

.reports-detail-table-stt {
  width: 80px;
  text-align: center;
  font-weight: 700;
  color: #3b82f6;
  background: #eff6ff;
  border-radius: 8px;
  padding: 8px 12px !important;
}

.reports-detail-table-title {
  min-width: 280px;
}

.reports-detail-notification-title {
  font-weight: 600;
  color: #1e293b;
  line-height: 1.4;
  font-size: 15px;
}

.reports-detail-table-sender {
  min-width: 180px;
}

.reports-detail-sender-info {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.reports-detail-table-icon {
  color: #3b82f6;
  font-size: 16px;
  flex-shrink: 0;
}

.reports-detail-table-date {
  min-width: 180px;
}

.reports-detail-date-info {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  padding: 8px 12px;
  background: #f0fdf4;
  border-radius: 8px;
  border: 1px solid #bbf7d0;
  color: #166534;
}

.reports-detail-table-type {
  min-width: 140px;
}

.reports-detail-type-badge {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1e40af;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid #93c5fd;
  display: inline-block;
  text-align: center;
  min-width: 80px;
}

.reports-detail-table-recipients {
  width: 120px;
  text-align: center;
}

.reports-detail-recipients-count {
  font-weight: 700;
  color: #1e293b;
  background: #f1f5f9;
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  display: inline-block;
  min-width: 40px;
}

.reports-detail-table-accepted,
.reports-detail-table-pending,
.reports-detail-table-rejected {
  width: 120px;
  text-align: center;
}

.reports-detail-response-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 60px;
  justify-content: center;
  border: 1px solid;
}

.reports-detail-response-badge.reports-detail-accepted {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #166534;
  border-color: #86efac;
}

.reports-detail-response-badge.reports-detail-pending {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #92400e;
  border-color: #fcd34d;
}

.reports-detail-response-badge.reports-detail-rejected {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  color: #991b1b;
  border-color: #fca5a5;
}

.reports-detail-table-actions {
  width: 100px;
  text-align: center;
}

.reports-detail-action-button {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 12px;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 16px;
  margin: 0 auto;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.reports-detail-action-button:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

/* Responsive */
@media (max-width: 1024px) {
  .reports-detail-view-container {
    padding: 16px;
  }
  
  .reports-detail-stats-header {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
  }
  
  .reports-detail-stats-card {
    padding: 16px;
  }
  
  .reports-detail-table-container {
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .reports-detail-stats-header {
    grid-template-columns: 1fr;
  }

  .reports-detail-table th,
  .reports-detail-table td {
    padding: 12px 16px;
  }

  .reports-detail-table-container {
    font-size: 14px;
  }

  .reports-detail-notification-container {
    border-radius: 12px;
  }
}

@media (max-width: 480px) {
  .reports-detail-view-container {
    padding: 12px;
  }

  .reports-detail-stats-card {
    padding: 16px;
    gap: 16px;
  }

  .reports-detail-stats-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .reports-detail-stats-number {
    font-size: 24px;
  }

  .reports-detail-table-title {
    padding: 16px;
    font-size: 16px;
  }

  .reports-detail-table th,
  .reports-detail-table td {
    padding: 10px 12px;
    font-size: 13px;
  }
}

/* Stats Header */
.stats-header {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 30px;
}

.stats-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.2s ease;
}

.stats-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
}

.stats-icon.document-icon {
  background: #3b82f6;
}

.stats-icon.user-icon {
  background: #10b981;
}

.stats-icon.accept-icon {
  background: #059669;
}

.stats-icon.pending-icon {
  background: #d97706;
}

.stats-icon.reject-icon {
  background: #dc2626;
}

.stats-content {
  flex: 1;
}

.stats-number {
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 4px 0;
}

.stats-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

/* Notification Container */
.notification-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.notification-item {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 0;
  transition: all 0.2s ease;
  overflow: hidden;
}

.notification-item:hover {
  border-color: #d1d5db;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* Notification Title */
.notification-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 16px 0;
  line-height: 1.4;
  padding: 20px 24px 0 24px;
}

/* Notification Info */
.notification-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 20px;
  padding: 0 24px;
}

.info-section {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  flex: 1;
}

.info-tag {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

.info-tag svg {
  width: 14px;
  height: 14px;
  color: #6b7280;
}

.info-tag.sender {
  background: #eff6ff;
  color: #1d4ed8;
  border-color: #bfdbfe;
}

.info-tag.date {
  background: #f0fdf4;
  color: #166534;
  border-color: #bbf7d0;
}

.info-tag.type {
  background: #fefce8;
  color: #a16207;
  border-color: #fde68a;
}

/* Response Stats */
.response-stats {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  min-width: 200px;
  justify-content: flex-end;
}

.response-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 600;
  min-width: 50px;
  justify-content: center;
}

.response-badge.accepted {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.response-badge.pending {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

.response-badge.rejected {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

.response-badge svg {
  width: 14px;
  height: 14px;
}

/* Notification Footer */
.notification-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: #f8fafc;
  border-top: 1px solid #e5e7eb;
  margin-top: 0;
}

.recipient-count {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

.recipient-count::before {
  content: "👥";
  font-size: 1rem;
}

.detail-button {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.detail-button:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.detail-button svg {
  width: 16px;
  height: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .detail-view-container {
    padding: 16px;
  }

  .stats-header {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .stats-card {
    padding: 16px;
  }

  .stats-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .stats-number {
    font-size: 1.25rem;
  }

  .notification-container {
    gap: 16px;
  }

  .notification-title {
    font-size: 1.125rem;
    padding: 16px 20px 0 20px;
  }

  .notification-info {
    flex-direction: column;
    gap: 12px;
    padding: 0 20px;
  }

  .info-section {
    width: 100%;
  }

  .response-stats {
    width: 100%;
    justify-content: flex-start;
    min-width: auto;
  }

  .notification-footer {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    padding: 12px 20px;
  }

  .detail-button {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .detail-view-container {
    padding: 12px;
  }

  .stats-header {
    grid-template-columns: 1fr;
  }

  .stats-card {
    padding: 12px;
    gap: 12px;
  }

  .stats-icon {
    width: 36px;
    height: 36px;
    font-size: 0.875rem;
  }

  .stats-number {
    font-size: 1.125rem;
  }

  .notification-title {
    font-size: 1rem;
    padding: 12px 16px 0 16px;
  }

  .notification-info {
    padding: 0 16px;
  }

  .info-section {
    flex-direction: column;
    gap: 6px;
  }

  .response-stats {
    flex-direction: row;
    gap: 6px;
  }

  .notification-footer {
    padding: 12px 16px;
  }
} 