/* CreateHealthCampaign - Modern Health Theme */
.create-health-campaign {
  padding: 25px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background: linear-gradient(135deg, #f8fcff 0%, #e3f2fd 100%);
  min-height: 100vh;
}

/* Header Section */
.create-health-campaign-form-header h2{
  color: #fff;
}


.create-health-campaign-form-header {
  background: linear-gradient(135deg, #00b4d8 0%, #0077b6 50%, #023e8a 100%);
  color: white;
  padding: 40px 35px;
  border-radius: 20px;
  margin-bottom: 35px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 180, 216, 0.25);
  position: relative;
  overflow: hidden;
}

.create-health-campaign-form-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200px;
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: rotate(45deg);
}

.create-health-campaign-header-icon {
  font-size: 3.5rem;
  margin-bottom: 20px;
  color: #90e0ef;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.create-health-campaign-header-content h2 {
  margin: 0 0 15px 0;
  font-size: 2.2rem;
  font-weight: 700;
  letter-spacing: -0.02em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content p {
  margin: 0;
  font-size: 1.2rem;
  opacity: 0.95;
  font-weight: 400;
}

/* Notifications */
.health-campaign-notification {
  display: flex;
  align-items: center;
  gap:18px;
  padding: 18px 25px;
  border-radius: 15px;
  margin-bottom: 25px;
  font-weight: 500;
  border: none;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.health-campaign-notification::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background: currentColor;
}

.notification.success {
  background: linear-gradient(135deg, #d1f2eb 0%, #a7f3d0 100%);
  color: #047857;
  border-left: 4px solid #10b981;
}

.notification.error {
  background: linear-gradient(135deg, #fef2f2 0%, #fce7e7 100%);
  color: #dc2626;
  border-left: 4px solid #ef4444;
}

.notification-icon {
  font-size: 1.4rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.notification-content h4 {
  margin: 0 0 8px 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.notification-content p {
  margin: 0;
  font-size: 0.95rem;
  opacity: 0.9;
}

/* Form Container */
.campaign-form {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  margin-bottom: 30px;
  border: 1px solid rgba(0, 180, 216, 0.1);
  position: relative;
}

.campaign-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #00b4d8, #0077b6, #023e8a);
  border-radius: 20px 20px 0 0;
}

/* Form Sections */
.form-section {
  margin-bottom: 40px;
  position: relative;
}

.form-section:not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 1px;
  background: linear-gradient(90deg, transparent, #e0e7ff, transparent);
}

.form-section h3 {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 25px 0;
  font-size: 1.4rem;
  color: #1e293b;
  padding: 15px 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border-left: 4px solid #00b4d8;
  font-weight: 600;
}

.section-icon {
  color: #00b4d8;
  font-size: 1.2rem;
}
/* Form Grid */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 25px;
  margin-top: 10px;
}

/* Form Groups */
.health-campaign-form-group {
  margin-bottom: 25px;
}

.health-campaign-form-group.full-width {
  grid-column: 1 / -1;
}

.health-campaign-form-group label {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
  font-weight: 600;
  color: #374151;
  font-size: 1rem;
  letter-spacing: -0.01em;
}

.label-icon {
  color: #00b4d8;
  font-size: 1rem;
}

/* Form Inputs */
.health-campaign-form-group input,
.health-campaign-form-group select,
.health-campaign-form-group textarea {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-sizing: border-box;
  background: #ffffff;
  font-family: inherit;
}

.health-campaign-form-group input:focus,
.health-campaign-form-group select:focus,
.health-campaign-form-group textarea:focus {
  outline: none;
  border-color: #00b4d8;
  box-shadow: 0 0 0 3px rgba(0, 180, 216, 0.1);
  transform: translateY(-1px);
}

.health-campaign-form-group input:hover,
.health-campaign-form-group select:hover,
.health-campaign-form-group textarea:hover {
  border-color: #9ca3af;
}

.health-campaign-form-group input::placeholder,
.health-campaign-form-group textarea::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.health-campaign-form-group textarea {
  min-height: 120px;
  resize: vertical;
  line-height: 1.6;
}

.helper-text {
  font-size: 0.85rem;
  color: #6b7280;
  margin-top: 8px;
  font-style: italic;
  line-height: 1.4;
}

/* Special Checkup Items Section */
.current-items {
  margin-bottom: 25px;
  padding: 20px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  border: 1px solid #bae6fd;
}

.current-items h4 {
  margin: 0 0 15px 0;
  color: #0369a1;
  font-size: 1rem;
  font-weight: 600;
}

.items-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.item-tag {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 14px;
  background: white;
  border: 1px solid #00b4d8;
  border-radius: 20px;
  font-size: 0.9rem;
  color: #0369a1;
  font-weight: 500;
}

.remove-item {
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  padding: 2px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.remove-item:hover {
  background: #fef2f2;
  transform: scale(1.1);
}

.input-with-button {
  display: flex;
  gap: 12px;
}

.input-with-button input {
  flex: 1;
}

.add-button {
  padding: 16px 20px;
  background: linear-gradient(135deg, #00b4d8, #0077b6);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.add-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #0077b6, #023e8a);
  transform: translateY(-1px);
}

.add-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.predefined-items {
  margin-top: 20px;
}

.predefined-items h4 {
  margin: 0 0 15px 0;
  color: #374151;
  font-size: 1rem;
  font-weight: 600;
}

.predefined-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.predefined-item {
  padding: 10px 16px;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
  color: #374151;
}

.predefined-item:hover:not(:disabled) {
  border-color: #00b4d8;
  background: #f0f9ff;
  transform: translateY(-1px);
}

.predefined-item.selected,
.predefined-item:disabled {
  background: #e0f2fe;
  border-color: #00b4d8;
  color: #0369a1;
  cursor: not-allowed;
}

/* Submit Button */
.form-actions {
  text-align: center;
  margin-top: 40px;
  padding-top: 30px;
  border-top: 1px solid #e5e7eb;
}

.submit-button {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  padding: 18px 40px;
  background: linear-gradient(135deg, #00b4d8 0%, #0077b6 50%, #023e8a 100%);
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 24px rgba(0, 180, 216, 0.3);
  position: relative;
  overflow: hidden;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.submit-button:hover:not(:disabled)::before {
  left: 100%;
}

.submit-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #0077b6 0%, #023e8a 50%, #001d3d 100%);
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(0, 180, 216, 0.4);
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .create-health-campaign {
    padding: 20px 15px;
  }

  .form-header {
    padding: 30px 25px;
  }

  .header-content h2 {
    font-size: 1.8rem;
  }

  .campaign-form {
    padding: 25px 20px;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .form-section h3 {
    font-size: 1.2rem;
    padding: 12px 16px;
  }

  .input-with-button {
    flex-direction: column;
    gap: 10px;
  }

  .predefined-list {
    justify-content: center;
  }

  .submit-button {
    padding: 16px 32px;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .create-health-campaign {
    padding: 15px 10px;
  }

  .form-header {
    padding: 25px 20px;
  }

  .header-content h2 {
    font-size: 1.6rem;
  }

  .header-content p {
    font-size: 1rem;
  }

  .campaign-form {
    padding: 20px 15px;
  }

  .health-campaign-form-group input,
  .health-campaign-form-group select,
  .health-campaign-form-group textarea {
    padding: 14px 16px;
  }

  .submit-button {
    padding: 14px 28px;
    font-size: 0.95rem;
  }

  .items-list {
    justify-content: center;
  }

  .predefined-list {
    justify-content: center;
  }
} 