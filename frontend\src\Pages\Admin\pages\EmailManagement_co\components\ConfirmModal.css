/* Confirm Modal styling */

.confirm-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100;
  padding: 20px;
}

.confirm-modal {
  background: white;
  border-radius: 15px;
  width: 100%;
  max-width: 450px;
  box-shadow: 0 25px 70px rgba(0, 0, 0, 0.3);
  animation: confirmModalSlideIn 0.3s ease-out;
  overflow: hidden;
}

@keyframes confirmModalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.confirm-header {
  padding: 30px 30px 20px 30px;
  text-align: center;
  background: linear-gradient(135deg, #1a7bb5, #50c9c3);
  color: white;
}

.confirm-icon {
  width: 70px;
  height: 70px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px auto;
  font-size: 1.8rem;
  backdrop-filter: blur(10px);
}

.confirm-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.confirm-body {
  padding: 25px 30px;
}

.confirm-details p {
  margin: 0 0 8px 0;
  color: #333;
  line-height: 1.5;
  text-align: center;
}

.warning-header {
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  text-align: left;
}

.warning-header i {
  color: #ff6b6b;
  font-size: 1.2rem;
  margin-top: 2px;
  flex-shrink: 0;
}

.warning-header p {
  margin: 0;
  color: #d63031;
  font-weight: 500;
}

.user-details {
  background: #f8f9fa !important;
  border-radius: 10px;
  padding: 12px;
  margin: 8px 0 0 0;
  border-left: 4px solid #1a7bb5;
  display: block !important;
  visibility: visible !important;
}

.detail-item {
  display: flex !important;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
  font-size: 0.9rem;
  visibility: visible !important;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  color: #666;
  font-weight: 500;
}

.detail-value {
  color: #333;
  font-weight: 600;
  background: #e9ecef;
  padding: 4px 8px;
  border-radius: 4px;
}

.bulk-warning {
  background: rgba(26, 123, 181, 0.1);
  border: 1px solid rgba(26, 123, 181, 0.2);
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
  display: flex;
  align-items: center;
  gap: 12px;
  color: #1a7bb5;
}

.bulk-warning i {
  font-size: 1.2rem;
}

.warning-note {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 8px;
  padding: 12px;
  margin-top: 15px;
  display: flex;
  align-items: flex-start;
  gap: 10px;
  color: #856404;
  font-size: 0.9rem;
}

.warning-note i {
  color: #ffc107;
  margin-top: 2px;
  flex-shrink: 0;
}

.confirm-footer {
  display: flex;
  justify-content: center;
  gap: 15px;
  padding: 20px 30px 30px 30px;
  background: #f8f9fa;
}

.btn {
  padding: 12px 25px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  justify-content: center;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
}

.btn-danger {
  background: linear-gradient(135deg, #1a7bb5, #50c9c3);
  color: white;
}

.btn-danger:hover {
  background: linear-gradient(135deg, #146aa0, #21d4fd);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(26, 123, 181, 0.4);
}

.bulk-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
  text-align: center;
}

.bulk-info h4 {
  margin: 0 0 8px 0;
  color: #333;
}

.bulk-info .highlight {
  color: #1a7bb5;
  font-weight: 700;
  font-size: 1.1em;
}

.bulk-info p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.email-content-info {
  background: rgba(26, 123, 181, 0.05);
  border: 1px solid rgba(26, 123, 181, 0.2);
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
}

.email-content-info h4 {
  margin: 0 0 10px 0;
  color: #1a7bb5;
  font-size: 0.95rem;
}

.email-content-info ul {
  margin: 0;
  padding-left: 20px;
  color: #333;
}

.email-content-info li {
  margin-bottom: 4px;
  font-size: 0.9rem;
}

.final-warning {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.final-warning i {
  color: #ffc107;
  font-size: 1.1rem;
  margin-top: 2px;
  flex-shrink: 0;
}

.final-warning p {
  margin: 0;
  color: #856404;
  font-weight: 500;
  font-size: 0.9rem;
  text-align: left;
}

/* Enhanced confirmation modal styles */
.highlight {
  background: linear-gradient(135deg, #1a7bb5, #146aa0);
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 600;
}

.confirmation-steps {
  margin: 20px 0;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #1a7bb5;
}

.confirmation-steps h4 {
  color: #1a7bb5;
  margin-bottom: 10px;
  font-size: 1rem;
}

.confirmation-steps ol {
  margin: 0;
  padding-left: 20px;
}

.confirmation-steps li {
  margin-bottom: 8px;
  color: #555;
  line-height: 1.4;
}

.bulk-stats {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin: 15px 0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(26, 123, 181, 0.1);
  border: 2px solid #e9ecef;
}

.stat-number {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1a7bb5;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.85rem;
  color: #666;
  text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
  .confirm-modal-overlay {
    padding: 15px;
  }

  .confirm-modal {
    max-width: 100%;
  }

  .confirm-header {
    padding: 25px 20px 15px 20px;
  }

  .confirm-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  .confirm-header h3 {
    font-size: 1.2rem;
  }

  .confirm-body {
    padding: 20px;
  }

  .confirm-footer {
    padding: 15px 20px 25px 20px;
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }

  .detail-item {
    flex-direction: column;
    gap: 5px;
  }

  .detail-item strong {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .confirm-modal-overlay {
    padding: 10px;
  }

  .confirm-header {
    padding: 20px 15px 10px 15px;
  }

  .confirm-body {
    padding: 15px;
  }

  .confirm-footer {
    padding: 10px 15px 20px 15px;
  }

  .warning-note,
  .bulk-warning {
    font-size: 0.8rem;
    padding: 10px;
  }

  .user-details {
    padding: 12px;
  }
}

/* Improved confirm modal styles */
.confirm-question {
  font-size: 1rem;
  font-weight: 500;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.confirm-note {
  font-size: 0.9rem;
  color: #666;
  text-align: center;
  margin-top: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #1a7bb5;
}
