/* <PERSON><PERSON><PERSON> tiên đặt các @import */
@import url('https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;500;600;700&display=swap');

/* CSS Namespace prefixes - Thêm vào để đảm bảo không có xung đột CSS */
:root {
  --ns-admin: "admin";  /* Namespace cho Admin */
  --ns-nurse: "nurse";  /* Namespace cho Nurse */
  --ns-parent: "parent";  /* Namespace cho Parent */
  --ns-shared: "shared";  /* Namespace cho shared components */
}

/* Ensure no scroll conflicts with Parent layout */
body {
  position: relative !important;
  overflow-x: hidden;
  /* Remove any global scroll behavior that might conflict */
  scroll-behavior: auto;
  margin: 0;
  padding: 0;
}

/* Ensure simple parent layout works correctly */
.simple-parent-layout {
  scroll-behavior: auto !important;
}

/* Remove any layout padding conflicts */
.simple-parent-layout body {
  padding-top: 0 !important;
}

/* <PERSON><PERSON><PERSON> bảo các biến CSS chính toàn cục - Dark Blue Theme for Parent Pages */
:root {
  /* Primary Dark Blue Colors - Darker and more professional */
  --primary-color: #1e40af; /* Blue-700 - Xanh dương đen chính */
  --primary-hover: #1d4ed8; /* Blue-700 - Hover state */
  --primary-dark: #1e3a8a; /* Blue-800 - Dark variant */
  --primary-darker: #1e1b4b; /* Indigo-900 - Darkest variant */
  --primary-light: #3b82f6; /* Blue-500 - Light variant */
  --primary-bg: #eff6ff; /* Blue-50 - Background */
  --primary-border: #bfdbfe; /* Blue-200 - Borders */
  
  /* Secondary Colors */
  --secondary-color: #475569; /* Slate-600 */
  --secondary-light: #94a3b8; /* Slate-400 */
  --secondary-dark: #334155; /* Slate-700 */
  --accent-color: #1e3a8a; /* Blue-800 - Xanh dương đen nhấn */
  
  /* Gradient Variations - Darker blues */
  --primary-gradient: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  --primary-gradient-light: linear-gradient(135deg, var(--primary-bg), var(--primary-border));
  --blue-gradient-main: linear-gradient(135deg, #1e40af 0%, #1d4ed8 25%, #1e3a8a 50%, #1e1b4b 100%);
  --blue-gradient-dark: linear-gradient(135deg, #1e3a8a 0%, #1e1b4b 50%, #0f172a 100%);
  --blue-gradient-soft: linear-gradient(135deg, #eff6ff 0%, #dbeafe 50%, #bfdbfe 100%);
  
  /* Text colors */
  --text-primary: #1e3a8a; /* Blue-800 */
  --text-dark: #1f2937; /* Gray-800 */
  --text-medium: #4b5563; /* Gray-600 */
  --text-light: #6b7280; /* Gray-500 */
  
  /* Backgrounds */
  --background-light: #f8fafc; /* Slate-50 */
  --background-content: #f1f5f9; /* Slate-100 */
  --bg-white: #ffffff;
  --bg-blue-light: #eff6ff; /* Blue-50 */
  
  /* Status Colors */
  --success-color: #059669; /* Emerald-600 */
  --success-light: #d1fae5; /* Emerald-100 */
  --danger-color: #dc2626; /* Red-600 */
  --danger-light: #fee2e2; /* Red-100 */
  --warning-color: #d97706; /* Amber-600 */
  --warning-light: #fef3c7; /* Amber-100 */
  --info-color: #0284c7; /* Sky-600 */
  --info-light: #e0f2fe; /* Sky-100 */
  
  /* Borders */
  --border-light: #e2e8f0; /* Slate-200 */
  --border-medium: #cbd5e1; /* Slate-300 */
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-blue: 0 4px 20px rgba(30, 64, 175, 0.25);
  
  /* Border radius */
  --border-radius: 12px;
  --border-radius-large: 20px;
  --border-radius-small: 8px;
  
  /* Fonts */
  --font-heading: 'Be Vietnam Pro', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-body: 'Nunito', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  
  /* Transitions */
  --transition-fast: all 0.2s ease;
  --transition-normal: all 0.3s ease;
  --transition-slow: all 0.5s ease;
}

/* Đảm bảo không trùng CSS - thêm prefix vào các class thông dụng */
.p-app { /* parent app */
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.n-app { /* nurse app */
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.a-app { /* admin app */
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Parent content wrapper for consistent container sizing - HIGH PRIORITY */
.parent-content-wrapper {
  max-width: 1200px !important;
  margin: 0 auto !important;
  padding: 0 20px !important;
}

/* Responsive adjustments for parent wrapper - HIGH PRIORITY */
@media (max-width: 1280px) {
  .parent-content-wrapper {
    max-width: 1100px !important;
    padding: 0 16px !important;
  }
  
  /* Medical Records responsive */
  .parent-content-wrapper .medical-container,
  .parent-content-wrapper .medical-main,
  .parent-content-wrapper .header-content {
    max-width: 100% !important;
    width: 100% !important;
    padding: var(--space-4) !important;
  }
}

@media (max-width: 768px) {
  .parent-content-wrapper {
    max-width: 100% !important;
    padding: 0 12px !important;
  }
  
  /* Medical Records responsive */
  .parent-content-wrapper .medical-container,
  .parent-content-wrapper .medical-main,
  .parent-content-wrapper .header-content {
    max-width: 100% !important;
    width: 100% !important;
    padding: var(--space-3) !important;
  }
}

.parent-content-wrapper .health-guide-container,
.parent-content-wrapper .community-container {
  max-width: 100% !important;
  margin: 0 auto !important;
  padding: 20px !important;
}



/* Override specific containers that should be smaller - FORCE OVERRIDE */
.parent-content-wrapper .send-medicine-container,
/* .parent-content-wrapper .health-guide-container, */
/* .parent-content-wrapper .community-container, */
.parent-content-wrapper .introduction-page,
.parent-content-wrapper .sp-container,
.parent-content-wrapper .health-declaration-container,
.parent-content-wrapper .notifications-container,
.parent-content-wrapper .article-detail-container,
.parent-content-wrapper .community-post-container,
.parent-content-wrapper .contact-container,
.parent-content-wrapper .intro-banner-container,
.parent-content-wrapper .intro-content-container {
  max-width: 80% !important;
  margin: 0 auto !important;
  padding: 20px !important;
}

/* Medical Records specific overrides - Allow full width */
.parent-content-wrapper .medical-main {
  max-width: 1200px !important;
  width: 1200px !important;
  margin: 0 auto !important;
  padding: var(--space-4) 0 !important;
}

.parent-content-wrapper .header-content {
  max-width: 1200px !important;
  width: 1200px !important;
  margin: var(--space-4) auto 0 auto !important;
}

.parent-content-wrapper .medical-container {
  max-width: 1200px !important;
  width: 1200px !important;
  margin: 0 auto !important;
}

/* Specific smaller containers */
.parent-content-wrapper .contact-header-content {
  max-width: 70% !important;
  margin: 0 auto !important;
}

/* Modals and special elements */
.parent-content-wrapper .modal-content,
.parent-content-wrapper .med-modal,
.parent-content-wrapper .hdm-modal-content {
  max-width: 90% !important;
}

/* MedicalRecords modal sizing fixes */
.parent-content-wrapper .modern-modal-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 1000 !important;
}

.parent-content-wrapper .modern-modal-content {
  max-width: 800px !important;
  width: 90% !important;
  max-height: 90vh !important;
  margin: 0 auto !important;
  position: relative !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
}

/* Ensure SendMedicine modal overlays have proper z-index hierarchy */
.parent-content-wrapper .send-medicine-container .zoom-overlay {
  z-index: 2100 !important;
}

.parent-content-wrapper .incident-zoom-overlay {
  z-index: 2000 !important;
}

/* Toast notifications should appear above header */
.Toastify__toast-container {
  z-index: 9999 !important;
  top: 20px !important; /* Position relative to viewport */
  margin-top: 0 !important;
}

/* Ensure toasts are visible on simple parent layout */
.simple-parent-layout .Toastify__toast-container {
  position: fixed !important;
  top: 150px !important; /* Below simple header */
  z-index: 9999 !important;
}

/* Modal overlays should appear above header */
.med-modal-overlay,
.hdm-modal-overlay,
.modern-modal-overlay {
  z-index: 1050 !important;
}

/* Ensure header doesn't interfere with scrolling content */
body.parent-page {
  padding-top: 0 !important;
}

/* Fix z-index conflicts for parent pages */
.parent-header {
  z-index: 1000 !important;
}

/* Ensure content is properly spaced below header - handled dynamically in ParentLayout.jsx */

/* Health guide content and community content specific */
.parent-content-wrapper .health-guide-content {
  max-width: 100% !important;
  margin: 0 !important;
}

.parent-content-wrapper .community-container .global-container {
  max-width: 100% !important;
  padding: 0 !important;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f8fafc;
  font-size: 14px;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 700;
  color: var(--text-dark);
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}

.global-container {
  max-width: 1920px;
  margin: 0 auto;
  padding: 0 20px;
}

.section {
  padding: 60px 0;
}

.section-title {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 20px;
  text-align: center;
  background: var(--blue-gradient-main);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.section-subtitle {
  font-size: 18px;
  font-weight: 400;
  text-align: center;
  margin-bottom: 40px;
  color: var(--text-medium);
}

.global-btn {
  display: inline-block;
  padding: 12px 24px;
  border: none;
  border-radius: var(--border-radius-small);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-normal);
  font-family: var(--font-body);
}

.global-btn-primary {
  background: var(--blue-gradient-main);
  color: white;
  box-shadow: var(--shadow-blue);
}

.global-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(30, 64, 175, 0.35);
}

.global-btn-secondary {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.global-btn-secondary:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

.global-card {
  background: var(--bg-white);
  border-radius: var(--border-radius);
  padding: 30px;
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  border: 1px solid var(--border-light);
}

.global-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-blue);
}

/* Shared Layout Styles */
.shared-admin-layout {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.shared-basic-content {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 2rem;
}

.shared-error-layout {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--background-light);
}

.shared-error-content {
  text-align: center;
  padding: 2rem;
  background: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
}

.shared-auth-layout {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-gradient);
}

.shared-auth-content {
  width: 100%;
  max-width: 400px;
  padding: 2rem;
  background: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
}

/* Utility Classes */
.global-text-primary { color: var(--primary-color); }
.global-text-secondary { color: var(--secondary-color); }
.global-text-dark { color: var(--text-dark); }
.global-text-medium { color: var(--text-medium); }
.global-text-light { color: var(--text-light); }

.global-bg-primary { background-color: var(--primary-color); }
.global-bg-primary-light { background-color: var(--primary-bg); }
.global-bg-gradient { background: var(--blue-gradient-main); }

.global-border-primary { border-color: var(--primary-color); }
.global-border-light { border-color: var(--border-light); }

/* Thêm vào global.css */
.section + .section,
.section + footer,
section + footer {
  margin-top: 0;
}

footer {
  margin-top: 0;
}

@media (max-width: 768px) {
  .global-container {
    padding: 0 15px;
  }
  
  .section {
    padding: 40px 0;
  }
  
  .section-title {
    font-size: 28px;
  }
  
  /* Mobile responsive for modals */
  .parent-content-wrapper .modern-modal-content {
    width: 95% !important;
    max-width: 95% !important;
    margin: 10px !important;
    top: 0 !important;
    transform: none !important;
    max-height: 95vh !important;
  }
  
  .parent-content-wrapper .send-medicine-container .zoomed-image,
  .parent-content-wrapper .incident-zoomed-image {
    width: 90% !important;
    height: auto !important;
    max-height: 80vh !important;
  }
}
