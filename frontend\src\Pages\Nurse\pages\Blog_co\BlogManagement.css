/* BlogManagement.css */

.blog-management-wrapper {
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* Custom tab styling */
.nav-pills-custom .nav-link {
  color: #6c757d;
  font-weight: 500;
  padding: 12px 24px;
  margin-right: 2px;
  border-radius: 0;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.nav-pills-custom .nav-link:hover {
  color: #0d6efd;
  background-color: transparent;
  border-bottom-color: #0d6efd;
}

.nav-pills-custom .nav-link.active {
  color: #0d6efd;
  background-color: transparent;
  border-bottom-color: #0d6efd;
  font-weight: 600;
}

/* Card styling */
.blog-management-wrapper .card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
}

.blog-management-wrapper .card-header {
  padding: 1rem 1.5rem 0;
  background-color: #fff;
  border-bottom: 1px solid #e9ecef;
}

.blog-management-wrapper .card-body {
  padding: 0;
}

/* Tab content styling */
.blog-management-wrapper .tab-content {
  background-color: #fff;
  border-radius: 0 0 12px 12px;
}

.blog-management-wrapper .tab-pane {
  padding: 0;
}

/* Page header styling */
.blog-management-wrapper h2 {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
}

.blog-management-wrapper .text-muted {
  color: #6c757d !important;
  font-size: 0.95rem;
}

/* Loading spinner styling */
.blog-management-wrapper .spinner-border {
  width: 3rem;
  height: 3rem;
}

/* Error alert styling */
.blog-management-wrapper .alert {
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
}

/* Responsive design */
@media (max-width: 768px) {
  .blog-management-wrapper .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .nav-pills-custom .nav-link {
    padding: 10px 16px;
    font-size: 0.9rem;
  }
  
  .blog-management-wrapper h2 {
    font-size: 1.5rem;
  }
}

/* Icon styling */
.blog-management-wrapper .fas {
  font-size: 0.9rem;
}

/* Smooth transitions */
.blog-management-wrapper * {
  transition: all 0.2s ease;
}
