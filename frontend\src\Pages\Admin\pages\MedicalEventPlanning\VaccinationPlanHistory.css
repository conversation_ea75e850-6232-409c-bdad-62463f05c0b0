.admin-vaccination-plan-history {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* Header */
.admin-vac-plan-history-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 48px 40px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-radius: 20px;
  border: none;
  box-shadow: 0 10px 40px rgba(59, 130, 246, 0.2);
  position: relative;
  overflow: hidden;
}

.admin-vac-plan-history-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  transform: rotate(45deg);
}

.admin-vac-plan-history-header h2 {
  margin: 0 0 16px 0;
  font-size: 2.5rem;
  font-weight: 700;
  color: #ffffff;
  position: relative;
  z-index: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.admin-header-content {
  background-color: transparent !important;
  position: relative;
  z-index: 1;
}

.admin-vac-plan-history-header p {
  margin: 0;
  font-size: 1.125rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  position: relative;
  z-index: 1;
}

/* Statistics Row */
.admin-vac-plan-statistics-row {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20px;
  margin-bottom: 40px;
}

.admin-vac-plan-stat-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 24px 20px;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  min-width: 0;
  overflow: hidden;
}

.admin-vac-plan-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 0 4px 4px 0;
}

.admin-vac-plan-stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border-color: #3b82f6;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  border-radius: 12px 0 0 12px;
}

.stat-card.total::before {
  background: #3b82f6;
}

.stat-card.ongoing::before {
  background: #f59e0b;
}

.stat-card.completed::before {
  background: #10b981;
}

.stat-card.cancelled::before {
  background: #ef4444;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.admin-stat-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: relative;
  z-index: 1;
}

.admin-stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1e293b;
  line-height: 1;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.admin-stat-label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Error Banner */
.error-banner {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  color: #dc2626;
  font-size: 0.9rem;
  font-weight: 500;
}

.error-banner-icon {
  font-size: 1.1rem;
  flex-shrink: 0;
}

.error-close-btn {
  margin-left: auto;
  background: none;
  border: none;
  color: #dc2626;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.error-close-btn:hover {
  background: rgba(220, 38, 38, 0.1);
}

/* Toolbar */
.admin-history-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  gap: 24px;
  flex-wrap: wrap;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 24px 28px;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.toolbar-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.admin-search-filter-group {
  display: flex;
  gap: 20px;
  flex: 1;
  min-width: 320px;
}

.admin-search-box {
  position: relative;
  flex: 2;
  max-width: 420px;
}

.admin-search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #3b82f6;
  font-size: 1.1rem;
  z-index: 1;
}

.admin-search-box input {
  width: 100%;
  padding: 14px 20px 14px 44px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  background: #ffffff;
  color: #1e293b;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-search-box input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.admin-search-box input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.admin-filter-dropdown {
  position: relative;
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 200px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-filter-dropdown:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.admin-filter-icon {
  position: absolute;
  left: 16px;
  color: #3b82f6;
  font-size: 1.1rem;
  z-index: 1;
}

.admin-filter-dropdown select {
  padding: 14px 20px 14px 44px;
  border: none;
  border-radius: 12px;
  background: transparent;
  font-size: 0.875rem;
  min-width: 200px;
  cursor: pointer;
  color: #1e293b;
  font-weight: 600;
}

.admin-filter-dropdown select:focus {
  outline: none;
}

.admin-refresh-button {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 14px 24px;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  font-size: 0.875rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.admin-refresh-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
}

.admin-refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.admin-toolbar-buttons {
  display: flex;
  gap: 16px;
  align-items: center;
}

.admin-sort-button:hover {
  background: #f1f5f9 !important;
  border-color: #3b82f6 !important;
  color: #3b82f6 !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15) !important;
}

/* Loading, Error, No Data States */
.loading-section,
.error-section,
.no-data-section {
  text-align: center;
  padding: 60px 24px;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin: 16px 0;
}

.loading-section p,
.error-section p,
.no-data-section p {
  color: #64748b;
  font-size: 1rem;
  margin: 16px 0;
  font-weight: 400;
}

.error-section h3,
.no-data-section h3 {
  color: #1e293b;
  margin: 16px 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.error-icon,
.no-data-icon {
  font-size: 3rem;
  color: #cbd5e1;
  margin-bottom: 20px;
}

.retry-button {
  padding: 12px 24px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  margin-top: 16px;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.retry-button:hover {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* Plans Grid */
.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 24px;
  margin-top: 16px;
}

.plan-card {
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.2s ease;
  position: relative;
}

.plan-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: #3b82f6;
}

.plan-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #3b82f6;
}

.plan-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px 24px 16px 24px;
  border-bottom: 1px solid #f1f5f9;
}

.vaccine-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.vaccine-icon {
  color: #3b82f6;
  font-size: 1.25rem;
  padding: 8px;
  background: #eff6ff;
  border-radius: 8px;
}

.vaccine-info h3 {
  margin: 0;
  color: #1e293b;
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.3;
}

.plan-card-body {
  padding: 20px 24px;
}

.plan-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.meta-icon {
  color: #3b82f6;
  font-size: 1rem;
  flex-shrink: 0;
}

.plan-id {
  background: #f1f5f9;
  padding: 4px 8px;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.75rem;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.plan-description {
  color: #475569;
  line-height: 1.6;
  margin: 16px 0;
  font-size: 0.875rem;
  font-weight: 400;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 3px solid #3b82f6;
}

.plan-dates {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  font-size: 0.75rem;
  color: #64748b;
  margin-top: 16px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
}

.plan-card-footer {
  padding: 16px 24px 20px 24px;
  display: flex;
  gap: 12px;
}

.edit-button,
.delete-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  justify-content: center;
  font-size: 0.875rem;
}

.edit-button {
  background: #3b82f6;
  color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.edit-button:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.delete-button {
  background: #ef4444;
  color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.delete-button:hover {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* Status Badges */
.admin-status-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid;
}

.admin-status-waiting {
  background: #fef3c7;
  color: #d97706;
  border-color: #fbbf24;
}

.admin-status-progress {
  background: #dbeafe;
  color: #2563eb;
  border-color: #60a5fa;
}

.admin-status-completed {
  background: #d1fae5;
  color: #059669;
  border-color: #34d399;
}

.admin-status-canceled {
  background: #fee2e2;
  color: #dc2626;
  border-color: #f87171;
}

.admin-status-default {
  background: #f1f5f9;
  color: #64748b;
  border-color: #cbd5e1;
}

/* Status Badge for Dropdown */
.admin-status-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.2s ease;
  border: 2px solid;
}

.admin-status-badge.admin-clickable {
  cursor: pointer;
  user-select: none;
}

.admin-status-badge.admin-clickable:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.admin-status-badge.admin-status-waiting {
  background: #fef3c7;
  color: #92400e;
  border-color: #fbbf24;
}

.admin-status-badge.admin-status-waiting.admin-clickable:hover {
  background: #fde68a;
  border-color: #f59e0b;
}

.admin-status-badge.admin-status-progress {
  background: #dbeafe;
  color: #1e40af;
  border-color: #3b82f6;
}

.admin-status-badge.admin-status-progress.admin-clickable:hover {
  background: #bfdbfe;
  border-color: #2563eb;
}

.admin-status-badge.admin-status-completed {
  background: #d1fae5;
  color: #047857;
  border-color: #10b981;
}

.admin-status-badge.admin-status-completed.admin-clickable:hover {
  background: #a7f3d0;
  border-color: #059669;
}

.admin-status-badge.admin-status-canceled {
  background: #fee2e2;
  color: #b91c1c;
  border-color: #ef4444;
}

.admin-status-badge.admin-status-canceled.admin-clickable:hover {
  background: #fecaca;
  border-color: #dc2626;
}

/* Status Dropdown */
.admin-status-dropdown-container {
  position: relative;
  display: inline-block;
}

.admin-status-dropdown {
  position: fixed;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 9999;
  min-width: 160px;
  max-height: 200px;
  overflow-y: auto;
  animation: dropdownFadeIn 0.2s ease-out;
  padding: 4px 0;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.admin-status-dropdown-item {
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f3f4f6;
}

.admin-status-dropdown-item:last-child {
  border-bottom: none;
}

.admin-status-dropdown-item:hover {
  background: #f8fafc;
  transform: translateX(2px);
}

.admin-status-dropdown-item .admin-status-badge {
  margin: 0;
  font-size: 0.75rem;
  padding: 4px 10px;
}

/* Admin Error Banner */
.admin-error-banner {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  color: #dc2626;
  font-size: 0.9rem;
  font-weight: 500;
}

.admin-error-banner-icon {
  font-size: 1.1rem;
  flex-shrink: 0;
}

.admin-error-close-btn {
  margin-left: auto;
  background: none;
  border: none;
  color: #dc2626;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.admin-error-close-btn:hover {
  background: rgba(220, 38, 38, 0.1);
}

/* Admin Loading, Error, No Data States */
.admin-loading-section,
.admin-error-section,
.admin-no-data-section {
  text-align: center;
  padding: 60px 24px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  margin: 16px 0;
}

.admin-loading-section p,
.admin-error-section p,
.admin-no-data-section p {
  color: #64748b;
  font-size: 1rem;
  margin: 16px 0;
}

.admin-loading-section .admin-spinning,
.admin-error-section .admin-error-icon,
.admin-no-data-section .admin-no-data-icon {
  font-size: 3rem;
  color: #94a3b8;
  margin-bottom: 16px;
}

.admin-loading-section .admin-spinning.admin-large {
  font-size: 4rem;
}

.admin-retry-button {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 16px;
}

.admin-retry-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Admin Pagination Styles */
.admin-pagination-info {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
}

.admin-pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.admin-pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.admin-pagination-btn:hover:not(.admin-disabled) {
  background: #f3f4f6;
  border-color: #9ca3af;
  color: #1f2937;
}

.admin-pagination-btn.admin-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f9fafb;
}

.admin-pagination-pages {
  display: flex;
  align-items: center;
  gap: 4px;
  margin: 0 12px;
}

.admin-pagination-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0 8px;
  font-size: 0.875rem;
  font-weight: 500;
}

.admin-pagination-page:hover:not(.admin-active) {
  background: #f3f4f6;
  border-color: #9ca3af;
  color: #1f2937;
}

.admin-pagination-page.admin-active {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-color: #3b82f6;
  color: white;
  font-weight: 600;
}

.admin-pagination-ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  color: #9ca3af;
  font-weight: 500;
}

/* Time Status Styles */
.time-status {
  margin-top: 10px;
  display: flex;
  justify-content: flex-start;
}

.time-status-badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid;
}

.time-status-badge.normal {
  background: #d1fae5;
  border-color: #34d399;
}

.time-status-badge.urgent {
  background: #fef3c7;
  border-color: #fbbf24;
}

.time-status-badge.overdue {
  background: #fee2e2;
  border-color: #f87171;
}

.time-status-badge.past {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

/* Detail Modal */
.vac-plan-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.vac-plan-modal-content.detail-modal {
  background: #ffffff;
  border-radius: 12px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  border: 1px solid #e2e8f0;
}

.vac-plan-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px 20px 28px;
  border-bottom: 1px solid #f1f5f9;
  background: #f8fafc;
}

.vac-plan-modal-header h2 {
  margin: 0;
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #64748b;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: #f1f5f9;
  color: #1e293b;
}

.vac-plan-modal-body {
  padding: 24px 28px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.detail-item {
  background: #f8fafc;
  padding: 16px;
  border-radius: 8px;
  border-left: 3px solid #3b82f6;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-item label {
  display: block;
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 6px;
  font-size: 0.875rem;
}

.detail-item span {
  color: #64748b;
  font-size: 1rem;
  font-weight: 400;
}

.description-content {
  background: #ffffff;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  line-height: 1.6;
  color: #475569;
  font-size: 0.875rem;
}

.json-preview h4 {
  margin: 24px 0 12px 0;
  color: #1e293b;
  font-size: 1rem;
  font-weight: 600;
}

/* Pagination Styles */
.admin-pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-top: 1px solid #e2e8f0;
  border-radius: 0 0 16px 16px;
}

.pagination-info {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #374151;
  font-size: 0.875rem;
}

.pagination-btn:hover:not(.disabled) {
  background: #f3f4f6;
  border-color: #9ca3af;
  color: #1f2937;
}

.pagination-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f9fafb;
}

.pagination-pages {
  display: flex;
  align-items: center;
  gap: 4px;
  margin: 0 12px;
}

.pagination-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  padding: 0 8px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
}

.pagination-page:hover:not(.active) {
  background: #f3f4f6;
  border-color: #9ca3af;
  color: #1f2937;
}

.pagination-page.active {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-color: #3b82f6;
  color: white;
  font-weight: 600;
}

.pagination-ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  color: #9ca3af;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Animations */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.spinning {
  animation: spin 1s linear infinite;
}

.spinning.large {
  font-size: 3rem;
  color: #74b9ff;
}

/* Edit Modal */
.vac-plan-modal-content.edit-modal {
  max-width: 600px;
  width: 90%;
}

.vac-plan-form-group {
  margin-bottom: 25px;
}

.vac-plan-form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2d3436;
  font-size: 1rem;
}

.vac-plan-form-group input,
.vac-plan-form-group select,
.vac-plan-form-group textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f8f9fa;
  color: #2d3436;
  font-family: inherit;
}

.vac-plan-form-group input:focus,
.vac-plan-form-group select:focus,
.vac-plan-form-group textarea:focus {
  outline: none;
  border-color: #74b9ff;
  background: white;
  box-shadow: 0 0 0 4px rgba(116, 185, 255, 0.1);
}

.vac-plan-form-group textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.vac-plan-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #f1f5f9;
  background: #f8fafc;
}

.cancel-button,
.save-button,
.delete-confirm-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.cancel-button {
  background: #ffffff;
  color: #64748b;
  border: 1px solid #d1d5db;
}

.cancel-button:hover {
  background: #f8fafc;
  color: #1e293b;
  border-color: #9ca3af;
}

.save-button {
  background: #10b981;
  color: white;
}

.save-button:hover:not(:disabled) {
  background: #059669;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.save-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Delete Confirmation Modal */
.vac-plan-modal-content.delete-modal {
  max-width: 480px;
  width: 90%;
}

.delete-info {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 12px;
  margin: 12px 0;
  color: #dc2626;
  font-weight: 500;
  font-size: 0.875rem;
}

.warning-text {
  color: #dc2626;
  font-weight: 500;
  margin-top: 12px;
  text-align: center;
  font-style: italic;
  font-size: 0.875rem;
}

.delete-confirm-button {
  background: #ef4444;
  color: white;
}

.delete-confirm-button:hover:not(:disabled) {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.delete-confirm-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Table Layout Styles */
.admin-plans-table-container {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  overflow-x: auto; /* Thêm thanh cuộn ngang */
  overflow-y: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  margin-top: 24px;
  /* Styling cho thanh cuộn */
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

/* Webkit scrollbar styling */
.admin-plans-table-container::-webkit-scrollbar {
  height: 8px;
}

.admin-plans-table-container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.admin-plans-table-container::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.admin-plans-table-container::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.admin-plans-table {
  width: 100%;
  min-width: 1200px; /* Đảm bảo table có độ rộng tối thiểu */
  border-collapse: collapse;
  font-size: 0.9rem;
  background: transparent;
  color: #374151 !important;
}

.admin-plans-table thead {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-bottom: none;
}

.admin-plans-table th {
  padding: 20px 16px;
  text-align: center;
  font-weight: 600;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-plans-table th:last-child {
  border-right: none;
}

.admin-plans-table td {
  padding: 16px;
  border-bottom: 1px solid #f1f5f9;
  border-right: 1px solid #f1f5f9;
  vertical-align: middle;
  background: #ffffff;
  transition: all 0.2s ease;
}

.admin-plans-table td:last-child {
  border-right: none;
}

.admin-plans-table tbody tr:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.admin-plans-table tbody tr:hover td {
  background: transparent;
}

.admin-plans-table tbody tr:last-child td {
  border-bottom: none;
}

/* Cell Specific Styles */
.admin-plan-id-cell {
  font-weight: 700;
  color: #000000;
  /* background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); */
  width: 80px;
  text-align: center;
  border-radius: 8px;
  padding: 8px 12px !important;
  margin: 4px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.admin-plan-name-cell {
  min-width: 280px;
  max-width: 380px;
  font-weight: 500;
}

.admin-name-with-icon {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 4px 0;
}

.admin-name-with-icon .admin-vaccine-icon {
  color: #10b981;
  font-size: 1.2rem;
  flex-shrink: 0;
  background: #d1fae5;
  padding: 8px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.admin-name-with-icon span {
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  line-height: 1.5;
  font-weight: 500;
  color: #1e293b;
}

.admin-date-cell, .admin-deadline-cell {
  width: 140px;
  font-size: 0.875rem;
  color: #475569;
  font-weight: 500;
  text-align: center;
}

.admin-status-cell {
  width: 200px;
  position: relative;
  text-align: center;
}

.admin-status-select {
  width: 100%;
  padding: 10px 14px;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  background: white;
  min-width: 180px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-status-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.admin-status-select.admin-status-waiting {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #92400e;
  border-color: #f59e0b;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.2);
}

.admin-status-select.admin-status-progress {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1e40af;
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.admin-status-select.admin-status-completed {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  color: #047857;
  border-color: #10b981;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
}

.admin-status-select.admin-status-canceled {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  color: #b91c1c;
  border-color: #ef4444;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
}

.admin-status-select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.admin-status-spinner {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.9rem;
  color: #3b82f6;
}

.admin-time-status-cell {
  width: 140px;
  text-align: center;
}

.admin-time-status-badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 6px 12px;
  border-radius: 16px;
  border: 2px solid;
  white-space: nowrap;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.admin-description-cell {
  max-width: 320px;
  min-width: 220px;
}

.admin-description-text {
  line-height: 1.5;
  color: #475569;
  font-size: 0.875rem;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  max-height: none;
  font-weight: 400;
  padding: 4px 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .plans-grid {
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 25px;
  }
}

@media (max-width: 768px) {
  .vaccination-plan-history {
    padding: 15px;
  }

  .history-header {
    padding: 25px 20px;
    margin-bottom: 30px;
  }

  .history-header h2 {
    font-size: 2rem;
  }

  .statistics-row {
    grid-template-columns: repeat(5, 1fr);
    gap: 8px;
    margin-bottom: 30px;
  }

  .stat-card {
    padding: 16px 12px;
  }

  .stat-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
  }

  .stat-number {
    font-size: 1.8rem;
  }

  .history-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 20px;
    padding: 20px;
  }

  .search-filter-group {
    flex-direction: column;
    gap: 15px;
    min-width: auto;
  }

  .search-box {
    max-width: none;
  }

  .filter-dropdown {
    min-width: auto;
  }

  .plans-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .plan-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .plan-dates {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .vac-plan-modal-content.detail-modal {
    margin: 10px;
    max-height: 95vh;
  }

  .vac-plan-modal-header,
  .vac-plan-modal-body {
    padding: 20px;
  }

  .detail-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .plans-table {
    font-size: 0.8rem;
  }
  
  .plans-table th,
  .plans-table td {
    padding: 8px 6px;
  }
  
  .description-cell {
    max-width: 150px;
  }

  .plans-table-container {
    overflow-x: auto;
  }
  
  .plans-table {
    min-width: 800px;
  }

  .pagination-container {
    flex-direction: column;
    gap: 12px;
    padding: 15px 20px;
  }

  .pagination-info {
    text-align: center;
    font-size: 0.8rem;
  }

  .pagination-controls {
    justify-content: center;
  }

  .pagination-pages {
    margin: 0 8px;
  }

  .pagination-btn,
  .pagination-page {
    width: 32px;
    height: 32px;
    font-size: 0.8rem;
  }
}